"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useGetAllStokvelGroupsQuery } from "@/lib/redux/features/groups/groupsApiSlice";
import { useAuth } from "@/context/AuthContext";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { JoinGroupModal } from "@/components/groups/JoinGroupModal";
import { 
  Users, 
  MapPin, 
  TrendingUp, 
  Search, 
  Filter,
  Star,
  DollarSign,
  ArrowRight,
  UserPlus,
  CheckCircle,
  LogIn,
  Zap,
  Eye,
  Heart,
  Share2,
  Bookmark,
  Clock,
  ExternalLink
} from "lucide-react";
import type { StokvelGroup } from "@/types/stokvelgroup";
import { toast } from "sonner";

export default function ProfessionalGroupsPage() {
  const { user } = useAuth();
  const router = useRouter();
  const { data: groups = [], isLoading, error, refetch } = useGetAllStokvelGroupsQuery();
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredGroups, setFilteredGroups] = useState<StokvelGroup[]>([]);
  const [selectedGroup, setSelectedGroup] = useState<StokvelGroup | null>(null);
  const [isJoinModalOpen, setIsJoinModalOpen] = useState(false);
  const [membershipStatus, setMembershipStatus] = useState<any>(null);
  const [membershipLoading, setMembershipLoading] = useState(false);
  const [likedGroups, setLikedGroups] = useState<string[]>([]);
  const [bookmarkedGroups, setBookmarkedGroups] = useState<string[]>([]);

  // Fetch user's membership status
  useEffect(() => {
    if (user) {
      fetchMembershipStatus();
    }
  }, [user]);

  const fetchMembershipStatus = async () => {
    if (!user) return;
    
    setMembershipLoading(true);
    try {
      const response = await fetch(`/api/users/${user._id}/group-membership-status`);
      const data = await response.json();
      
      if (data.success) {
        setMembershipStatus(data.data);
      }
    } catch (error) {
      console.error('Error fetching membership status:', error);
    } finally {
      setMembershipLoading(false);
    }
  };

  // Filter and sort groups based on search query and membership status
  useEffect(() => {
    let filtered = groups;
    
    // Apply search filter
    if (searchQuery.trim()) {
      filtered = groups.filter(group =>
        group.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        group.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        group.geolocation?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Sort groups: user's current groups first, then others
    if (membershipStatus && user) {
      const userGroupIds = membershipStatus.activeGroups.map((g: any) => g._id);
      
      const sortedGroups = [...filtered].sort((a, b) => {
        const aIsUserGroup = userGroupIds.includes(a._id);
        const bIsUserGroup = userGroupIds.includes(b._id);
        
        // User's groups come first
        if (aIsUserGroup && !bIsUserGroup) return -1;
        if (!aIsUserGroup && bIsUserGroup) return 1;
        
        // Within each category, sort alphabetically
        return a.name.localeCompare(b.name);
      });
      
      setFilteredGroups(sortedGroups);
    } else {
      setFilteredGroups(filtered);
    }
  }, [groups, searchQuery, membershipStatus, user]);

  const handleJoinGroup = (groupId: string) => {
    const group = groups.find(g => g._id === groupId);
    if (group) {
      setSelectedGroup(group);
      setIsJoinModalOpen(true);
    }
  };

  const handleGoToGroup = (groupId: string) => {
    router.push(`/group/${groupId}`);
  };

  const handleJoinSuccess = () => {
    refetch();
    fetchMembershipStatus();
    toast.success("Successfully joined the group!");
  };

  const toggleLike = (groupId: string) => {
    setLikedGroups((prev) => 
      prev.includes(groupId) 
        ? prev.filter((id) => id !== groupId) 
        : [...prev, groupId]
    );
  };

  const toggleBookmark = (groupId: string) => {
    setBookmarkedGroups((prev) => 
      prev.includes(groupId) 
        ? prev.filter((id) => id !== groupId) 
        : [...prev, groupId]
    );
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="container mx-auto px-4 py-8">
          <div className="space-y-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <Card key={i} className="glass">
                <CardHeader>
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gray-200 rounded-full animate-pulse" />
                    <div className="space-y-2">
                      <div className="h-4 bg-gray-200 rounded w-48 animate-pulse" />
                      <div className="h-3 bg-gray-200 rounded w-32 animate-pulse" />
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="h-4 bg-gray-200 rounded w-full animate-pulse" />
                  <div className="grid grid-cols-3 gap-4">
                    <div className="h-16 bg-gray-200 rounded animate-pulse" />
                    <div className="h-16 bg-gray-200 rounded animate-pulse" />
                    <div className="h-16 bg-gray-200 rounded animate-pulse" />
                  </div>
                  <div className="h-10 bg-gray-200 rounded w-full animate-pulse" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
        <Card className="max-w-md mx-auto">
          <CardContent className="text-center py-8">
            <div className="text-red-500 mb-4">
              <Users className="h-12 w-12 mx-auto" />
            </div>
            <h3 className="text-lg font-semibold mb-2">Failed to Load Groups</h3>
            <p className="text-gray-600 mb-4">
              There was an error loading the stokvel groups. Please try again.
            </p>
            <Button onClick={() => refetch()}>
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Professional Header Section */}
      <section className="relative pt-20 pb-12 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-[#2A7C6C]/10 via-background to-[#2A7C6C]/5" />
        <div className="absolute inset-0 opacity-10 bg-[radial-gradient(circle_at_20%_30%,_hsl(var(--primary))_3px,_transparent_3px),_radial-gradient(circle_at_80%_70%,_hsl(var(--primary))_2px,_transparent_2px)] bg-[length:80px_80px,_120px_120px]" />
        
        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="max-w-6xl mx-auto"
          >
            {/* Header Content */}
            <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-8 mb-8">
              <div className="flex-1">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                  className="mb-6"
                >
                  <span className="inline-flex items-center px-4 py-2 rounded-full bg-[#2A7C6C]/10 text-[#2A7C6C] text-sm font-medium mb-4">
                    <div className="w-2 h-2 bg-[#2A7C6C] rounded-full mr-2 animate-pulse" />
                    Live Groups
                  </span>
                  <h1 
                    className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4"
                    style={{
                      fontFamily: "ClashDisplay-Variable, sans-serif",
                      letterSpacing: "-0.02em",
                    }}
                  >
                    Stokvel <span className="bg-gradient-to-r from-[#2A7C6C] to-[#7FDBCA] bg-clip-text text-transparent">Groups</span>
                  </h1>
                  <p className="text-xl text-gray-600 max-w-2xl leading-relaxed">
                    Join local savings groups and unlock the power of collective buying in your community
                  </p>
                </motion.div>
                
                {/* User Status Display - Professional Text Only */}
                {user && membershipStatus?.hasActiveGroup && (
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.3 }}
                    className="flex items-center text-sm bg-green-50 px-4 py-3 rounded-lg border border-green-200 inline-flex"
                  >
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-green-500 rounded-full mr-3 animate-pulse"></div>
                      <span className="font-medium text-gray-900">{user.name}</span>
                      <span className="mx-2 text-gray-400">•</span>
                      <span className="text-gray-600">Active member of</span>
                      <span className="ml-1 font-semibold text-[#2A7C6C]">
                        {membershipStatus.activeGroups[0]?.name}
                      </span>
                    </div>
                  </motion.div>
                )}
              </div>
              
              {/* Search Section */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2 }}
                className="lg:w-96"
              >
                <div className="bg-white/95 backdrop-blur-md border border-gray-200 rounded-xl p-4 shadow-lg">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                    <Input
                      type="text"
                      placeholder="Search groups..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10 h-12 border-gray-200 focus:border-[#2A7C6C] focus:ring-[#2A7C6C] rounded-lg bg-white"
                    />
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <Button variant="ghost" size="sm" className="text-gray-400 h-6 w-6 p-0">
                        <Filter className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Groups Feed Section */}
      <section className="pb-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            {filteredGroups.length === 0 ? (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-center py-16"
              >
                <Search className="h-16 w-16 mx-auto text-gray-400 mb-6" />
                <h3 className="text-2xl font-bold text-gray-900 mb-4">No Groups Found</h3>
                <p className="text-gray-600 max-w-md mx-auto">
                  {searchQuery.trim() 
                    ? "Try adjusting your search terms or browse all available groups."
                    : "There are no groups available at the moment."
                  }
                </p>
                {searchQuery.trim() && (
                  <Button 
                    variant="outline" 
                    onClick={() => setSearchQuery("")}
                    className="mt-6"
                  >
                    Clear Search
                  </Button>
                )}
              </motion.div>
            ) : (
              <div className="space-y-6">
                <AnimatePresence>
                  {filteredGroups.map((group, index) => (
                    <GroupCard
                      key={group._id}
                      group={group}
                      index={index}
                      onJoin={handleJoinGroup}
                      onGoToGroup={handleGoToGroup}
                      currentUser={user}
                      membershipStatus={membershipStatus}
                      likedGroups={likedGroups}
                      bookmarkedGroups={bookmarkedGroups}
                      onToggleLike={toggleLike}
                      onToggleBookmark={toggleBookmark}
                    />
                  ))}
                </AnimatePresence>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Join Group Modal */}
      <JoinGroupModal
        isOpen={isJoinModalOpen}
        onClose={() => setIsJoinModalOpen(false)}
        group={selectedGroup}
        onSuccess={handleJoinSuccess}
      />
    </div>
  );
}

interface GroupCardProps {
  group: StokvelGroup;
  index: number;
  onJoin: (groupId: string) => void;
  onGoToGroup: (groupId: string) => void;
  currentUser: any;
  membershipStatus: any;
  likedGroups: string[];
  bookmarkedGroups: string[];
  onToggleLike: (groupId: string) => void;
  onToggleBookmark: (groupId: string) => void;
}

function GroupCard({
  group,
  index,
  onJoin,
  onGoToGroup,
  currentUser,
  membershipStatus,
  likedGroups,
  bookmarkedGroups,
  onToggleLike,
  onToggleBookmark
}: GroupCardProps) {
  // Check if user is a member of this specific group
  const isUserMember = currentUser && group.members.includes(currentUser._id);

  // Check if user has any active groups (for relocation logic)
  const hasActiveGroups = membershipStatus?.hasActiveGroup || false;
  const userActiveGroupIds = membershipStatus?.activeGroups?.map((g: any) => g._id) || [];
  const isCurrentGroup = userActiveGroupIds.includes(group._id);

  // Determine the action type
  const getActionType = () => {
    if (!currentUser) return 'login';
    if (isUserMember) return 'member';
    if (hasActiveGroups && !isCurrentGroup) return 'relocate';
    return 'join';
  };

  const actionType = getActionType();

  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -50 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      whileHover={{ scale: 1.02 }}
      className="relative"
    >
      <Card
        className={`glass hover:shadow-xl transition-all duration-300 ${
          isCurrentGroup
            ? "border-green-200 bg-green-50/50 ring-2 ring-green-200/50"
            : "border-gray-200/50 bg-white/80"
        }`}
      >
        {isCurrentGroup && (
          <div className="absolute -top-2 -right-2">
            <Badge className="bg-green-500 text-white">
              <CheckCircle className="w-3 h-3 mr-1" />
              Current
            </Badge>
          </div>
        )}

        <CardHeader className="pb-4">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-4 flex-1">
              <Avatar className="w-16 h-16 border-2 border-gray-200">
                <AvatarImage src="/StokvelLogo.avif" alt={group.name} />
                <AvatarFallback className="bg-[#2A7C6C] text-white text-lg font-bold">
                  {group.name.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <h3 className={`text-2xl font-bold transition-colors duration-200 cursor-pointer mb-2 ${
                  isCurrentGroup ? 'text-green-700' : 'text-gray-900 hover:text-[#2A7C6C]'
                }`}>
                  {group.name}
                </h3>
                <p className="text-gray-600 text-base mb-3 line-clamp-2">
                  {group.description}
                </p>
                <div className="flex items-center space-x-6 text-sm text-gray-500">
                  <div className="flex items-center space-x-1">
                    <Clock className="w-4 h-4" />
                    <span>Active</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Users className="w-4 h-4" />
                    <span>{group.members.length} members</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Eye className="w-4 h-4" />
                    <span>{Math.floor(Math.random() * 100) + 50} views</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-2 ml-4">
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => onToggleLike(group._id)}
                className={`p-2 rounded-full transition-colors duration-200 ${
                  likedGroups.includes(group._id)
                    ? "bg-red-100 text-red-500"
                    : "bg-gray-100 hover:bg-gray-200 text-gray-600"
                }`}
              >
                <Heart className={`w-4 h-4 ${likedGroups.includes(group._id) ? "fill-current" : ""}`} />
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => onToggleBookmark(group._id)}
                className={`p-2 rounded-full transition-colors duration-200 ${
                  bookmarkedGroups.includes(group._id)
                    ? "bg-[#2A7C6C]/10 text-[#2A7C6C]"
                    : "bg-gray-100 hover:bg-gray-200 text-gray-600"
                }`}
              >
                <Bookmark className={`w-4 h-4 ${bookmarkedGroups.includes(group._id) ? "fill-current" : ""}`} />
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 text-gray-600 transition-colors duration-200"
              >
                <Share2 className="w-4 h-4" />
              </motion.button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Group Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
              <MapPin className="w-5 h-5 text-[#2A7C6C]" />
              <div>
                <p className="text-sm text-gray-600">Location</p>
                <p className="font-semibold text-gray-900">{group.geolocation || "Not specified"}</p>
              </div>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
              <DollarSign className="w-5 h-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Total Sales</p>
                <p className="font-semibold text-gray-900">R{group.totalSales?.toLocaleString() || '0'}</p>
              </div>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
              <TrendingUp className="w-5 h-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Avg Order</p>
                <p className="font-semibold text-gray-900">R{group.avgOrderValue?.toLocaleString() || '0'}</p>
              </div>
            </div>
          </div>

          {/* Action Section */}
          <div className="flex items-center justify-between pt-4 border-t border-gray-200">
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-600">
                Match Score: <span className="font-semibold text-[#2A7C6C]">
                  {Math.floor(Math.random() * 20) + 80}%
                </span>
              </div>
              {isCurrentGroup && (
                <Badge className="bg-green-100 text-green-700 border-green-300">
                  Your Group
                </Badge>
              )}
            </div>

            <Button
              onClick={() => {
                if (actionType === 'member' && isCurrentGroup) {
                  onGoToGroup(group._id);
                } else {
                  onJoin(group._id);
                }
              }}
              disabled={actionType === 'member' && !isCurrentGroup}
              className={`transition-all duration-300 ${
                actionType === 'member' && isCurrentGroup
                  ? "bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-lg hover:shadow-xl"
                  : actionType === 'member'
                  ? "bg-gray-100 text-gray-500 cursor-not-allowed"
                  : actionType === 'relocate'
                  ? "bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white shadow-lg hover:shadow-xl"
                  : actionType === 'login'
                  ? "bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-lg hover:shadow-xl"
                  : "bg-gradient-to-r from-[#2A7C6C] to-[#1E5A4F] hover:from-[#1E5A4F] hover:to-[#2A7C6C] text-white shadow-lg hover:shadow-xl"
              }`}
            >
              {actionType === 'member' ? (
                <>
                  {isCurrentGroup ? (
                    <>
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Go to Group
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Member
                    </>
                  )}
                </>
              ) : actionType === 'relocate' ? (
                <>
                  <ArrowRight className="h-4 w-4 mr-2" />
                  Relocate to Group
                </>
              ) : actionType === 'login' ? (
                <>
                  <LogIn className="h-4 w-4 mr-2" />
                  Login to Join
                </>
              ) : (
                <>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Join Group
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
