"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useGetAllStokvelGroupsQuery } from "@/lib/redux/features/groups/groupsApiSlice";
import { useAuth } from "@/context/AuthContext";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Users,
  MapPin,
  TrendingUp,
  ShoppingCart,
  Search,
  Filter,
  Star,
  DollarSign,
  Package,
  ArrowRight,
  UserPlus,
  CheckCircle,
  LogIn,
  Clock,
  Eye,
  Heart,
  Bookmark,
  Share2,
  ExternalLink
} from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import type { StokvelGroup } from "@/types/stokvelgroup";
import { JoinGroupModal } from "@/components/groups/JoinGroupModal";

export default function GroupsPage() {
  const { user } = useAuth();
  const router = useRouter();
  const { data: groups = [], isLoading, error, refetch } = useGetAllStokvelGroupsQuery();
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredGroups, setFilteredGroups] = useState<StokvelGroup[]>([]);
  const [selectedGroup, setSelectedGroup] = useState<StokvelGroup | null>(null);
  const [isJoinModalOpen, setIsJoinModalOpen] = useState(false);
  const [membershipStatus, setMembershipStatus] = useState<any>(null);
  const [membershipLoading, setMembershipLoading] = useState(false);

  // Fetch user's membership status
  useEffect(() => {
    if (user) {
      fetchMembershipStatus();
    }
  }, [user]);

  const fetchMembershipStatus = async () => {
    if (!user) return;

    setMembershipLoading(true);
    try {
      const response = await fetch(`/api/users/${user._id}/group-membership-status`);
      const data = await response.json();

      if (data.success) {
        setMembershipStatus(data.data);
      }
    } catch (error) {
      console.error('Error fetching membership status:', error);
    } finally {
      setMembershipLoading(false);
    }
  };

  // Enhanced search function for comprehensive filtering
  const performAdvancedSearch = (groups: StokvelGroup[], query: string) => {
    if (!query.trim()) return groups;

    const searchTerm = query.toLowerCase().trim();

    return groups.filter(group => {
      // Search in group name
      const nameMatch = group.name.toLowerCase().includes(searchTerm);

      // Search in group description
      const descriptionMatch = group.description.toLowerCase().includes(searchTerm);

      // Search in full geolocation
      const locationMatch = group.geolocation?.toLowerCase().includes(searchTerm) || false;

      // Extract and search in province
      const province = extractProvince(group.geolocation || "");
      const provinceMatch = province.toLowerCase().includes(searchTerm);

      // Extract and search in city/township
      const cityTownshipMatch = extractCityTownship(group.geolocation || "", searchTerm);

      // Search in specific location components (city, suburb, township)
      const locationComponents = group.geolocation?.toLowerCase().split(/[,\s]+/) || [];
      const componentMatch = locationComponents.some(component =>
        component.includes(searchTerm)
      );

      return nameMatch || descriptionMatch || locationMatch || provinceMatch || cityTownshipMatch || componentMatch;
    });
  };

  // Extract city/township from location string
  const extractCityTownship = (location: string, searchTerm: string): boolean => {
    if (!location) return false;

    const locationLower = location.toLowerCase();

    // Common South African townships and suburbs
    const townships = [
      'soweto', 'alexandra', 'diepsloot', 'orange farm', 'tembisa', 'katlehong',
      'tokoza', 'vosloorus', 'thokoza', 'daveyton', 'duduza', 'kwa-thema',
      'sebokeng', 'evaton', 'sharpeville', 'boipatong', 'bophelong',
      'mamelodi', 'atteridgeville', 'soshanguve', 'hammanskraal', 'ga-rankuwa',
      'mabopane', 'winterveld', 'temba', 'rosslyn', 'akasia', 'montana',
      'gugulethu', 'langa', 'nyanga', 'khayelitsha', 'mitchells plain',
      'manenberg', 'hanover park', 'elsies river', 'delft', 'philippi',
      'umlazi', 'chatsworth', 'phoenix', 'inanda', 'ntuzuma', 'kwamashu',
      'lamontville', 'chesterville', 'cato manor', 'pinetown', 'westville'
    ];

    // Check if search term matches any known township
    const townshipMatch = townships.some(township =>
      township.includes(searchTerm) || locationLower.includes(township)
    );

    // Check for general location terms
    const locationTerms = locationLower.split(/[,\s-]+/).filter(term => term.length > 2);
    const termMatch = locationTerms.some(term => term.includes(searchTerm));

    return townshipMatch || termMatch;
  };

  // Filter and sort groups based on search query and membership status
  useEffect(() => {
    // Apply advanced search filter
    let filtered = performAdvancedSearch(groups, searchQuery);

    // Sort groups: user's current groups first, then others
    if (membershipStatus && user) {
      const userGroupIds = membershipStatus.activeGroups.map((g: any) => g._id);

      const sortedGroups = [...filtered].sort((a, b) => {
        const aIsUserGroup = userGroupIds.includes(a._id);
        const bIsUserGroup = userGroupIds.includes(b._id);

        // User's groups come first
        if (aIsUserGroup && !bIsUserGroup) return -1;
        if (!aIsUserGroup && bIsUserGroup) return 1;

        // Within each category, sort alphabetically
        return a.name.localeCompare(b.name);
      });

      setFilteredGroups(sortedGroups);
    } else {
      setFilteredGroups(filtered);
    }
  }, [groups, searchQuery, membershipStatus, user]);

  const handleJoinGroup = (groupId: string) => {
    const group = groups.find(g => g._id === groupId);
    if (group) {
      setSelectedGroup(group);
      setIsJoinModalOpen(true);
    }
  };

  const handleGoToGroup = (groupId: string) => {
    router.push(`/group/${groupId}`);
  };

  const handleJoinSuccess = () => {
    refetch(); // Refresh the groups data
    setIsJoinModalOpen(false);
    setSelectedGroup(null);
  };

  // Group categorization by location
  const categorizeGroupsByLocation = () => {
    const categories: { [key: string]: StokvelGroup[] } = {};

    filteredGroups.forEach(group => {
      const location = group.geolocation || "Other Locations";
      const province = extractProvince(location);

      if (!categories[province]) {
        categories[province] = [];
      }
      categories[province].push(group);
    });

    return categories;
  };

  const extractProvince = (location: string): string => {
    const provinces = [
      'Gauteng', 'Western Cape', 'KwaZulu-Natal', 'Eastern Cape',
      'Limpopo', 'Mpumalanga', 'North West', 'Free State', 'Northern Cape'
    ];

    for (const province of provinces) {
      if (location.toLowerCase().includes(province.toLowerCase())) {
        return province;
      }
    }

    // Check for major cities and map to provinces
    const cityToProvince: { [key: string]: string } = {
      'johannesburg': 'Gauteng',
      'pretoria': 'Gauteng',
      'cape town': 'Western Cape',
      'durban': 'KwaZulu-Natal',
      'port elizabeth': 'Eastern Cape',
      'bloemfontein': 'Free State',
      'kimberley': 'Northern Cape',
      'polokwane': 'Limpopo',
      'nelspruit': 'Mpumalanga',
      'mafikeng': 'North West'
    };

    for (const [city, province] of Object.entries(cityToProvince)) {
      if (location.toLowerCase().includes(city)) {
        return province;
      }
    }

    return "Other Locations";
  };

  // Get search context for user feedback
  const getSearchContext = () => {
    if (!searchQuery.trim()) return null;

    const searchTerm = searchQuery.toLowerCase().trim();

    // Check if searching for a province
    const provinces = [
      'Gauteng', 'Western Cape', 'KwaZulu-Natal', 'Eastern Cape',
      'Limpopo', 'Mpumalanga', 'North West', 'Free State', 'Northern Cape'
    ];

    const matchedProvince = provinces.find(province =>
      province.toLowerCase().includes(searchTerm) || searchTerm.includes(province.toLowerCase())
    );

    if (matchedProvince) {
      return { type: 'province', value: matchedProvince };
    }

    // Check if searching for a major city
    const cities = ['johannesburg', 'cape town', 'durban', 'pretoria', 'port elizabeth', 'bloemfontein'];
    const matchedCity = cities.find(city =>
      city.includes(searchTerm) || searchTerm.includes(city)
    );

    if (matchedCity) {
      return { type: 'city', value: matchedCity.charAt(0).toUpperCase() + matchedCity.slice(1) };
    }

    // Check if searching for a township
    const townships = ['soweto', 'alexandra', 'diepsloot', 'mamelodi', 'gugulethu', 'umlazi'];
    const matchedTownship = townships.find(township =>
      township.includes(searchTerm) || searchTerm.includes(township)
    );

    if (matchedTownship) {
      return { type: 'township', value: matchedTownship.charAt(0).toUpperCase() + matchedTownship.slice(1) };
    }

    return { type: 'general', value: searchQuery };
  };

  const renderGroupsByLocation = () => {
    const categorizedGroups = categorizeGroupsByLocation();
    const sortedProvinces = Object.keys(categorizedGroups).sort((a, b) => {
      // Prioritize user's current group province
      if (user && membershipStatus?.hasActiveGroup) {
        const userGroupLocation = membershipStatus.activeGroups[0]?.geolocation || "";
        const userProvince = extractProvince(userGroupLocation);
        if (a === userProvince) return -1;
        if (b === userProvince) return 1;
      }

      // Then sort alphabetically, but keep "Other Locations" last
      if (a === "Other Locations") return 1;
      if (b === "Other Locations") return -1;
      return a.localeCompare(b);
    });

    return sortedProvinces.map((province, provinceIndex) => (
      <motion.div
        key={province}
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: provinceIndex * 0.1 }}
        className="space-y-4"
      >
        {/* Province Header */}
        <div className="flex items-center space-x-3 mb-6">
          <div className="flex items-center space-x-2">
            <MapPin className="h-5 w-5 text-[#2A7C6C]" />
            <h2 className="text-xl font-bold text-gray-900">{province}</h2>
          </div>
          <div className="flex-1 h-px bg-gradient-to-r from-gray-300 to-transparent"></div>
          <Badge variant="outline" className="text-gray-600">
            {categorizedGroups[province].length} group{categorizedGroups[province].length !== 1 ? 's' : ''}
          </Badge>
        </div>

        {/* Groups in this province */}
        <div className="space-y-4">
          <AnimatePresence>
            {categorizedGroups[province].map((group, index) => (
              <GroupCard
                key={group._id}
                group={group}
                index={index}
                onJoin={handleJoinGroup}
                onGoToGroup={handleGoToGroup}
                currentUser={user}
                membershipStatus={membershipStatus}
              />
            ))}
          </AnimatePresence>
        </div>
      </motion.div>
    ));
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="container mx-auto px-4 py-8">
          <div className="space-y-8">
            {/* Header Skeleton */}
            <div className="text-center space-y-4">
              <Skeleton className="h-12 w-96 mx-auto" />
              <Skeleton className="h-6 w-[600px] mx-auto" />
            </div>
            
            {/* Search Skeleton */}
            <div className="max-w-2xl mx-auto">
              <Skeleton className="h-12 w-full" />
            </div>
            
            {/* Cards Skeleton */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Array.from({ length: 6 }).map((_, i) => (
                <Card key={i} className="h-80">
                  <CardHeader>
                    <Skeleton className="h-6 w-3/4" />
                    <Skeleton className="h-4 w-full" />
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <Skeleton className="h-4 w-1/2" />
                    <div className="grid grid-cols-2 gap-4">
                      <Skeleton className="h-16" />
                      <Skeleton className="h-16" />
                    </div>
                    <Skeleton className="h-10 w-full" />
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
        <Card className="max-w-md mx-auto">
          <CardContent className="text-center py-8">
            <div className="text-red-500 mb-4">
              <Package className="h-12 w-12 mx-auto" />
            </div>
            <h3 className="text-lg font-semibold mb-2">Failed to Load Groups</h3>
            <p className="text-gray-600 mb-4">
              We couldn't load the available groups. Please try again later.
            </p>
            <Button onClick={() => window.location.reload()}>
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="container mx-auto px-4 py-8 max-w-5xl">
        {/* Professional Header Section */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6 mb-6">
            <div className="flex-1">
              <h1
                className="text-3xl lg:text-4xl font-bold text-gray-900 mb-3"
                style={{
                  fontFamily: "ClashDisplay-Variable, sans-serif",
                  letterSpacing: "-0.02em",
                }}
              >
                Stokvel Groups
              </h1>
              <p className="text-lg text-gray-600 max-w-2xl mb-4">
                Discover and join local savings groups in your area
              </p>

              {/* User Status Display - Professional Text Only */}
              {user && membershipStatus?.hasActiveGroup && (
                <div className="inline-flex items-center text-sm text-gray-700 bg-green-50 px-4 py-2 rounded-lg border border-green-200">
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                    <span className="font-medium text-gray-900">{user.name}</span>
                    <span className="mx-2 text-gray-400">•</span>
                    <span className="text-gray-600">Member of</span>
                    <span className="ml-1 font-semibold text-[#2A7C6C]">
                      {membershipStatus.activeGroups[0]?.name}
                    </span>
                  </div>
                </div>
              )}
            </div>

            {/* Search Section */}
            <div className="lg:w-96">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <Input
                  type="text"
                  placeholder="Search by group name, province, city, or township..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 h-12 border-gray-200 focus:border-[#2A7C6C] focus:ring-[#2A7C6C] rounded-lg bg-white shadow-sm"
                />
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <Button variant="ghost" size="sm" className="text-gray-400 h-6 w-6 p-0">
                    <Filter className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Search Examples */}
              {searchQuery.length === 0 && (
                <div className="mt-3 text-xs text-gray-500">
                  <span className="font-medium">Try searching:</span>
                  <span className="ml-1">
                    "Gauteng", "Soweto", "Cape Town", "Durban", or group names
                  </span>
                </div>
              )}
            </div>
          </div>
        </motion.div>

        {/* Stats Overview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12"
        >
          <Card className="bg-gradient-to-r from-[#2A7C6C] to-[#1E5A4F] text-white">
            <CardContent className="p-6 text-center">
              <Users className="h-8 w-8 mx-auto mb-2" />
              <div className="text-2xl font-bold">{groups.length}</div>
              <div className="text-sm opacity-90">Active Groups</div>
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-to-r from-purple-600 to-purple-700 text-white">
            <CardContent className="p-6 text-center">
              <TrendingUp className="h-8 w-8 mx-auto mb-2" />
              <div className="text-2xl font-bold">
                {groups.reduce((sum, group) => sum + group.members.length, 0)}
              </div>
              <div className="text-sm opacity-90">Total Members</div>
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
            <CardContent className="p-6 text-center">
              <DollarSign className="h-8 w-8 mx-auto mb-2" />
              <div className="text-2xl font-bold">
                {formatCurrency(groups.reduce((sum, group) => sum + group.totalSales, 0))}
              </div>
              <div className="text-sm opacity-90">Total Sales</div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Groups Grid */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          {filteredGroups.length === 0 ? (
            <Card className="max-w-md mx-auto">
              <CardContent className="text-center py-12">
                <Search className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Groups Found</h3>
                <p className="text-gray-600">
                  {searchQuery.trim()
                    ? "Try adjusting your search terms or browse all available groups."
                    : "There are no groups available at the moment."
                  }
                </p>
                {searchQuery.trim() && (
                  <Button
                    variant="outline"
                    onClick={() => setSearchQuery("")}
                    className="mt-4"
                  >
                    Clear Search
                  </Button>
                )}
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-8">
              {/* Search Results Header */}
              {searchQuery.trim() && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-white/80 backdrop-blur-sm border border-gray-200 rounded-lg p-4 mb-6"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Search className="h-5 w-5 text-[#2A7C6C]" />
                      <div>
                        <h3 className="font-semibold text-gray-900">
                          Search Results
                          {(() => {
                            const context = getSearchContext();
                            if (context) {
                              switch (context.type) {
                                case 'province':
                                  return ` in ${context.value}`;
                                case 'city':
                                  return ` in ${context.value}`;
                                case 'township':
                                  return ` in ${context.value}`;
                                default:
                                  return ` for "${context.value}"`;
                              }
                            }
                            return '';
                          })()}
                        </h3>
                        <p className="text-sm text-gray-600">
                          Found {filteredGroups.length} group{filteredGroups.length !== 1 ? 's' : ''} matching your search
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSearchQuery("")}
                      className="text-gray-600 hover:text-gray-900"
                    >
                      Clear Search
                    </Button>
                  </div>
                </motion.div>
              )}

              {renderGroupsByLocation()}
            </div>
          )}
        </motion.div>

        {/* Join Group Modal */}
        <JoinGroupModal
          isOpen={isJoinModalOpen}
          onClose={() => {
            setIsJoinModalOpen(false);
            setSelectedGroup(null);
          }}
          group={selectedGroup}
          onSuccess={handleJoinSuccess}
        />
      </div>
    </div>
  );
}

interface GroupCardProps {
  group: StokvelGroup;
  index: number;
  onJoin: (groupId: string) => void;
  onGoToGroup: (groupId: string) => void;
  currentUser: any;
  membershipStatus: any;
}

function GroupCard({ group, index, onJoin, onGoToGroup, currentUser, membershipStatus }: GroupCardProps) {
  // Check if user is a member of this specific group
  const isUserMember = currentUser && group.members.includes(currentUser._id);

  // Check if user has any active groups (for relocation logic)
  const hasActiveGroups = membershipStatus?.hasActiveGroup || false;
  const userActiveGroupIds = membershipStatus?.activeGroups?.map((g: any) => g._id) || [];
  const isCurrentGroup = userActiveGroupIds.includes(group._id);

  // Determine the action type
  const getActionType = () => {
    if (!currentUser) return 'login';
    if (isUserMember) return 'member';
    if (hasActiveGroups && !isCurrentGroup) return 'relocate';
    return 'join';
  };

  const actionType = getActionType();
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -50 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      whileHover={{ scale: 1.02 }}
      className="relative"
    >
      <Card
        className={`glass-subtle hover:shadow-md transition-all duration-300 ${
          isCurrentGroup
            ? "border-green-200 bg-green-50/30 ring-1 ring-green-200/30"
            : "border-gray-200/30 bg-white/90"
        }`}
      >
        {isCurrentGroup && (
          <div className="absolute -top-2 -right-2">
            <Badge className="bg-green-500 text-white">
              <CheckCircle className="w-3 h-3 mr-1" />
              Current
            </Badge>
          </div>
        )}

        <CardHeader className="pb-4">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-4 flex-1">
              <div className="w-16 h-16 bg-gradient-to-br from-[#2A7C6C] to-[#7FDBCA] rounded-full flex items-center justify-center text-white text-xl font-bold shadow-lg">
                {group.name.charAt(0)}
              </div>
              <div className="flex-1">
                <h3 className={`text-2xl font-bold transition-colors duration-200 cursor-pointer mb-2 ${
                  isCurrentGroup ? 'text-green-700' : 'text-gray-900 hover:text-[#2A7C6C]'
                }`}>
                  {group.name}
                </h3>
                <p className="text-gray-600 text-base mb-3 line-clamp-2">
                  {group.description}
                </p>
                <div className="flex items-center space-x-6 text-sm text-gray-500">
                  <div className="flex items-center space-x-1">
                    <Clock className="w-4 h-4" />
                    <span>Active</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Users className="w-4 h-4" />
                    <span>{group.members.length} members</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Eye className="w-4 h-4" />
                    <span>{Math.floor(Math.random() * 100) + 50} views</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-2 ml-4">
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 text-gray-600 transition-colors duration-200"
              >
                <Heart className="w-4 h-4" />
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 text-gray-600 transition-colors duration-200"
              >
                <Bookmark className="w-4 h-4" />
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 text-gray-600 transition-colors duration-200"
              >
                <Share2 className="w-4 h-4" />
              </motion.button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Group Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
              <MapPin className="w-5 h-5 text-[#2A7C6C]" />
              <div>
                <p className="text-sm text-gray-600">Location</p>
                <p className="font-semibold text-gray-900">{group.geolocation || "Not specified"}</p>
              </div>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
              <DollarSign className="w-5 h-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Total Sales</p>
                <p className="font-semibold text-gray-900">R{group.totalSales?.toLocaleString() || '0'}</p>
              </div>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
              <TrendingUp className="w-5 h-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Avg Order</p>
                <p className="font-semibold text-gray-900">R{group.avgOrderValue?.toLocaleString() || '0'}</p>
              </div>
            </div>
          </div>

          {/* Action Section */}
          <div className="flex items-center justify-between pt-4 border-t border-gray-200">
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-600">
                Match Score: <span className="font-semibold text-[#2A7C6C]">
                  {Math.floor(Math.random() * 20) + 80}%
                </span>
              </div>
              {isCurrentGroup && (
                <Badge className="bg-green-100 text-green-700 border-green-300">
                  Your Group
                </Badge>
              )}
            </div>

            <Button
              onClick={() => {
                if (actionType === 'member' && isCurrentGroup) {
                  onGoToGroup(group._id);
                } else {
                  onJoin(group._id);
                }
              }}
              disabled={actionType === 'member' && !isCurrentGroup}
              className={`transition-all duration-300 ${
                actionType === 'member' && isCurrentGroup
                  ? "bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-lg hover:shadow-xl"
                  : actionType === 'member'
                  ? "bg-gray-100 text-gray-500 cursor-not-allowed"
                  : actionType === 'relocate'
                  ? "bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white shadow-lg hover:shadow-xl"
                  : actionType === 'login'
                  ? "bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-lg hover:shadow-xl"
                  : "bg-gradient-to-r from-[#2A7C6C] to-[#1E5A4F] hover:from-[#1E5A4F] hover:to-[#2A7C6C] text-white shadow-lg hover:shadow-xl"
              }`}
            >
              {actionType === 'member' ? (
                <>
                  {isCurrentGroup ? (
                    <>
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Go to Group
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Member
                    </>
                  )}
                </>
              ) : actionType === 'relocate' ? (
                <>
                  <ArrowRight className="h-4 w-4 mr-2" />
                  Relocate to Group
                </>
              ) : actionType === 'login' ? (
                <>
                  <LogIn className="h-4 w-4 mr-2" />
                  Login to Join
                </>
              ) : (
                <>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Join Group
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
