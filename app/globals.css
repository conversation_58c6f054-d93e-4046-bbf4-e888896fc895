/* @tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --primary: 167 49% 32%;
    --primary-foreground: 210 40% 98%;
  }
}

body {
  font-family: 'Avenir', sans-serif;
}

.text-primary {
  color: #2A7C6C;
}

.bg-primary {
  background-color: #2A7C6C;
}
 */

 @tailwind base;
 @tailwind components;
 @tailwind utilities;

 @layer base {
   :root {
     --background: 0 0% 100%;
     --foreground: 222.2 84% 4.9%;
     --primary: 167 49% 32%;
     --primary-foreground: 210 40% 98%;
     --card: 0 0% 100%;
     --card-foreground: 222.2 84% 4.9%;
     --popover: 0 0% 100%;
     --popover-foreground: 222.2 84% 4.9%;
     --secondary: 210 40% 96%;
     --secondary-foreground: 222.2 84% 4.9%;
     --muted: 210 40% 96%;
     --muted-foreground: 215.4 16.3% 46.9%;
     --accent: 210 40% 96%;
     --accent-foreground: 222.2 84% 4.9%;
     --destructive: 0 84.2% 60.2%;
     --destructive-foreground: 210 40% 98%;
     --border: 214.3 31.8% 91.4%;
     --input: 214.3 31.8% 91.4%;
     --ring: 167 49% 32%;
     --radius: 0.5rem;
   }
 }

 @font-face {
   font-family: 'ClashDisplay-Variable';
   src: url('/fonts/ClashDisplay-Variable.woff2') format('woff2');
   font-weight: 200 700;
   font-display: swap;
   font-style: normal;
 }

 body {
   font-family: 'Avenir', sans-serif;
 }

 .text-primary {
   color: #2A7C6C;
 }

 .bg-primary {
   background-color: #2A7C6C;
 }

 /* Glass effect for professional cards */
 .glass {
   background: rgba(255, 255, 255, 0.95);
   backdrop-filter: blur(10px);
   -webkit-backdrop-filter: blur(10px);
   border: 1px solid rgba(255, 255, 255, 0.2);
   box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
 }

 /* Subtle glass effect for professional, minimal look */
 .glass-subtle {
   background: rgba(255, 255, 255, 0.98);
   backdrop-filter: blur(8px);
   -webkit-backdrop-filter: blur(8px);
   border: 1px solid rgba(255, 255, 255, 0.3);
   box-shadow: 0 2px 8px 0 rgba(31, 38, 135, 0.08);
 }

 .scrollbar-hide::-webkit-scrollbar {
   display: none;
 }

 /* Mobile Navigation Animations */
 @keyframes slideInFromLeft {
   from {
     opacity: 0;
     transform: translateX(-20px);
   }
   to {
     opacity: 1;
     transform: translateX(0);
   }
 }

 /* Ensure mobile menu is always on top */
 .mobile-menu-overlay {
   position: fixed !important;
   top: 0 !important;
   left: 0 !important;
   right: 0 !important;
   bottom: 0 !important;
   z-index: 9999 !important;
 }

 .mobile-menu-panel {
   position: fixed !important;
   top: 0 !important;
   left: 0 !important;
   height: 100vh !important;
   height: 100dvh !important; /* Dynamic viewport height for mobile */
   z-index: 10000 !important;
 }

 /* Line clamp utilities for text truncation */
 .line-clamp-1 {
   overflow: hidden;
   display: -webkit-box;
   -webkit-box-orient: vertical;
   -webkit-line-clamp: 1;
 }

 .line-clamp-2 {
   overflow: hidden;
   display: -webkit-box;
   -webkit-box-orient: vertical;
   -webkit-line-clamp: 2;
 }

 .line-clamp-3 {
   overflow: hidden;
   display: -webkit-box;
   -webkit-box-orient: vertical;
   -webkit-line-clamp: 3;
 }

