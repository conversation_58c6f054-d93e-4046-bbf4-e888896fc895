// app/api/customers/trends/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { getCustomerTrends } from '@/lib/backend/customers';
import { handleApiError } from '@/lib/backend/error-service';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '30d';
    
    const trends = await getCustomerTrends(period);
    
    return NextResponse.json(trends, { status: 200 });
  } catch (error) {
    return handleApiError(error, 'Failed to get customer trends');
  }
}
