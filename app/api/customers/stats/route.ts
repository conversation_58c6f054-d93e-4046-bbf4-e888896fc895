// app/api/customers/stats/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { getCustomersWithStats } from '@/lib/backend/customers';
import { handleApiError } from '@/lib/backend/error-service';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    
    const filters = {
      status: searchParams.get('status') || undefined,
      search: searchParams.get('search') || undefined,
    };

    // Remove undefined values
    Object.keys(filters).forEach(key => {
      if (filters[key as keyof typeof filters] === undefined) {
        delete filters[key as keyof typeof filters];
      }
    });

    const result = await getCustomersWithStats(page, limit, filters);
    
    return NextResponse.json(result, { status: 200 });
  } catch (error) {
    return handleApiError(error, 'Failed to get customer stats');
  }
}
