// app/api/customers/overview/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { getCustomerOverview } from '@/lib/backend/customers';
import { handleApiError } from '@/lib/backend/error-service';

export async function GET(request: NextRequest) {
  try {
    const overview = await getCustomerOverview();
    
    return NextResponse.json(overview, { status: 200 });
  } catch (error) {
    return handleApiError(error, 'Failed to get customer overview');
  }
}
