// app/api/customers/top/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { getTopCustomers } from '@/lib/backend/customers';
import { handleApiError } from '@/lib/backend/error-service';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10');
    const period = searchParams.get('period') || '30d';
    
    const topCustomers = await getTopCustomers(limit, period);
    
    return NextResponse.json(topCustomers, { status: 200 });
  } catch (error) {
    return handleApiError(error, 'Failed to get top customers');
  }
}
