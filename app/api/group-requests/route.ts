// app/api/group-requests/route.ts

import { NextRequest, NextResponse } from "next/server";
import mongoose from "mongoose";
import { connectToDatabase } from "@/lib/dbconnect";
import { getCorsHeaders } from "@/lib/cors";
import { verifyAccessToken } from "@/lib/auth";
import { GroupRequest } from "@/models/GroupRequest";
import { User } from "@/models/User";
import { Location } from "@/models/Location";
import { Township } from "@/models/Township";
import { City } from "@/models/City";
import { Province } from "@/models/Province";
import { NotificationService } from "@/lib/services/notificationService";
import '@/models'; // Ensure all models are loaded

export async function OPTIONS(req: NextRequest) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

// GET /api/group-requests - Get all group requests (admin only) or user's requests
export async function GET(req: NextRequest) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);

  try {
    await connectToDatabase();

    // Verify authentication
    const token = req.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { headers: corsHeaders, status: 401 }
      );
    }

    const payload = await verifyAccessToken(token);
    if (!payload) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { headers: corsHeaders, status: 401 }
      );
    }

    // Get user to check role
    const user = await User.findById(payload.userId);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { headers: corsHeaders, status: 404 }
      );
    }

    const { searchParams } = new URL(req.url);
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');

    let query: any = {};

    // If not admin, only show user's own requests
    if (user.role !== 'admin') {
      query.userId = user._id;
    } else {
      // Admin can filter by status
      if (status && ['pending', 'approved', 'rejected'].includes(status)) {
        query.status = status;
      }

      // Admin can search
      if (search) {
        query.$or = [
          { requestedGroupName: { $regex: search, $options: 'i' } },
          { userName: { $regex: search, $options: 'i' } },
          { userEmail: { $regex: search, $options: 'i' } },
          { fullLocationPath: { $regex: search, $options: 'i' } }
        ];
      }
    }

    const skip = (page - 1) * limit;

    const [requests, total] = await Promise.all([
      GroupRequest.find(query)
        .populate('userId', 'name email phone')
        .populate('reviewedBy', 'name email')
        .populate('createdGroupId', 'name _id')
        .sort({ requestDate: -1 })
        .skip(skip)
        .limit(limit),
      GroupRequest.countDocuments(query)
    ]);

    return NextResponse.json({
      requests,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    }, { headers: corsHeaders, status: 200 });

  } catch (error) {
    console.error("Error fetching group requests:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { headers: corsHeaders, status: 500 }
    );
  }
}

// POST /api/group-requests - Create new group request
export async function POST(req: NextRequest) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);

  try {
    await connectToDatabase();

    // Verify authentication
    const token = req.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { headers: corsHeaders, status: 401 }
      );
    }

    const payload = await verifyAccessToken(token);
    if (!payload) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { headers: corsHeaders, status: 401 }
      );
    }

    const body = await req.json();
    const {
      userId,
      userEmail,
      userName,
      userPhone,
      provinceId,
      provinceName,
      cityId,
      cityName,
      townshipId,
      townshipName,
      locationId,
      locationName,
      fullLocationPath,
      requestedGroupName,
      groupDescription
    } = body;

    // Validate required fields
    if (!userId || !userEmail || !userName || !locationId || !requestedGroupName) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Verify user exists and matches token
    if (payload.userId !== userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { headers: corsHeaders, status: 403 }
      );
    }

    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { headers: corsHeaders, status: 404 }
      );
    }

    // Verify location exists
    const location = await Location.findById(locationId);
    if (!location) {
      return NextResponse.json(
        { error: 'Location not found' },
        { headers: corsHeaders, status: 404 }
      );
    }

    // Check for existing pending request for same group name and location
    const existingRequest = await GroupRequest.findOne({
      requestedGroupName: requestedGroupName.trim(),
      locationId,
      status: { $ne: 'rejected' }
    });

    if (existingRequest) {
      return NextResponse.json(
        { error: 'A request for this group name already exists in this location' },
        { headers: corsHeaders, status: 409 }
      );
    }

    // Create the group request
    const groupRequest = new GroupRequest({
      userId,
      userEmail: userEmail.toLowerCase().trim(),
      userName: userName.trim(),
      userPhone: userPhone?.trim(),
      provinceId,
      provinceName: provinceName.trim(),
      cityId,
      cityName: cityName.trim(),
      townshipId,
      townshipName: townshipName.trim(),
      locationId,
      locationName: locationName.trim(),
      fullLocationPath: fullLocationPath.trim(),
      requestedGroupName: requestedGroupName.trim(),
      groupDescription: groupDescription?.trim(),
      status: 'pending'
    });

    await groupRequest.save();

    // Populate the response
    await groupRequest.populate('userId', 'name email phone');

    // Send notification to admins about new request
    try {
      await NotificationService.notifyGroupRequestSubmitted(groupRequest._id.toString());
    } catch (notificationError) {
      console.error('Failed to send notification:', notificationError);
      // Don't fail the request if notification fails
    }

    return NextResponse.json({
      success: true,
      message: 'Group request submitted successfully',
      request: groupRequest
    }, { headers: corsHeaders, status: 201 });

  } catch (error) {
    console.error("Error creating group request:", error);
    
    // Handle validation errors
    if (error instanceof mongoose.Error.ValidationError) {
      const validationErrors = Object.values(error.errors).map(err => err.message);
      return NextResponse.json(
        { error: 'Validation failed', details: validationErrors },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Handle duplicate key errors
    if (error instanceof Error && 'code' in error && error.code === 11000) {
      return NextResponse.json(
        { error: 'A request for this group name already exists in this location' },
        { headers: corsHeaders, status: 409 }
      );
    }

    return NextResponse.json(
      { error: "Internal Server Error" },
      { headers: corsHeaders, status: 500 }
    );
  }
}
