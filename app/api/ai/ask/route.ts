// app/api/ai/ask/route.ts
import { NextResponse } from 'next/server';
import { createChatMemory } from '@/lib/langchain/memory';
import { createChatChain } from '@/lib/langchain/chain';
import { verifyAccessToken } from '@/lib/auth';
import { analyzeAIError, getErrorRecoveryMessage, logAIError } from '@/lib/utils/aiErrorHandler';

// Fallback response function - ONLY used when OpenAI quota is exceeded or billing issues
async function getFallbackResponse(query: string, userContext: any): Promise<string> {
  const lowerQuery = query.toLowerCase();

  // Common questions and responses
  if (lowerQuery.includes('join') && lowerQuery.includes('group')) {
    return `Hi! I'd be happy to help you join a Stokvel group. Here's how you can get started:

1. **Browse Groups**: Go to the Groups section to see available groups in your area
2. **Find Your Match**: Look for groups that match your shopping needs and location
3. **Request to Join**: Click "Join Group" on any group that interests you
4. **Wait for Approval**: The group admin will review and approve your request
5. **Start Shopping**: Once approved, you can participate in group orders and enjoy bulk discounts!

**Benefits of joining a group:**
- Save money with bulk pricing (up to 15% off)
- Share delivery costs with other members
- Connect with your community
- Access to group-exclusive deals

Would you like me to help you find groups in your specific area?`;
  }

  if (lowerQuery.includes('product') || lowerQuery.includes('buy') || lowerQuery.includes('shop')) {
    return `I can help you find products! Here's what you can do:

**Browse Products:**
- Visit our Products page to see our full catalog
- Use filters to narrow down by category, price, or availability
- Search for specific items using the search bar

**Shopping Tips:**
- Individual orders: Shop normally and checkout by yourself
- Group orders: Join a group to get bulk discounts
- Bulk pricing: Larger group orders unlock better prices

**Popular Categories:**
- Electronics & Gadgets
- Home & Garden
- Fashion & Clothing
- Health & Beauty
- Food & Beverages

What type of products are you looking for?`;
  }

  if (lowerQuery.includes('order') || lowerQuery.includes('track') || lowerQuery.includes('delivery')) {
    if (userContext.userId) {
      return `I can help you with your orders! Here's what you can do:

**Track Your Orders:**
- Go to "My Orders" in your account
- Check order status and tracking information
- View estimated delivery dates

**Order Status Meanings:**
- **Pending**: Order received, being processed
- **Confirmed**: Payment confirmed, preparing for shipment
- **Shipped**: Order dispatched, on its way to you
- **Delivered**: Order completed

**Delivery Information:**
- Standard delivery: 3-5 business days
- Free delivery on orders over R500
- Group orders may have coordinated delivery

Need help with a specific order? Please provide your order number.`;
    } else {
      return `To track your orders, you'll need to sign in to your account first. Once logged in, you can:

- View all your order history
- Track current orders
- Check delivery status
- Download receipts

Would you like me to help you sign in?`;
    }
  }

  if (lowerQuery.includes('help') || lowerQuery.includes('support') || lowerQuery.includes('contact')) {
    return `I'm here to help! Here are some ways I can assist you:

**Common Topics:**
- How to join Stokvel groups
- Product browsing and shopping
- Order tracking and delivery
- Account management
- Payment methods and billing

**Quick Actions:**
- Browse products and categories
- Find groups in your area
- Check your order status
- Learn about bulk pricing benefits

**Need More Help?**
If you need immediate assistance, you can also contact our support team directly.

What specific topic would you like help with?`;
  }

  if (lowerQuery.includes('account') || lowerQuery.includes('profile') || lowerQuery.includes('login')) {
    return `I can help you with account-related questions:

**Account Management:**
- Update your personal information
- Manage delivery addresses
- Change password and security settings
- View your referral code and earnings

**Profile Features:**
- Order history and tracking
- Group memberships
- Wishlist and saved items
- Payment methods

**Getting Started:**
- Create a new account to unlock all features
- Join groups for bulk pricing benefits
- Earn rewards through our referral program

What would you like to do with your account?`;
  }

  // Default response
  return `Hello! I'm Sarah, your Stokvel Market assistant. I'm here to help you with:

**Shopping & Products:**
- Finding and browsing products
- Understanding bulk pricing and discounts
- Managing your shopping cart

**Groups & Community:**
- Joining Stokvel groups for better prices
- Understanding how group buying works
- Connecting with your local community

**Orders & Delivery:**
- Tracking your orders
- Understanding delivery options
- Managing your purchase history

**Account & Support:**
- Managing your profile and settings
- Getting help with any issues
- Learning about platform features

I'm currently running in simplified mode, but I'm still here to help! What would you like to know more about?`;
}

export async function POST(request: Request) {
  try {
    const { query, sessionId, userContext } = await request.json();

    if (!query || !sessionId) {
      return NextResponse.json(
        { error: 'Query and sessionId are required' },
        { status: 400 }
      );
    }

    // Extract user context from headers if available
    let enhancedUserContext = userContext || {};

    try {
      const authHeader = request.headers.get('authorization');
      if (authHeader?.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        const payload = await verifyAccessToken(token);
        if (payload) {
          enhancedUserContext = {
            ...enhancedUserContext,
            userId: payload.userId,
            userRole: payload.role,
            userEmail: payload.email
          };
        }
      }
    } catch (error) {
      // Continue without user context if token verification fails
      console.log('Token verification failed, continuing as guest');
    }

    // Create or retrieve chat memory
    const memory = await createChatMemory(sessionId);

    // Create chat chain with user context
    const chain = await createChatChain(memory, enhancedUserContext);

    // Create a streaming response with fallback
    const encoder = new TextEncoder();
    const stream = new ReadableStream({
      async start(controller) {
        try {
          // Add a greeting message if this is the first interaction
          const chatHistory = await memory.chatHistory.getMessages();
          let enhancedQuery = query;

          if (chatHistory.length === 0) {
            enhancedQuery = `Hello! I'm ${enhancedUserContext.userId ? 'a returning user' : 'new to the platform'}. ${query}`;
          }

          // Try AI first - this is the primary method
          try {
            await chain.call({
              question: enhancedQuery,
            }, {
              callbacks: [{
                handleLLMNewToken(token: string) {
                  controller.enqueue(encoder.encode(token));
                },
              }],
            });
          } catch (aiError: any) {
            // Analyze the error to determine appropriate response
            const errorInfo = analyzeAIError(aiError);
            logAIError(errorInfo, { query, userId: enhancedUserContext.userId });

            if (errorInfo.shouldUseFallback) {
              console.log(`Using fallback responses due to: ${errorInfo.logMessage}`);

              // Add notice about limited mode
              const fallbackNotice = `⚠️ ${errorInfo.userMessage}\n\n`;
              controller.enqueue(encoder.encode(fallbackNotice));

              // Get fallback response
              const fallbackResponse = await getFallbackResponse(query, enhancedUserContext);

              // Stream the fallback response
              const words = fallbackResponse.split(' ');
              for (let i = 0; i < words.length; i++) {
                const word = words[i] + (i < words.length - 1 ? ' ' : '');
                controller.enqueue(encoder.encode(word));
                // Small delay to simulate streaming
                await new Promise(resolve => setTimeout(resolve, 30));
              }

              // Add recovery information
              const recoveryMessage = getErrorRecoveryMessage(errorInfo);
              controller.enqueue(encoder.encode(recoveryMessage));
            } else {
              // For non-quota errors, provide a different message and don't use fallback
              const errorMessage = `I'm experiencing technical difficulties right now. ${errorInfo.userMessage}`;
              controller.enqueue(encoder.encode(errorMessage));
            }
          }

          controller.close();
        } catch (error) {
          console.error('AI chain error:', error);

          // Final fallback
          const errorResponse = "I'm experiencing some technical difficulties right now. Please try again in a moment, or contact our support team for immediate assistance.";
          controller.enqueue(encoder.encode(errorResponse));
          controller.close();
        }
      },
    });

    return new NextResponse(stream, {
      headers: {
        "Content-Type": "text/plain",
        "Cache-Control": "no-cache",
      },
    });
  } catch (error) {
    console.error("Error generating AI response:", error);
    const errorMessage = error instanceof Error ? error.message : "An unknown error occurred";
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
