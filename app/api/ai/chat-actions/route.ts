// app/api/ai/chat-actions/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { AIChatService, ChatContext } from '@/lib/services/aiChatService';
import { verifyAccessToken } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const { action, context, query } = await request.json();
    
    if (!action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      );
    }

    // Extract user context from headers if available
    let chatContext: ChatContext = {
      sessionId: context?.sessionId || 'anonymous',
      currentPage: context?.currentPage,
      groupId: context?.groupId
    };
    
    try {
      const authHeader = request.headers.get('authorization');
      if (authHeader?.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        const payload = await verifyAccessToken(token);
        if (payload) {
          chatContext.userId = payload.userId;
          chatContext.userRole = payload.role;
        }
      }
    } catch (error) {
      // Continue without user context if token verification fails
      console.log('Token verification failed, continuing as guest');
    }

    let response;

    switch (action) {
      case 'get_quick_actions':
        response = await AIChatService.getQuickActions(chatContext);
        break;
      
      case 'get_page_help':
        response = await AIChatService.getPageHelp(chatContext.currentPage || '/');
        break;
      
      case 'search_products':
        if (!query) {
          return NextResponse.json(
            { error: 'Query is required for product search' },
            { status: 400 }
          );
        }
        response = await AIChatService.searchProducts(query);
        break;
      
      case 'get_order_status':
        if (!chatContext.userId) {
          response = {
            success: false,
            message: "I need you to be logged in to check your order status. Would you like to sign in?"
          };
        } else {
          response = await AIChatService.getOrderStatus(chatContext.userId, query);
        }
        break;
      
      case 'get_group_info':
        if (!chatContext.userId) {
          response = {
            success: false,
            message: "I need you to be logged in to check your group information. Would you like to sign in?"
          };
        } else {
          response = await AIChatService.getGroupInfo(chatContext.userId);
        }
        break;
      
      default:
        response = {
          success: false,
          message: "I'm not sure how to help with that specific request. Could you try asking in a different way?"
        };
    }

    return NextResponse.json(response);
  } catch (error) {
    console.error('AI chat actions error:', error);
    return NextResponse.json(
      { 
        success: false,
        message: "I'm experiencing some technical difficulties right now. Please try again in a moment or contact our support team if the issue persists."
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const action = searchParams.get('action');
    
    if (!action) {
      return NextResponse.json(
        { error: 'Action parameter is required' },
        { status: 400 }
      );
    }

    // Extract user context from headers if available
    let chatContext: ChatContext = {
      sessionId: 'anonymous',
      currentPage: searchParams.get('page') || '/'
    };
    
    try {
      const authHeader = request.headers.get('authorization');
      if (authHeader?.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        const payload = await verifyAccessToken(token);
        if (payload) {
          chatContext.userId = payload.userId;
          chatContext.userRole = payload.role;
        }
      }
    } catch (error) {
      // Continue without user context if token verification fails
      console.log('Token verification failed, continuing as guest');
    }

    let response;

    switch (action) {
      case 'quick_actions':
        response = await AIChatService.getQuickActions(chatContext);
        break;
      
      case 'page_help':
        response = await AIChatService.getPageHelp(chatContext.currentPage || '/');
        break;
      
      default:
        response = {
          success: false,
          message: "Unknown action requested"
        };
    }

    return NextResponse.json(response);
  } catch (error) {
    console.error('AI chat actions GET error:', error);
    return NextResponse.json(
      { 
        success: false,
        message: "Technical error occurred"
      },
      { status: 500 }
    );
  }
}
