// app/api/ai/health/route.ts
import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';

export async function GET() {
  try {
    const healthCheck = {
      timestamp: new Date().toISOString(),
      status: 'checking',
      services: {
        openai: {
          configured: !!process.env.OPENAI_API_KEY,
          keyLength: process.env.OPENAI_API_KEY?.length || 0,
          keyPrefix: process.env.OPENAI_API_KEY?.substring(0, 10) + '...',
          model: process.env.OPENAI_MODEL || 'gpt-4'
        },
        database: {
          configured: !!process.env.MONGODB_URL,
          connected: false
        },
        auth: {
          configured: !!process.env.JWT_SECRET,
          secretLength: process.env.JWT_SECRET?.length || 0
        },
        environment: {
          nodeEnv: process.env.NODE_ENV,
          nextEnv: process.env.NEXT_PUBLIC_APP_URL
        }
      },
      recommendations: []
    };

    // Test database connection
    try {
      await connectToDatabase();
      healthCheck.services.database.connected = true;
    } catch (dbError) {
      healthCheck.services.database.connected = false;
      healthCheck.recommendations.push('Database connection failed - check MONGODB_URL');
    }

    // Check OpenAI configuration
    if (!process.env.OPENAI_API_KEY) {
      healthCheck.recommendations.push('OPENAI_API_KEY is missing');
    } else if (process.env.OPENAI_API_KEY.length < 50) {
      healthCheck.recommendations.push('OPENAI_API_KEY appears to be invalid (too short)');
    }

    // Check JWT configuration
    if (!process.env.JWT_SECRET) {
      healthCheck.recommendations.push('JWT_SECRET is missing');
    } else if (process.env.JWT_SECRET.length < 32) {
      healthCheck.recommendations.push('JWT_SECRET should be at least 32 characters long');
    }

    // Determine overall status
    if (healthCheck.recommendations.length === 0) {
      healthCheck.status = 'healthy';
    } else {
      healthCheck.status = 'needs_attention';
    }

    return NextResponse.json(healthCheck);
  } catch (error) {
    return NextResponse.json({
      timestamp: new Date().toISOString(),
      status: 'error',
      error: 'Health check failed',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
