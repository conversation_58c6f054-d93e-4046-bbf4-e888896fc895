// app/api/stokvel-groups/leave/route.ts

import { NextRequest, NextResponse } from "next/server";
import mongoose from "mongoose";
import { connectToDatabase } from "@/lib/dbconnect";
import { getCorsHeaders } from "@/lib/cors";
import { User } from "@/models/User";
import { StokvelGroup } from "@/models/StokvelGroup";
import { ShoppingCart } from "@/models/ShoppingCart";
import '@/models'; // Ensure all models are loaded

export async function OPTIONS(req: NextRequest) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function POST(req: NextRequest) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);

  try {
    await connectToDatabase();

    const body = await req.json();
    const { userId, groupId, transferItemsToGroupId } = body;

    if (!userId || !groupId) {
      return NextResponse.json(
        { error: "userId and groupId are required." },
        { headers: corsHeaders, status: 400 }
      );
    }

    if (!mongoose.Types.ObjectId.isValid(userId) || !mongoose.Types.ObjectId.isValid(groupId)) {
      return NextResponse.json(
        { error: "Invalid userId or groupId format" },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Find user and group
    const [user, group] = await Promise.all([
      User.findById(userId),
      StokvelGroup.findById(groupId)
    ]);

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { headers: corsHeaders, status: 404 }
      );
    }

    if (!group) {
      return NextResponse.json(
        { error: "Group not found" },
        { headers: corsHeaders, status: 404 }
      );
    }

    // Check if user is actually a member of this group
    if (!user.stokvelGroups.includes(group._id)) {
      return NextResponse.json(
        { error: "User is not a member of this group" },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Check for pending cart items
    const pendingItems = await ShoppingCart.find({
      user: userId,
      groupId: groupId,
      status: { $in: ['pending', 'processing'] }
    });

    if (pendingItems.length > 0) {
      if (!transferItemsToGroupId) {
        return NextResponse.json(
          { 
            error: "Cannot leave group with pending items. Please specify a group to transfer items to.",
            pendingItemsCount: pendingItems.length,
            requiresTransfer: true
          },
          { headers: corsHeaders, status: 400 }
        );
      }

      // Validate transfer target group
      if (!mongoose.Types.ObjectId.isValid(transferItemsToGroupId)) {
        return NextResponse.json(
          { error: "Invalid transfer target group ID" },
          { headers: corsHeaders, status: 400 }
        );
      }

      const targetGroup = await StokvelGroup.findById(transferItemsToGroupId);
      if (!targetGroup) {
        return NextResponse.json(
          { error: "Transfer target group not found" },
          { headers: corsHeaders, status: 404 }
        );
      }

      // Check if user is a member of target group
      if (!user.stokvelGroups.includes(targetGroup._id)) {
        return NextResponse.json(
          { error: "User is not a member of the target group" },
          { headers: corsHeaders, status: 400 }
        );
      }

      // Transfer pending items to the target group
      await ShoppingCart.updateMany(
        {
          user: userId,
          groupId: groupId,
          status: { $in: ['pending', 'processing'] }
        },
        {
          $set: { groupId: transferItemsToGroupId }
        }
      );
    }

    // Remove user from group's members array
    group.members = group.members.filter(
      (memberId: mongoose.Types.ObjectId) => memberId.toString() !== userId
    );
    await group.save();

    // Remove group from user's stokvelGroups array
    user.stokvelGroups = user.stokvelGroups.filter(
      (userGroupId: mongoose.Types.ObjectId) => userGroupId.toString() !== groupId
    );
    await user.save();

    return NextResponse.json(
      {
        success: true,
        message: pendingItems.length > 0 
          ? `Successfully left group and transferred ${pendingItems.length} pending items`
          : "Successfully left group",
        transferredItems: pendingItems.length
      },
      { headers: corsHeaders, status: 200 }
    );

  } catch (error) {
    console.error("Error leaving group:", error);
    return NextResponse.json(
      {
        success: false,
        message: "An error occurred while processing your request.",
        error: "Internal Server Error"
      },
      { headers: corsHeaders, status: 500 }
    );
  }
}
