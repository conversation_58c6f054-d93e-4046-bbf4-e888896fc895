// app/api/orders/[orderId]/status/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { updateOrderStatus } from '@/lib/backend/orders';
import { handleApiError } from '@/lib/backend/error-service';

export async function PATCH(
  request: NextRequest,
  { params }: { params: { orderId: string } }
) {
  try {
    const { orderId } = params;
    const { status, notes } = await request.json();

    if (!status) {
      return NextResponse.json(
        { error: 'Status is required' },
        { status: 400 }
      );
    }

    const validStatuses = ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status' },
        { status: 400 }
      );
    }

    const order = await updateOrderStatus(orderId, status, notes);
    
    return NextResponse.json(order, { status: 200 });
  } catch (error) {
    return handleApiError(error, 'Failed to update order status');
  }
}
