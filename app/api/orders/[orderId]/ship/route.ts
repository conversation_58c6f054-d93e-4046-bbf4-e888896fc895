// app/api/orders/[orderId]/ship/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { shipOrder } from '@/lib/backend/orders';
import { handleApiError } from '@/lib/backend/error-service';

export async function PATCH(
  request: NextRequest,
  { params }: { params: { orderId: string } }
) {
  try {
    const { orderId } = params;
    const { trackingNumber, carrier, estimatedDelivery } = await request.json();

    if (!trackingNumber || !carrier) {
      return NextResponse.json(
        { error: 'Tracking number and carrier are required' },
        { status: 400 }
      );
    }

    const order = await shipOrder(orderId, trackingNumber, carrier, estimatedDelivery);
    
    return NextResponse.json(order, { status: 200 });
  } catch (error) {
    return handleApiError(error, 'Failed to ship order');
  }
}
