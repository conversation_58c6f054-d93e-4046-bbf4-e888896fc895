// app/api/orders/admin/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { getAllOrdersPaginated } from '@/lib/backend/orders';
import { handleApiError } from '@/lib/backend/error-service';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    
    const filters = {
      status: searchParams.get('status') || undefined,
      paymentStatus: searchParams.get('paymentStatus') || undefined,
      dateFrom: searchParams.get('dateFrom') || undefined,
      dateTo: searchParams.get('dateTo') || undefined,
      groupId: searchParams.get('groupId') || undefined,
      userId: searchParams.get('userId') || undefined,
    };

    // Remove undefined values
    Object.keys(filters).forEach(key => {
      if (filters[key as keyof typeof filters] === undefined) {
        delete filters[key as keyof typeof filters];
      }
    });

    const result = await getAllOrdersPaginated(page, limit, filters);
    
    return NextResponse.json(result, { status: 200 });
  } catch (error) {
    return handleApiError(error, 'Failed to get orders');
  }
}
