// app/api/orders/overview/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { getOrderOverview } from '@/lib/backend/orders';
import { handleApiError } from '@/lib/backend/error-service';

export async function GET(request: NextRequest) {
  try {
    const overview = await getOrderOverview();
    
    return NextResponse.json(overview, { status: 200 });
  } catch (error) {
    return handleApiError(error, 'Failed to get order overview');
  }
}
