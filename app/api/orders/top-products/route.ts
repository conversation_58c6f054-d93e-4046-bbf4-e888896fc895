// app/api/orders/top-products/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { getTopSellingProducts } from '@/lib/backend/orders';
import { handleApiError } from '@/lib/backend/error-service';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10');
    const period = searchParams.get('period') || '30d';
    
    const topProducts = await getTopSellingProducts(limit, period);
    
    return NextResponse.json(topProducts, { status: 200 });
  } catch (error) {
    return handleApiError(error, 'Failed to get top selling products');
  }
}
