// app/api/admin/products/analytics/route.ts

import { NextRequest, NextResponse } from "next/server";
import { connectToDatabase } from "@/lib/dbconnect";
import { getCorsHeaders } from "@/lib/cors";
import { Product } from "@/models/Product";
import { ProductCategory } from "@/models/ProductCategory";
import { MemberOrder } from "@/models/MemberOrder";
import { GroupOrder } from "@/models/GroupOrder";
import mongoose from "mongoose";
import '@/models'; // Ensure all models are loaded

export async function GET(req: NextRequest) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);
  const { searchParams } = new URL(req.url);
  const period = parseInt(searchParams.get('period') || '30'); // Default 30 days

  try {
    await connectToDatabase();

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date(endDate.getTime() - period * 24 * 60 * 60 * 1000);
    const previousStartDate = new Date(startDate.getTime() - period * 24 * 60 * 60 * 1000);

    // Get basic product counts
    const [
      totalProducts,
      activeProducts,
      outOfStockProducts,
      lowStockProducts,
      totalCategories,
      activeCategories
    ] = await Promise.all([
      Product.countDocuments({ isArchived: false }),
      Product.countDocuments({ isArchived: false, stock: { $gt: 0 } }),
      Product.countDocuments({ isArchived: false, stock: 0 }),
      Product.countDocuments({ isArchived: false, stock: { $gt: 0, $lte: 10 } }),
      ProductCategory.countDocuments(),
      ProductCategory.countDocuments({ is_active: true })
    ]);

    // Get revenue data from orders
    const revenueAggregation = await GroupOrder.aggregate([
      {
        $match: {
          orderPlacedAt: { $gte: startDate, $lte: endDate },
          status: { $in: ['completed', 'delivered'] }
        }
      },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$totalOrderValue' },
          totalOrders: { $sum: 1 },
          averageOrderValue: { $avg: '$totalOrderValue' }
        }
      }
    ]);

    // Get previous period revenue for trend calculation
    const previousRevenueAggregation = await GroupOrder.aggregate([
      {
        $match: {
          orderPlacedAt: { $gte: previousStartDate, $lt: startDate },
          status: { $in: ['completed', 'delivered'] }
        }
      },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$totalOrderValue' },
          totalOrders: { $sum: 1 }
        }
      }
    ]);

    const currentRevenue = revenueAggregation[0] || { totalRevenue: 0, totalOrders: 0, averageOrderValue: 0 };
    const previousRevenue = previousRevenueAggregation[0] || { totalRevenue: 0, totalOrders: 0 };

    // Calculate trends
    const revenueTrend = previousRevenue.totalRevenue > 0 
      ? ((currentRevenue.totalRevenue - previousRevenue.totalRevenue) / previousRevenue.totalRevenue * 100)
      : currentRevenue.totalRevenue > 0 ? 100 : 0;

    const ordersTrend = previousRevenue.totalOrders > 0 
      ? ((currentRevenue.totalOrders - previousRevenue.totalOrders) / previousRevenue.totalOrders * 100)
      : currentRevenue.totalOrders > 0 ? 100 : 0;

    // Get top selling products
    const topSellingProducts = await GroupOrder.aggregate([
      {
        $match: {
          orderPlacedAt: { $gte: startDate, $lte: endDate },
          status: { $in: ['completed', 'delivered'] }
        }
      },
      { $unwind: '$orderItems' },
      {
        $group: {
          _id: '$orderItems.product',
          totalSold: { $sum: '$orderItems.quantity' },
          totalRevenue: { $sum: { $multiply: ['$orderItems.quantity', '$orderItems.price'] } }
        }
      },
      {
        $lookup: {
          from: 'products',
          localField: '_id',
          foreignField: '_id',
          as: 'productInfo'
        }
      },
      { $unwind: '$productInfo' },
      {
        $project: {
          name: '$productInfo.name',
          totalSold: 1,
          totalRevenue: 1
        }
      },
      { $sort: { totalSold: -1 } },
      { $limit: 10 }
    ]);

    // Get sales trend data (last 7 days)
    const salesTrendData = await GroupOrder.aggregate([
      {
        $match: {
          orderPlacedAt: { $gte: new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000), $lte: endDate },
          status: { $in: ['completed', 'delivered'] }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: "%Y-%m-%d", date: "$orderPlacedAt" }
          },
          sales: { $sum: '$totalOrderValue' },
          orders: { $sum: 1 }
        }
      },
      { $sort: { '_id': 1 } }
    ]);

    // Format sales trend data
    const last7Days = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date(endDate.getTime() - i * 24 * 60 * 60 * 1000);
      const dateStr = date.toISOString().split('T')[0];
      const dayData = salesTrendData.find(d => d._id === dateStr);
      
      last7Days.push({
        date: date.toLocaleDateString('en-US', { weekday: 'short' }),
        sales: dayData?.sales || 0,
        orders: dayData?.orders || 0
      });
    }

    // Get category performance
    const categoryPerformance = await ProductCategory.aggregate([
      {
        $lookup: {
          from: 'products',
          localField: '_id',
          foreignField: 'category',
          as: 'products'
        }
      },
      {
        $project: {
          name: 1,
          productCount: { $size: '$products' },
          activeProducts: {
            $size: {
              $filter: {
                input: '$products',
                cond: { $and: [{ $eq: ['$$this.isArchived', false] }, { $gt: ['$$this.stock', 0] }] }
              }
            }
          }
        }
      },
      { $sort: { productCount: -1 } }
    ]);

    const analytics = {
      overview: {
        totalProducts: totalProducts || 0,
        activeProducts: activeProducts || 0,
        outOfStockProducts: outOfStockProducts || 0,
        lowStockProducts: lowStockProducts || 0,
        totalCategories: totalCategories || 0,
        activeCategories: activeCategories || 0
      },
      revenue: {
        total: currentRevenue.totalRevenue || 0,
        trend: Math.round(revenueTrend * 100) / 100,
        formatted: `R ${(currentRevenue.totalRevenue || 0).toLocaleString('en-ZA', { minimumFractionDigits: 2 })}`
      },
      orders: {
        total: currentRevenue.totalOrders || 0,
        trend: Math.round(ordersTrend * 100) / 100,
        averageValue: currentRevenue.averageOrderValue || 0,
        formatted: `${(currentRevenue.totalOrders || 0).toLocaleString()}`
      },
      topSellingProducts: topSellingProducts || [],
      salesTrend: last7Days,
      categoryPerformance: categoryPerformance || [],
      period,
      lastUpdated: new Date().toISOString()
    };

    return NextResponse.json({
      success: true,
      analytics
    }, { headers: corsHeaders, status: 200 });

  } catch (error) {
    console.error("Error fetching product analytics:", error);
    
    // Return fallback data
    const fallbackAnalytics = {
      overview: {
        totalProducts: 0,
        activeProducts: 0,
        outOfStockProducts: 0,
        lowStockProducts: 0,
        totalCategories: 0,
        activeCategories: 0
      },
      revenue: {
        total: 0,
        trend: 0,
        formatted: "R 0.00"
      },
      orders: {
        total: 0,
        trend: 0,
        averageValue: 0,
        formatted: "0"
      },
      topSellingProducts: [],
      salesTrend: [],
      categoryPerformance: [],
      period,
      lastUpdated: new Date().toISOString()
    };

    return NextResponse.json({
      success: false,
      analytics: fallbackAnalytics,
      error: "Failed to fetch analytics"
    }, { headers: corsHeaders, status: 200 }); // Return 200 with fallback data
  }
}
