// app/api/admin/update-user-role/route.ts

import { NextRequest, NextResponse } from "next/server";
import { connectToDatabase } from "@/lib/dbconnect";
import { getCorsHeaders } from "@/lib/cors";
import { User } from "@/models/User";
import { verify } from 'jsonwebtoken';

const ACCESS_TOKEN_SECRET = process.env.ACCESS_TOKEN_SECRET!;

interface DecodedToken {
  id: string;
  role: string;
  iat?: number;
  exp?: number;
}

export async function OPTIONS(req: NextRequest) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

// POST /api/admin/update-user-role - Update user role to admin (temporary endpoint)
export async function POST(req: NextRequest) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);

  try {
    await connectToDatabase();

    // Get token from Authorization header
    const authHeader = req.headers.get('authorization') || '';
    if (!authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'No access token found' },
        { headers: corsHeaders, status: 401 }
      );
    }

    const accessToken = authHeader.substring(7);
    
    // Verify the token
    const decoded = verify(accessToken, ACCESS_TOKEN_SECRET) as DecodedToken;
    
    // Find the user in the DB
    const user = await User.findById(decoded.id);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { headers: corsHeaders, status: 404 }
      );
    }

    // Update user role to admin
    user.role = 'admin';
    await user.save();

    console.log(`Updated user ${user.email} role to admin`);

    return NextResponse.json(
      { 
        success: true, 
        message: `User ${user.email} role updated to admin`,
        user: {
          id: user._id,
          email: user.email,
          name: user.name,
          role: user.role
        }
      },
      { headers: corsHeaders, status: 200 }
    );

  } catch (error) {
    console.error("Error updating user role:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { headers: corsHeaders, status: 500 }
    );
  }
}
