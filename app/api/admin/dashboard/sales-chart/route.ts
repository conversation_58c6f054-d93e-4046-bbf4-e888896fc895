// app/api/admin/dashboard/sales-chart/route.ts

import { NextRequest, NextResponse } from "next/server";
import { connectToDatabase } from "@/lib/dbconnect";
import { getCorsHeaders } from "@/lib/cors";
import { verifyAccessToken } from "@/lib/auth";
import { User } from "@/models/User";
import { MemberOrder } from "@/models/MemberOrder";
import { BackendErrorService, FallbackDataGenerators } from "@/lib/services/backend-error-service";
import '@/models'; // Ensure all models are loaded

export async function OPTIONS(req: NextRequest) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

// GET /api/admin/dashboard/sales-chart - Get sales chart data
export async function GET(req: NextRequest) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);

  try {
    await connectToDatabase();

    // Verify authentication and admin role
    // Try to get token from cookies first (web), then from Authorization header (mobile)
    let token = req.cookies.get('accessToken')?.value;
    if (!token) {
      token = req.headers.get('authorization')?.replace('Bearer ', '');
    }

    if (!token) {
      return NextResponse.json(
        { error: 'No token provided' },
        { headers: corsHeaders, status: 401 }
      );
    }

    const payload = await verifyAccessToken(token);
    if (!payload) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { headers: corsHeaders, status: 401 }
      );
    }

    const user = await User.findById(payload.userId);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { headers: corsHeaders, status: 404 }
      );
    }

    if (user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { headers: corsHeaders, status: 403 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const period = searchParams.get('period') || 'week'; // week, month, year
    const days = period === 'week' ? 7 : period === 'month' ? 30 : 365;

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Get sales data aggregated by day
    const salesData = await MemberOrder.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' },
            dayOfWeek: { $dayOfWeek: '$createdAt' }
          },
          sales: {
            $sum: {
              $cond: [
                { $eq: ['$paymentInfo.status', 'paid'] },
                '$totalAmount',
                0
              ]
            }
          },
          orders: { $sum: 1 },
          paidOrders: {
            $sum: {
              $cond: [
                { $eq: ['$paymentInfo.status', 'paid'] },
                1,
                0
              ]
            }
          }
        }
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 }
      }
    ]);

    // Create a complete date range array
    const chartData = [];
    const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    for (let i = 0; i < days; i++) {
      const currentDate = new Date(startDate);
      currentDate.setDate(startDate.getDate() + i);
      
      const year = currentDate.getFullYear();
      const month = currentDate.getMonth() + 1;
      const day = currentDate.getDate();
      const dayOfWeek = currentDate.getDay();

      // Find matching data
      const matchingData = salesData.find(item => 
        item._id.year === year && 
        item._id.month === month && 
        item._id.day === day
      );

      let label;
      if (period === 'week') {
        label = dayNames[dayOfWeek];
      } else if (period === 'month') {
        label = `${monthNames[month - 1]} ${day}`;
      } else {
        label = `${monthNames[month - 1]}`;
      }

      chartData.push({
        day: label,
        date: currentDate.toISOString().split('T')[0],
        sales: matchingData ? Math.round(matchingData.sales) : 0,
        orders: matchingData ? matchingData.orders : 0,
        paidOrders: matchingData ? matchingData.paidOrders : 0
      });
    }

    // If period is year, group by month
    if (period === 'year') {
      const monthlyData = [];
      const monthlyGroups = {};

      chartData.forEach(item => {
        const monthKey = item.day;
        if (!monthlyGroups[monthKey]) {
          monthlyGroups[monthKey] = {
            day: monthKey,
            sales: 0,
            orders: 0,
            paidOrders: 0
          };
        }
        monthlyGroups[monthKey].sales += item.sales;
        monthlyGroups[monthKey].orders += item.orders;
        monthlyGroups[monthKey].paidOrders += item.paidOrders;
      });

      Object.values(monthlyGroups).forEach(group => monthlyData.push(group));
      chartData.length = 0;
      chartData.push(...monthlyData);
    }

    // Calculate summary statistics
    const totalSales = chartData.reduce((sum, item) => sum + item.sales, 0);
    const totalOrders = chartData.reduce((sum, item) => sum + item.orders, 0);
    const totalPaidOrders = chartData.reduce((sum, item) => sum + item.paidOrders, 0);
    const averageOrderValue = totalPaidOrders > 0 ? totalSales / totalPaidOrders : 0;

    // Calculate trends (compare first half vs second half)
    const midPoint = Math.floor(chartData.length / 2);
    const firstHalf = chartData.slice(0, midPoint);
    const secondHalf = chartData.slice(midPoint);

    const firstHalfSales = firstHalf.reduce((sum, item) => sum + item.sales, 0);
    const secondHalfSales = secondHalf.reduce((sum, item) => sum + item.sales, 0);
    
    const salesTrend = firstHalfSales > 0 
      ? ((secondHalfSales - firstHalfSales) / firstHalfSales * 100)
      : secondHalfSales > 0 ? 100 : 0;

    const response = {
      success: true,
      data: chartData,
      summary: {
        totalSales,
        totalOrders,
        totalPaidOrders,
        averageOrderValue,
        salesTrend,
        period,
        dateRange: {
          start: startDate.toISOString(),
          end: endDate.toISOString()
        }
      },
      lastUpdated: new Date().toISOString()
    };

    return NextResponse.json(response, { headers: corsHeaders, status: 200 });

  } catch (error) {
    console.error("Error fetching sales chart data:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { headers: corsHeaders, status: 500 }
    );
  }
}
