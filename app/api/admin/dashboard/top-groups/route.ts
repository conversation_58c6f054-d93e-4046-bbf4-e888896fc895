// app/api/admin/dashboard/top-groups/route.ts

import { NextRequest, NextResponse } from "next/server";
import { connectToDatabase } from "@/lib/dbconnect";
import { getCorsHeaders } from "@/lib/cors";
import { verifyAccessToken } from "@/lib/auth";
import { User } from "@/models/User";
import { StokvelGroup } from "@/models/StokvelGroup";
import { MemberOrder } from "@/models/MemberOrder";
import { Location } from "@/models/Location";
import { Township } from "@/models/Township";
import { City } from "@/models/City";
import { Province } from "@/models/Province";
import '@/models'; // Ensure all models are loaded

export async function OPTIONS(req: NextRequest) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

// GET /api/admin/dashboard/top-groups - Get top performing groups
export async function GET(req: NextRequest) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);

  try {
    await connectToDatabase();

    // Verify authentication and admin role
    // Try to get token from cookies first (web), then from Authorization header (mobile)
    let token = req.cookies.get('accessToken')?.value;
    if (!token) {
      token = req.headers.get('authorization')?.replace('Bearer ', '');
    }

    if (!token) {
      return NextResponse.json(
        { error: 'No token provided' },
        { headers: corsHeaders, status: 401 }
      );
    }

    const payload = await verifyAccessToken(token);
    if (!payload) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { headers: corsHeaders, status: 401 }
      );
    }

    const user = await User.findById(payload.userId);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { headers: corsHeaders, status: 404 }
      );
    }

    if (user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { headers: corsHeaders, status: 403 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const period = searchParams.get('period') || '30'; // days
    const limit = parseInt(searchParams.get('limit') || '10');
    const sortBy = searchParams.get('sortBy') || 'revenue'; // revenue, orders, members

    // Calculate date range
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(period));

    // Get group performance data
    const groupPerformance = await MemberOrder.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate },
          'paymentInfo.status': 'paid'
        }
      },
      {
        $group: {
          _id: '$groupId',
          totalRevenue: { $sum: '$totalAmount' },
          totalOrders: { $sum: 1 },
          averageOrderValue: { $avg: '$totalAmount' },
          uniqueCustomers: { $addToSet: '$userId' }
        }
      },
      {
        $addFields: {
          customerCount: { $size: '$uniqueCustomers' }
        }
      },
      {
        $sort: {
          [sortBy === 'revenue' ? 'totalRevenue' : 
           sortBy === 'orders' ? 'totalOrders' : 
           sortBy === 'members' ? 'customerCount' : 'totalRevenue']: -1
        }
      },
      {
        $limit: limit
      }
    ]);

    // Get group details and location information
    const enrichedGroups = await Promise.all(
      groupPerformance.map(async (groupData) => {
        try {
          const group = await StokvelGroup.findById(groupData._id)
            .populate('admin', 'name email')
            .lean();

          if (!group) {
            return null;
          }

          // Get location hierarchy
          let locationPath = 'Unknown Location';
          if (group.locationId) {
            try {
              const location = await Location.findById(group.locationId).lean();
              if (location) {
                const township = await Township.findById(location.townshipId).lean();
                if (township) {
                  const city = await City.findById(township.cityId).lean();
                  if (city) {
                    const province = await Province.findById(city.provinceId).lean();
                    if (province) {
                      locationPath = `${location.name}, ${township.name}, ${city.name}, ${province.name}`;
                    }
                  }
                }
              }
            } catch (locationError) {
              console.error('Error fetching location for group:', group._id, locationError);
            }
          } else if (group.geolocation) {
            // Fallback to legacy geolocation
            locationPath = group.geolocation;
          }

          return {
            id: group._id,
            name: group.name,
            location: locationPath,
            admin: group.admin,
            totalMembers: group.members?.length || 0,
            totalRevenue: groupData.totalRevenue,
            totalOrders: groupData.totalOrders,
            averageOrderValue: groupData.averageOrderValue,
            activeCustomers: groupData.customerCount,
            createdAt: group.createdAt,
            // Performance metrics
            revenuePerMember: group.members?.length > 0 
              ? groupData.totalRevenue / group.members.length 
              : 0,
            ordersPerMember: group.members?.length > 0 
              ? groupData.totalOrders / group.members.length 
              : 0
          };
        } catch (error) {
          console.error('Error processing group:', groupData._id, error);
          return null;
        }
      })
    );

    // Filter out null results and format response
    const validGroups = enrichedGroups.filter(group => group !== null);

    // Calculate summary statistics
    const totalRevenue = validGroups.reduce((sum, group) => sum + group.totalRevenue, 0);
    const totalOrders = validGroups.reduce((sum, group) => sum + group.totalOrders, 0);
    const totalMembers = validGroups.reduce((sum, group) => sum + group.totalMembers, 0);
    const averageGroupSize = validGroups.length > 0 ? totalMembers / validGroups.length : 0;

    // Format currency values
    const formattedGroups = validGroups.map(group => ({
      ...group,
      formattedRevenue: `R${group.totalRevenue.toLocaleString()}`,
      formattedAverageOrderValue: `R${group.averageOrderValue.toFixed(2)}`,
      formattedRevenuePerMember: `R${group.revenuePerMember.toFixed(2)}`
    }));

    const response = {
      success: true,
      groups: formattedGroups,
      summary: {
        totalGroups: validGroups.length,
        totalRevenue,
        totalOrders,
        totalMembers,
        averageGroupSize: Math.round(averageGroupSize),
        period: parseInt(period),
        sortBy,
        limit
      },
      lastUpdated: new Date().toISOString()
    };

    return NextResponse.json(response, { headers: corsHeaders, status: 200 });

  } catch (error) {
    console.error("Error fetching top groups data:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { headers: corsHeaders, status: 500 }
    );
  }
}
