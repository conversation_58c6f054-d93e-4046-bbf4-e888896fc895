// app/api/admin/dashboard/stats/route.ts
// Dashboard statistics API endpoint

import { NextRequest, NextResponse } from "next/server";
import { connectToDatabase } from "@/lib/dbconnect";
import { getCorsHeaders } from "@/lib/cors";
import { verifyAccessToken } from "@/lib/auth";
import { User } from "@/models/User";
import { MemberOrder } from "@/models/MemberOrder";
import { GroupOrder } from "@/models/GroupOrder";
import { StokvelGroup } from "@/models/StokvelGroup";
import { Product } from "@/models/Product";
import { BackendErrorService, FallbackDataGenerators } from "@/lib/services/backend-error-service";
import '@/models'; // Ensure all models are loaded

export async function OPTIONS(req: NextRequest) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

// GET /api/admin/dashboard/stats - Get dashboard statistics
export async function GET(req: NextRequest) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);

  try {
    await connectToDatabase();

    // Verify authentication and admin role
    // Try to get token from cookies first (web), then from Authorization header (mobile)
    let token = req.cookies.get('accessToken')?.value;
    if (!token) {
      token = req.headers.get('authorization')?.replace('Bearer ', '');
    }

    if (!token) {
      return NextResponse.json(
        { error: 'No token provided' },
        { headers: corsHeaders, status: 401 }
      );
    }

    const payload = await verifyAccessToken(token);
    if (!payload) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { headers: corsHeaders, status: 401 }
      );
    }

    const user = await User.findById(payload.userId);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { headers: corsHeaders, status: 404 }
      );
    }

    if (user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { headers: corsHeaders, status: 403 }
      );
    }

    // Get date range from query params
    const { searchParams } = new URL(req.url);
    const period = searchParams.get('period') || '30'; // days
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(period));

    // Calculate previous period for comparison
    const previousStartDate = new Date(startDate);
    previousStartDate.setDate(previousStartDate.getDate() - parseInt(period));

    // Parallel data fetching for performance
    const [
      // Current period data
      currentRevenue,
      currentOrders,
      currentUsers,
      currentGroups,
      
      // Previous period data for comparison
      previousRevenue,
      previousOrders,
      previousUsers,
      previousGroups,
      
      // Additional metrics
      totalProducts,
      activeGroups
    ] = await Promise.all([
      // Current period revenue
      MemberOrder.aggregate([
        {
          $match: {
            createdAt: { $gte: startDate },
            'paymentInfo.status': 'paid'
          }
        },
        {
          $group: {
            _id: null,
            totalRevenue: { $sum: '$totalAmount' },
            totalOrders: { $sum: 1 },
            averageOrderValue: { $avg: '$totalAmount' }
          }
        }
      ]),

      // Current period orders
      MemberOrder.countDocuments({
        createdAt: { $gte: startDate }
      }),

      // Current period new users
      User.countDocuments({
        createdAt: { $gte: startDate },
        role: { $in: ['customer', 'member'] }
      }),

      // Current period new groups
      StokvelGroup.countDocuments({
        createdAt: { $gte: startDate }
      }),

      // Previous period revenue
      MemberOrder.aggregate([
        {
          $match: {
            createdAt: { $gte: previousStartDate, $lt: startDate },
            'paymentInfo.status': 'paid'
          }
        },
        {
          $group: {
            _id: null,
            totalRevenue: { $sum: '$totalAmount' },
            totalOrders: { $sum: 1 }
          }
        }
      ]),

      // Previous period orders
      MemberOrder.countDocuments({
        createdAt: { $gte: previousStartDate, $lt: startDate }
      }),

      // Previous period users
      User.countDocuments({
        createdAt: { $gte: previousStartDate, $lt: startDate },
        role: { $in: ['customer', 'member'] }
      }),

      // Previous period groups
      StokvelGroup.countDocuments({
        createdAt: { $gte: previousStartDate, $lt: startDate }
      }),

      // Total products
      Product.countDocuments({ isArchived: false }),

      // Active groups
      StokvelGroup.countDocuments({})
    ]);

    // Calculate metrics and trends
    const currentRevenueData = currentRevenue[0] || { totalRevenue: 0, totalOrders: 0, averageOrderValue: 0 };
    const previousRevenueData = previousRevenue[0] || { totalRevenue: 0, totalOrders: 0 };

    const revenueTrend = previousRevenueData.totalRevenue > 0 
      ? ((currentRevenueData.totalRevenue - previousRevenueData.totalRevenue) / previousRevenueData.totalRevenue * 100)
      : currentRevenueData.totalRevenue > 0 ? 100 : 0;

    const ordersTrend = previousOrders > 0 
      ? ((currentOrders - previousOrders) / previousOrders * 100)
      : currentOrders > 0 ? 100 : 0;

    const usersTrend = previousUsers > 0 
      ? ((currentUsers - previousUsers) / previousUsers * 100)
      : currentUsers > 0 ? 100 : 0;

    const groupsTrend = previousGroups > 0 
      ? ((currentGroups - previousGroups) / previousGroups * 100)
      : currentGroups > 0 ? 100 : 0;

    // Get total users for customer percentage calculation
    const totalUsers = await User.countDocuments({ role: { $in: ['customer', 'member'] } });
    const customerGrowthRate = totalUsers > 0 ? (currentUsers / totalUsers * 100) : 0;

    const stats = {
      revenue: {
        value: currentRevenueData.totalRevenue,
        trend: revenueTrend,
        description: `From the last ${period} days`,
        formatted: `R${(currentRevenueData.totalRevenue / 1000).toFixed(1)}K`
      },
      orders: {
        value: currentOrders,
        trend: ordersTrend,
        description: `Orders in last ${period} days`,
        formatted: currentOrders.toLocaleString()
      },
      customers: {
        value: customerGrowthRate,
        trend: usersTrend,
        description: `+${currentUsers} new customers`,
        formatted: `${customerGrowthRate.toFixed(1)}%`
      },
      averageSale: {
        value: currentRevenueData.averageOrderValue,
        trend: revenueTrend, // Use revenue trend as proxy
        description: 'Per transaction',
        formatted: `R${currentRevenueData.averageOrderValue.toFixed(0)}`
      },
      // Additional metrics
      totalGroups: activeGroups,
      totalProducts: totalProducts,
      period: parseInt(period),
      lastUpdated: new Date().toISOString()
    };

    const response = BackendErrorService.createSuccessResponse(
      { stats },
      false,
      'database'
    );

    return BackendErrorService.createNextResponse(response, corsHeaders);

  } catch (error) {
    console.error("Error fetching dashboard stats:", error);
    BackendErrorService.logError(
      BackendErrorService.createErrorResponse(
        'DATABASE_ERROR' as any,
        'Failed to fetch dashboard statistics',
        error,
        FallbackDataGenerators.dashboardStats()
      ),
      { endpoint: '/api/admin/dashboard/stats' }
    );

    return BackendErrorService.handleDatabaseError(error, 'dashboardStats');
  }
}
