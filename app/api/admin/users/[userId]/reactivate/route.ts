// app/api/admin/users/[userId]/reactivate/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { User } from '@/models/User';
import { handleApiError } from '@/lib/backend/error-service';

export async function POST(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    await connectToDatabase();

    const user = await User.findByIdAndUpdate(
      params.userId,
      {
        status: 'active',
        suspensionReason: null,
        suspendedAt: null,
        reactivatedAt: new Date(),
        updatedAt: new Date(),
      },
      { new: true, runValidators: true }
    ).select('-password -refreshToken');

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // TODO: Send notification email to user about reactivation
    // await sendReactivationNotificationEmail(user.email);

    return NextResponse.json(
      {
        ...user.toObject(),
        message: 'User account reactivated successfully',
      },
      { status: 200 }
    );
  } catch (error) {
    return handleApiError(error, 'Failed to reactivate user account');
  }
}
