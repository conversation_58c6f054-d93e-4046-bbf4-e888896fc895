// app/api/admin/users/[userId]/suspend/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { User } from '@/models/User';
import { handleApiError } from '@/lib/backend/error-service';

export async function POST(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    await connectToDatabase();

    const body = await request.json();
    const { reason } = body;

    if (!reason || reason.trim() === '') {
      return NextResponse.json(
        { error: 'Suspension reason is required' },
        { status: 400 }
      );
    }

    const user = await User.findByIdAndUpdate(
      params.userId,
      {
        status: 'suspended',
        suspensionReason: reason,
        suspendedAt: new Date(),
        updatedAt: new Date(),
      },
      { new: true, runValidators: true }
    ).select('-password -refreshToken');

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // TODO: Send notification email to user about suspension
    // await sendSuspensionNotificationEmail(user.email, reason);

    return NextResponse.json(
      {
        ...user.toObject(),
        message: 'User account suspended successfully',
      },
      { status: 200 }
    );
  } catch (error) {
    return handleApiError(error, 'Failed to suspend user account');
  }
}
