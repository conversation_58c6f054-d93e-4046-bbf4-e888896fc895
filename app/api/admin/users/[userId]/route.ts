// app/api/admin/users/[userId]/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { User } from '@/models/User';
import { handleApiError } from '@/lib/backend/error-service';

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    await connectToDatabase();

    const user = await User.findById(params.userId)
      .select('-password -refreshToken')
      .lean();

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(user, { status: 200 });
  } catch (error) {
    return handleApiError(error, 'Failed to get user');
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    await connectToDatabase();

    const body = await request.json();
    const { name, email, role, status, suspensionReason, notes } = body;

    // Build update object
    const updateData: any = {
      updatedAt: new Date(),
    };

    if (name !== undefined) updateData.name = name;
    if (email !== undefined) updateData.email = email;
    if (role !== undefined) updateData.role = role;
    if (status !== undefined) updateData.status = status;
    if (suspensionReason !== undefined) updateData.suspensionReason = suspensionReason;
    if (notes !== undefined) updateData.notes = notes;

    // If status is being changed to suspended, add suspension timestamp
    if (status === 'suspended') {
      updateData.suspendedAt = new Date();
    }

    // If status is being changed from suspended to active, clear suspension data
    if (status === 'active') {
      updateData.suspendedAt = null;
      updateData.suspensionReason = null;
    }

    const user = await User.findByIdAndUpdate(
      params.userId,
      updateData,
      { new: true, runValidators: true }
    ).select('-password -refreshToken');

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(user, { status: 200 });
  } catch (error) {
    return handleApiError(error, 'Failed to update user');
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    await connectToDatabase();

    const user = await User.findByIdAndDelete(params.userId);

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { message: 'User deleted successfully' },
      { status: 200 }
    );
  } catch (error) {
    return handleApiError(error, 'Failed to delete user');
  }
}
