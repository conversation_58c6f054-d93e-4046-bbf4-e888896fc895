// app/api/admin/users/[userId]/reset-password/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { User } from '@/models/User';
import { handleApiError } from '@/lib/backend/error-service';
import crypto from 'crypto';

export async function POST(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    await connectToDatabase();

    const user = await User.findById(params.userId);

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Generate password reset token
    const resetToken = crypto.randomBytes(32).toString('hex');
    const resetTokenExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    // Update user with reset token
    await User.findByIdAndUpdate(params.userId, {
      passwordResetToken: resetToken,
      passwordResetExpiry: resetTokenExpiry,
      updatedAt: new Date(),
    });

    // In a real application, you would send an email here
    // For now, we'll just log the reset token and return success
    console.log(`Password reset token for user ${user.email}: ${resetToken}`);

    // TODO: Implement email sending service
    // await sendPasswordResetEmail(user.email, resetToken);

    return NextResponse.json(
      {
        success: true,
        message: 'Password reset email sent successfully',
        // In development, include the token for testing
        ...(process.env.NODE_ENV === 'development' && { resetToken }),
      },
      { status: 200 }
    );
  } catch (error) {
    return handleApiError(error, 'Failed to send password reset email');
  }
}
