// app/api/ws/route.ts

import { NextRequest } from "next/server";
import { WebSocketServer } from 'ws';
import { connectToDatabase } from "@/lib/dbconnect";
import { verifyAccessToken } from "@/lib/auth";
import { User } from "@/models/User";
import { MemberOrder } from "@/models/MemberOrder";
import { StokvelGroup } from "@/models/StokvelGroup";

// Store active WebSocket connections
const clients = new Map<string, any>();

// WebSocket message types
interface WebSocketMessage {
  type: 'dashboard_update' | 'order_update' | 'user_update' | 'group_update' | 'system_status' | 'heartbeat' | 'request_dashboard_update';
  data: any;
  timestamp: string;
}

// Dashboard data aggregation function
async function getDashboardData() {
  try {
    await connectToDatabase();
    
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    // Get basic stats
    const [totalRevenue, totalOrders, totalUsers, totalGroups] = await Promise.all([
      MemberOrder.aggregate([
        {
          $match: {
            createdAt: { $gte: thirtyDaysAgo },
            'paymentInfo.status': 'paid'
          }
        },
        {
          $group: {
            _id: null,
            total: { $sum: '$totalAmount' }
          }
        }
      ]),
      MemberOrder.countDocuments({
        createdAt: { $gte: thirtyDaysAgo }
      }),
      User.countDocuments({
        createdAt: { $gte: thirtyDaysAgo },
        role: { $in: ['customer', 'member'] }
      }),
      StokvelGroup.countDocuments({})
    ]);

    return {
      revenue: totalRevenue[0]?.total || 0,
      orders: totalOrders,
      users: totalUsers,
      groups: totalGroups,
      lastUpdated: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    return null;
  }
}

// Broadcast message to all connected admin clients
function broadcastToAdmins(message: WebSocketMessage) {
  clients.forEach((client, clientId) => {
    if (client.ws.readyState === 1 && client.isAdmin) { // WebSocket.OPEN = 1
      try {
        client.ws.send(JSON.stringify(message));
      } catch (error) {
        console.error(`Error sending message to client ${clientId}:`, error);
        clients.delete(clientId);
      }
    }
  });
}

// Handle WebSocket upgrade
export async function GET(req: NextRequest) {
  // Check if this is a WebSocket upgrade request
  const upgrade = req.headers.get('upgrade');
  if (upgrade !== 'websocket') {
    return new Response('Expected WebSocket upgrade', { status: 426 });
  }

  try {
    // In a real implementation, you'd need to handle the WebSocket upgrade properly
    // This is a simplified version for demonstration
    return new Response('WebSocket endpoint - upgrade handling would be implemented here', {
      status: 200,
      headers: {
        'Content-Type': 'text/plain',
      },
    });
  } catch (error) {
    console.error('WebSocket upgrade error:', error);
    return new Response('WebSocket upgrade failed', { status: 500 });
  }
}

// Since Next.js doesn't natively support WebSocket upgrades in API routes,
// we'll create a polling-based alternative for real-time updates
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { action, token } = body;

    // Verify authentication
    if (!token) {
      return Response.json({ error: 'Authentication required' }, { status: 401 });
    }

    const payload = await verifyAccessToken(token);
    if (!payload) {
      return Response.json({ error: 'Invalid token' }, { status: 401 });
    }

    const user = await User.findById(payload.userId);
    if (!user || user.role !== 'admin') {
      return Response.json({ error: 'Admin access required' }, { status: 403 });
    }

    switch (action) {
      case 'subscribe':
        // In a real WebSocket implementation, this would establish the connection
        const dashboardData = await getDashboardData();
        return Response.json({
          success: true,
          data: dashboardData,
          message: 'Subscribed to real-time updates'
        });

      case 'get_dashboard_data':
        const currentData = await getDashboardData();
        return Response.json({
          success: true,
          data: currentData
        });

      case 'ping':
        return Response.json({
          success: true,
          timestamp: new Date().toISOString(),
          message: 'pong'
        });

      default:
        return Response.json({ error: 'Unknown action' }, { status: 400 });
    }
  } catch (error) {
    console.error('WebSocket API error:', error);
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Server-Sent Events alternative for real-time updates
export async function PUT(req: NextRequest) {
  const token = req.headers.get('authorization')?.replace('Bearer ', '');
  
  if (!token) {
    return new Response('Authentication required', { status: 401 });
  }

  try {
    const payload = await verifyAccessToken(token);
    if (!payload) {
      return new Response('Invalid token', { status: 401 });
    }

    const user = await User.findById(payload.userId);
    if (!user || user.role !== 'admin') {
      return new Response('Admin access required', { status: 403 });
    }

    // Set up Server-Sent Events
    const encoder = new TextEncoder();
    
    const stream = new ReadableStream({
      start(controller) {
        // Send initial connection message
        const initialMessage = `data: ${JSON.stringify({
          type: 'connection',
          message: 'Connected to real-time dashboard updates',
          timestamp: new Date().toISOString()
        })}\n\n`;
        controller.enqueue(encoder.encode(initialMessage));

        // Send dashboard data every 30 seconds
        const interval = setInterval(async () => {
          try {
            const dashboardData = await getDashboardData();
            if (dashboardData) {
              const message = `data: ${JSON.stringify({
                type: 'dashboard_update',
                data: dashboardData,
                timestamp: new Date().toISOString()
              })}\n\n`;
              controller.enqueue(encoder.encode(message));
            }
          } catch (error) {
            console.error('Error sending SSE update:', error);
            const errorMessage = `data: ${JSON.stringify({
              type: 'error',
              message: 'Failed to fetch dashboard data',
              timestamp: new Date().toISOString()
            })}\n\n`;
            controller.enqueue(encoder.encode(errorMessage));
          }
        }, 30000);

        // Clean up on close
        req.signal.addEventListener('abort', () => {
          clearInterval(interval);
          controller.close();
        });
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control',
      },
    });
  } catch (error) {
    console.error('SSE setup error:', error);
    return new Response('Internal server error', { status: 500 });
  }
}

// Utility function to trigger dashboard updates (can be called from other API routes)
export async function triggerDashboardUpdate(updateType: string, data: any) {
  const message: WebSocketMessage = {
    type: updateType as any,
    data,
    timestamp: new Date().toISOString()
  };

  // In a real WebSocket implementation, this would broadcast to all connected clients
  console.log('Dashboard update triggered:', message);
  
  // For now, we'll just log it. In a production environment, you'd:
  // 1. Store the update in a cache/database
  // 2. Broadcast via WebSocket to connected clients
  // 3. Or use a message queue system like Redis pub/sub
}

// Export the trigger function for use in other API routes
export { triggerDashboardUpdate as broadcastDashboardUpdate };
