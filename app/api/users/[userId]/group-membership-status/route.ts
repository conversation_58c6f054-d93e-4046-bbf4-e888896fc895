// app/api/users/[userId]/group-membership-status/route.ts

import { NextRequest, NextResponse } from "next/server";
import { connectToDatabase } from "@/lib/dbconnect";
import { User } from "@/models/User";
import { StokvelGroup } from "@/models/StokvelGroup";
import { ShoppingCart } from "@/models/ShoppingCart";
import { getCorsHeaders } from "@/lib/cors";
import mongoose from "mongoose";
import '@/models'; // Ensure all models are loaded

export interface GroupMembershipStatus {
  hasActiveGroup: boolean;
  activeGroups: {
    _id: string;
    name: string;
    description: string;
    geolocation?: string;
    members: number;
    joinedAt: Date;
    isLatest: boolean;
  }[];
  previousGroups: {
    _id: string;
    name: string;
    description: string;
    geolocation?: string;
    members: number;
    leftAt?: Date;
    hasUndeliveredItems: boolean;
    undeliveredItemsCount: number;
  }[];
  canJoinNewGroup: boolean;
  requiresRelocation: boolean;
  multipleActiveGroups: boolean; // For legacy accounts
}

export async function GET(request: NextRequest) {
  const origin = request.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);

  // Extract userId from URL path
  const url = new URL(request.url);
  const pathParts = url.pathname.split('/');
  const userId = pathParts[pathParts.length - 2];

  try {
    await connectToDatabase();

    if (!userId || !mongoose.Types.ObjectId.isValid(userId)) {
      return NextResponse.json(
        {
          success: false,
          message: "Valid User ID is required."
        },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Find the user with populated groups
    const user = await User.findById(userId).populate({
      path: 'stokvelGroups',
      select: 'name description geolocation members createdAt updatedAt'
    });

    if (!user) {
      return NextResponse.json(
        {
          success: false,
          message: "User not found."
        },
        { headers: corsHeaders, status: 404 }
      );
    }

    const currentGroups = user.stokvelGroups || [];
    const hasActiveGroup = currentGroups.length > 0;
    const multipleActiveGroups = currentGroups.length > 1;

    // Format active groups with join dates
    const activeGroups = currentGroups.map((group: any, index: number) => ({
      _id: group._id.toString(),
      name: group.name,
      description: group.description,
      geolocation: group.geolocation,
      members: group.members.length,
      joinedAt: group.createdAt, // This is approximate - we could track actual join dates
      isLatest: index === currentGroups.length - 1 // Mark the latest joined group
    }));

    // Get previous groups from shopping cart history
    const previousGroupIds = await ShoppingCart.distinct("groupId", {
      user: userId,
      groupId: { $nin: currentGroups.map((g: any) => g._id) }
    });

    // Get details of previous groups
    const previousGroupsData = await StokvelGroup.find({
      _id: { $in: previousGroupIds }
    }).select('name description geolocation members');

    // Check for undelivered items in previous groups
    const previousGroups = await Promise.all(
      previousGroupsData.map(async (group) => {
        const undeliveredItems = await ShoppingCart.find({
          user: userId,
          groupId: group._id,
          status: { $in: ['pending', 'processing'] }
        });

        return {
          _id: group._id.toString(),
          name: group.name,
          description: group.description,
          geolocation: group.geolocation,
          members: group.members.length,
          hasUndeliveredItems: undeliveredItems.length > 0,
          undeliveredItemsCount: undeliveredItems.length
        };
      })
    );

    // Business rules
    const canJoinNewGroup = !hasActiveGroup;
    const requiresRelocation = hasActiveGroup;

    const membershipStatus: GroupMembershipStatus = {
      hasActiveGroup,
      activeGroups,
      previousGroups,
      canJoinNewGroup,
      requiresRelocation,
      multipleActiveGroups
    };

    return NextResponse.json(
      {
        success: true,
        data: membershipStatus
      },
      { headers: corsHeaders, status: 200 }
    );

  } catch (error) {
    console.error("Error fetching group membership status:", error);
    return NextResponse.json(
      {
        success: false,
        message: "An error occurred while fetching group membership status.",
        error: "Internal Server Error"
      },
      { headers: corsHeaders, status: 500 }
    );
  }
}

export async function OPTIONS(req: NextRequest) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}
