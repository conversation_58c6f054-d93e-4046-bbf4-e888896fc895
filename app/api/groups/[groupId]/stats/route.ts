// app/api/groups/[groupId]/stats/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { verifyAccessToken } from '@/lib/auth';
import { StokvelGroup } from '@/models/StokvelGroup';
import { GroupOrder } from '@/models/GroupOrder';
import { MemberOrder } from '@/models/MemberOrder';
import { User } from '@/models/User';
import '@/models'; // Ensure all models are loaded

export async function OPTIONS(req: NextRequest) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function GET(
  request: NextRequest,
  { params }: { params: { groupId: string } }
) {
  const origin = request.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    // Verify authentication - check both Authorization header and cookies
    let token = request.headers.get('authorization')?.replace('Bearer ', '');

    // If no Authorization header, try to get token from cookies
    if (!token) {
      token = request.cookies.get('accessToken')?.value;
    }

    if (!token) {
      return NextResponse.json(
        { error: 'No token provided' },
        { headers: corsHeaders, status: 401 }
      );
    }

    const payload = await verifyAccessToken(token);
    if (!payload) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { headers: corsHeaders, status: 401 }
      );
    }

    await connectToDatabase();

    const groupId = params.groupId;

    // Verify group exists and user is a member
    const group = await StokvelGroup.findById(groupId)
      .populate('members', 'name email')
      .lean();

    if (!group) {
      return NextResponse.json(
        { error: 'Group not found' },
        { headers: corsHeaders, status: 404 }
      );
    }

    // Check if user is a member
    const isMember = group.members.some((member: any) => 
      member._id.toString() === payload.userId
    );

    if (!isMember) {
      return NextResponse.json(
        { error: 'Access denied. You are not a member of this group.' },
        { headers: corsHeaders, status: 403 }
      );
    }

    // Calculate date ranges
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const oneYearAgo = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);

    // Get group orders statistics
    const [groupOrders, memberOrders] = await Promise.all([
      GroupOrder.find({ groupId }).lean(),
      MemberOrder.find({ groupId }).lean()
    ]);

    // Calculate basic statistics
    const totalOrders = groupOrders.length;
    const activeOrders = groupOrders.filter(order => 
      ['draft', 'pending', 'processing'].includes(order.status)
    ).length;
    const completedOrders = groupOrders.filter(order => 
      order.status === 'completed'
    ).length;
    const pendingOrders = groupOrders.filter(order => 
      order.status === 'pending'
    ).length;

    const totalRevenue = memberOrders
      .filter(order => order.paymentInfo?.status === 'paid')
      .reduce((sum, order) => sum + order.totalAmount, 0);

    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

    // Calculate member participation
    const totalMembers = group.members.length;
    const membersWithOrders = new Set(
      memberOrders.map(order => order.userId.toString())
    ).size;
    const memberParticipationRate = totalMembers > 0 ? 
      (membersWithOrders / totalMembers) * 100 : 0;

    // Calculate order completion rate
    const orderCompletionRate = totalOrders > 0 ? 
      (completedOrders / totalOrders) * 100 : 0;

    // Calculate savings progress (using group's totalSales vs a target)
    const savingsTarget = 100000; // Default target, could be configurable
    const totalSavings = group.totalSales || 0;
    const savingsProgress = savingsTarget > 0 ? 
      (totalSavings / savingsTarget) * 100 : 0;

    // Get top spenders
    const memberSpending = memberOrders
      .filter(order => order.paymentInfo?.status === 'paid')
      .reduce((acc, order) => {
        const userId = order.userId.toString();
        if (!acc[userId]) {
          acc[userId] = { totalSpent: 0, orderCount: 0 };
        }
        acc[userId].totalSpent += order.totalAmount;
        acc[userId].orderCount += 1;
        return acc;
      }, {} as Record<string, { totalSpent: number; orderCount: number }>);

    const topSpenders = Object.entries(memberSpending)
      .sort(([, a], [, b]) => b.totalSpent - a.totalSpent)
      .slice(0, 5)
      .map(([userId, data]) => {
        const member = group.members.find((m: any) => m._id.toString() === userId);
        return {
          userId,
          name: member?.name || 'Unknown',
          totalSpent: data.totalSpent
        };
      });

    // Calculate monthly trends (last 12 months)
    const monthlyTrends = [];
    for (let i = 11; i >= 0; i--) {
      const monthStart = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const monthEnd = new Date(now.getFullYear(), now.getMonth() - i + 1, 0);
      
      const monthOrders = memberOrders.filter(order => {
        const orderDate = new Date(order.createdAt);
        return orderDate >= monthStart && orderDate <= monthEnd;
      });

      const monthRevenue = monthOrders
        .filter(order => order.paymentInfo?.status === 'paid')
        .reduce((sum, order) => sum + order.totalAmount, 0);

      monthlyTrends.push({
        month: monthStart.toISOString().substring(0, 7), // YYYY-MM format
        orders: monthOrders.length,
        revenue: monthRevenue,
        members: new Set(monthOrders.map(order => order.userId.toString())).size
      });
    }

    // Get recent activity count (last 7 days)
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const recentActivityCount = memberOrders.filter(order => 
      new Date(order.createdAt) >= sevenDaysAgo
    ).length;

    // Mock online members count (would need real-time service)
    const onlineMembers = Math.floor(totalMembers * 0.3); // Assume 30% online

    const stats = {
      totalMembers,
      onlineMembers,
      totalOrders,
      totalRevenue,
      averageOrderValue,
      activeOrders,
      completedOrders,
      pendingOrders,
      totalSavings,
      savingsTarget,
      savingsProgress: Math.min(savingsProgress, 100),
      memberParticipationRate,
      orderCompletionRate,
      recentActivityCount,
      topSpenders,
      monthlyTrends
    };

    return NextResponse.json(
      {
        success: true,
        data: stats,
        metadata: {
          groupId,
          calculatedAt: now.toISOString(),
          period: '30_days'
        }
      },
      { headers: corsHeaders, status: 200 }
    );

  } catch (error) {
    console.error('Error fetching group stats:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch group statistics',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { headers: corsHeaders, status: 500 }
    );
  }
}
