import { NextRequest, NextResponse } from 'next/server';
import { getGroupOrders, createMemberOrderAndUpdateGroup } from '@/lib/shoppingCartBackendUtilities';
import { ShoppingCart } from '@/models/ShoppingCart';
import { getCorsHeaders } from '@/lib/cors';
import { connectToDatabase } from '@/lib/dbconnect';
import type { Types } from 'mongoose';
import type { IGroupOrder } from '@/models/GroupOrder';
import '@/models'; // Ensure all models are loaded

// Connect to database
connectToDatabase();

// Updated interface to match the actual MongoDB document structure
interface OrderItem {
  product: Types.ObjectId | {
    _id: Types.ObjectId;
    name: string;
    price: number;
  };
  quantity: number;
  userId: Types.ObjectId;
  unitPrice: number;
  subtotal: number;
}

export async function OPTIONS(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function GET(request: NextRequest) {
  const origin = request.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    const url = new URL(request.url);
    const segments = url.pathname.split('/');
    const groupId = segments[segments.length - 2];

    // Get pagination parameters from query string
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const skip = parseInt(url.searchParams.get('skip') || '0');
    const populateProducts = url.searchParams.get('populate') === 'true';

    const orders = await getGroupOrders(groupId, null, {
      limit: Math.min(limit, 100), // Cap at 100 to prevent abuse
      skip,
      lean: true,
      populateProducts
    });

    const transformedOrders = orders.map((order: IGroupOrder) => ({
      ...order.toObject(),
      _id: order._id?.toString(),
      groupId: order.groupId?.toString(),
      orderItems: order.orderItems.map((item: OrderItem) => ({
        ...item,
        userId: item.userId.toString(),
        product: {
          _id: (typeof item.product === 'object' && '_id' in item.product)
            ? item.product._id.toString()
            : (item.product as Types.ObjectId).toString(),
          name: (typeof item.product === 'object' && 'name' in item.product)
            ? item.product.name
            : '',
          price: (typeof item.product === 'object' && 'price' in item.product)
            ? item.product.price
            : 0,
        }
      }))
    }));

    return NextResponse.json(transformedOrders, { headers: corsHeaders });
  } catch (error: unknown) {
    console.error('Error fetching group orders:', error);
    return NextResponse.json(
      { error: 'Failed to fetch group orders' },
      { headers: corsHeaders, status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    const url = new URL(req.url);
    const segments = url.pathname.split('/');
    const groupId = segments[segments.length - 2];

    const { userId, customerInfo, paymentMethod } = await req.json();

    // Validate required fields
    if (!userId || !groupId || !customerInfo) {
      return NextResponse.json(
        { error: "User ID, Group ID, and customer info are required." },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Get the user's cart
    const cart = await ShoppingCart.findOne({ user: userId, groupId })
      .populate({
        path: 'items.product',
        select: 'name price image'
      });

    if (!cart || !cart.items || cart.items.length === 0) {
      return NextResponse.json(
        { error: "Cart is empty or not found." },
        { headers: corsHeaders, status: 404 }
      );
    }

    console.log('Creating order with data:', { userId, groupId, cartItems: cart.items.length, customerInfo });

    // Create member order and update group order
    const { memberOrder, groupOrder } = await createMemberOrderAndUpdateGroup(
      userId,
      groupId,
      cart,
      customerInfo,
      paymentMethod
    );

    console.log('Order creation result:', {
      memberOrderCreated: !!memberOrder,
      groupOrderCreated: !!groupOrder,
      memberOrderId: memberOrder?._id,
      groupOrderId: groupOrder?._id
    });

    if (!groupOrder || !memberOrder) {
      console.error('Order creation failed:', { groupOrder: !!groupOrder, memberOrder: !!memberOrder });
      return NextResponse.json(
        { error: "Failed to create member order and group order." },
        { headers: corsHeaders, status: 500 }
      );
    }

    return NextResponse.json({
      message: "Order created successfully",
      groupOrder,
      memberOrder,
      orderId: groupOrder._id,
      memberOrderId: memberOrder._id,
      memberOrderNumber: memberOrder.orderNumber
    }, {
      headers: corsHeaders,
      status: 201,
    });
  } catch (error) {
    console.error("Failed to create GroupOrder:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { headers: corsHeaders, status: 500 }
    );
  }
}