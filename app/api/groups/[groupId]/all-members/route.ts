// app/api/groups/[groupId]/all-members/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCorsHeaders } from '@/lib/cors';
import { connectToDatabase } from "@/lib/dbconnect";
import { User } from '@/models/User';
import { StokvelGroup } from '@/models/StokvelGroup';
import { MemberOrder } from '@/models/MemberOrder';
import { verifyAccessToken } from '@/lib/auth';
import mongoose from 'mongoose';

export async function OPTIONS(req: NextRequest) {
    const origin = req.headers.get('origin');
    const corsHeaders = getCorsHeaders(origin);
    return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function GET(
    request: NextRequest,
    { params }: { params: { groupId: string } }
) {
    const origin = request.headers.get('origin');
    const corsHeaders = getCorsHeaders(origin);

    try {
        // Verify authentication - check both Authorization header and cookies
        let token = request.headers.get('authorization')?.replace('Bearer ', '');

        // If no Authorization header, try to get token from cookies
        if (!token) {
            token = request.cookies.get('accessToken')?.value;
        }

        console.log('GroupMembers API - Token found:', !!token);
        console.log('GroupMembers API - Cookies:', request.cookies.getAll().map(c => c.name));

        if (!token) {
            console.log('GroupMembers API - No token provided');
            return NextResponse.json(
                { error: 'No token provided' },
                { headers: corsHeaders, status: 401 }
            );
        }

        const payload = await verifyAccessToken(token);
        if (!payload) {
            console.log('GroupMembers API - Invalid token');
            return NextResponse.json(
                { error: 'Invalid token' },
                { headers: corsHeaders, status: 401 }
            );
        }

        console.log('GroupMembers API - Authentication successful, userId:', payload.userId);

        const { groupId } = params;

        if (!groupId) {
            return NextResponse.json(
                { error: 'Group ID is required.' },
                { headers: corsHeaders, status: 400 }
            );
        }

        await connectToDatabase();

        // Find the group and populate member details
        const group = await StokvelGroup.findById(groupId)
            .populate({
                path: 'members',
                select: 'name email phone createdAt',
                model: 'User'
            })
            .select('members admin')
            .lean();

        if (!group) {
            return NextResponse.json(
                { error: 'Group not found.' },
                { headers: corsHeaders, status: 404 }
            );
        }

        // Check if the requesting user is a member of the group
        const isUserMember = group.members.some((member: any) =>
            member._id.toString() === payload.userId
        );

        if (!isUserMember) {
            return NextResponse.json(
                { error: 'You are not a member of this group.' },
                { headers: corsHeaders, status: 403 }
            );
        }

        // Get member order statistics
        const memberIds = group.members.map((member: any) => member._id);

        // Aggregate member order data
        const memberOrderStats = await MemberOrder.aggregate([
            {
                $match: {
                    userId: { $in: memberIds },
                    groupId: new mongoose.Types.ObjectId(groupId)
                }
            },
            {
                $group: {
                    _id: '$userId',
                    totalOrders: { $sum: 1 },
                    totalSpent: {
                        $sum: {
                            $cond: [
                                { $eq: ['$paymentInfo.status', 'paid'] },
                                '$totalAmount',
                                0
                            ]
                        }
                    }
                }
            }
        ]);

        // Create a map for quick lookup
        const orderStatsMap = new Map();
        memberOrderStats.forEach(stat => {
            orderStatsMap.set(stat._id.toString(), {
                totalOrders: stat.totalOrders,
                totalSpent: stat.totalSpent
            });
        });

        // Format member data for the frontend
        const formattedMembers = group.members.map((member: any) => {
            const stats = orderStatsMap.get(member._id.toString()) || { totalOrders: 0, totalSpent: 0 };

            return {
                userId: member._id.toString(),
                name: member.name || 'N/A',
                email: member.email || 'N/A',
                phone: member.phone || 'N/A',
                totalOrders: stats.totalOrders.toString(),
                totalSpent: `R${stats.totalSpent.toFixed(2)}`,
                joinedAt: member.createdAt ? member.createdAt.toISOString() : new Date().toISOString(),
                isOnline: false, // TODO: Implement real-time online status
                lastSeen: member.lastSeen || null
            };
        });

        return NextResponse.json(
            formattedMembers,
            { headers: corsHeaders, status: 200 }
        );

    } catch (error) {
        console.error('Failed to fetch group members:', error);
        return NextResponse.json(
            { error: 'Failed to fetch group members.' },
            { headers: corsHeaders, status: 500 }
        );
    }
}