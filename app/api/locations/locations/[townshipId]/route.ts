// app/api/locations/locations/[townshipId]/route.ts

import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import {
  getLocationsByTownship,
  createLocation,
  getLocationById,
  updateLocation,
  deleteLocation,
  checkLocationExists,
  suggestLocationNames
} from '@/lib/locationUtilities';
import '@/models'; // Ensure all models are loaded

// Connect to database when the module is loaded
connectToDatabase();

export async function OPTIONS(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ townshipId: string }> }
) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    await connectToDatabase();
    const { townshipId } = await params;

    const { searchParams } = new URL(req.url);
    const locationId = searchParams.get('id');

    if (locationId) {
      // Get specific location
      const location = await getLocationById(locationId);

      if (!location) {
        return NextResponse.json(
          { error: 'Location not found' },
          { headers: corsHeaders, status: 404 }
        );
      }

      return NextResponse.json({ location }, {
        headers: corsHeaders,
        status: 200
      });
    } else {
      // Get all locations for the township
      const locations = await getLocationsByTownship(townshipId);
      
      return NextResponse.json({ locations }, { 
        headers: corsHeaders, 
        status: 200 
      });
    }
  } catch (error) {
    console.error('Error fetching locations:', error);
    return NextResponse.json(
      { error: 'Failed to fetch locations' },
      { headers: corsHeaders, status: 500 }
    );
  }
}

export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ townshipId: string }> }
) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    await connectToDatabase();
    const { townshipId } = await params;
    const { name, description } = await req.json();

    // Validation
    if (!name) {
      return NextResponse.json(
        { error: 'Name is required' },
        { headers: corsHeaders, status: 400 }
      );
    }

    if (name.length < 2 || name.length > 100) {
      return NextResponse.json(
        { error: 'Name must be between 2 and 100 characters' },
        { headers: corsHeaders, status: 400 }
      );
    }

    if (description && description.length > 500) {
      return NextResponse.json(
        { error: 'Description must be less than 500 characters' },
        { headers: corsHeaders, status: 400 }
      );
    }

    const location = await createLocation(
      name.trim(),
      townshipId,
      description?.trim()
    );
    return NextResponse.json({ location }, {
      headers: corsHeaders,
      status: 201
    });
  } catch (error: any) {
    console.error('Error creating location:', error);
    console.error('Error details:', {
      message: error.message,
      code: error.code,
      stack: error.stack,
      name: error.name
    });

    // Handle duplicate key error
    if (error.code === 11000) {
      // Generate suggestions for alternative names
      const suggestions = await suggestLocationNames(name.trim(), townshipId);

      return NextResponse.json(
        {
          error: 'Location name already exists in this township',
          suggestions: suggestions,
          duplicateName: name.trim()
        },
        { headers: corsHeaders, status: 409 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create location', details: error.message },
      { headers: corsHeaders, status: 500 }
    );
  }
}

export async function PUT(
  req: NextRequest,
  { params }: { params: { townshipId: string } }
) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  
  try {
    await connectToDatabase();
    const { id, name, description, isActive } = await req.json();
    
    if (!id) {
      return NextResponse.json(
        { error: 'Location ID is required' },
        { headers: corsHeaders, status: 400 }
      );
    }
    
    const updateData: any = {};
    if (name !== undefined) updateData.name = name.trim();
    if (description !== undefined) updateData.description = description?.trim();
    if (isActive !== undefined) updateData.isActive = isActive;
    
    const location = await updateLocation(id, updateData);
    
    if (!location) {
      return NextResponse.json(
        { error: 'Location not found' },
        { headers: corsHeaders, status: 404 }
      );
    }
    
    return NextResponse.json({ location }, { 
      headers: corsHeaders, 
      status: 200 
    });
  } catch (error: any) {
    console.error('Error updating location:', error);
    
    // Handle duplicate key error
    if (error.code === 11000) {
      return NextResponse.json(
        { error: 'Location name already exists in this township' },
        { headers: corsHeaders, status: 409 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to update location' },
      { headers: corsHeaders, status: 500 }
    );
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: { townshipId: string } }
) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  
  try {
    await connectToDatabase();
    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json(
        { error: 'Location ID is required' },
        { headers: corsHeaders, status: 400 }
      );
    }
    
    const location = await deleteLocation(id);
    
    if (!location) {
      return NextResponse.json(
        { error: 'Location not found' },
        { headers: corsHeaders, status: 404 }
      );
    }
    
    return NextResponse.json(
      { message: 'Location deleted successfully', location }, 
      { headers: corsHeaders, status: 200 }
    );
  } catch (error) {
    console.error('Error deleting location:', error);
    return NextResponse.json(
      { error: 'Failed to delete location' },
      { headers: corsHeaders, status: 500 }
    );
  }
}
