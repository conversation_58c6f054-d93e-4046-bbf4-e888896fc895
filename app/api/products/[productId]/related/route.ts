// app/api/products/[productId]/related/route.ts

import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { getRelatedProducts } from '@/lib/ratingUtilities';
import '@/models'; // Ensure all models are loaded

// Connect to database when the module is loaded
connectToDatabase();

export async function OPTIONS(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function GET(
  req: Request,
  { params }: { params: { productId: string } }
) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    const { productId } = await params;
    const url = new URL(req.url);
    const limit = parseInt(url.searchParams.get('limit') || '6');

    if (!productId) {
      return NextResponse.json(
        { error: 'Product ID is required.' },
        { headers: corsHeaders, status: 400 }
      );
    }

    const relatedProducts = await getRelatedProducts(productId, limit);

    return NextResponse.json(
      { 
        products: relatedProducts,
        count: relatedProducts.length
      },
      { headers: corsHeaders, status: 200 }
    );
  } catch (error) {
    console.error('Failed to fetch related products:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}
