// app/api/products/[productId]/ratings/route.ts

import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { 
  createProductRating,
  getProductRatings,
  getProductRatingSummary,
  getUserProductRating
} from '@/lib/ratingUtilities';
import '@/models'; // Ensure all models are loaded

// Connect to database when the module is loaded
connectToDatabase();

export async function OPTIONS(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function GET(
  req: NextRequest,
  { params }: { params: { productId: string } }
) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    const { productId } = await params;
    const userId = req.nextUrl.searchParams.get('userId');
    const page = parseInt(req.nextUrl.searchParams.get('page') || '1');
    const limit = parseInt(req.nextUrl.searchParams.get('limit') || '10');
    const sortBy = req.nextUrl.searchParams.get('sortBy') as 'newest' | 'oldest' | 'highest' | 'lowest' | 'helpful' || 'newest';
    const summary = req.nextUrl.searchParams.get('summary');
    const userRating = req.nextUrl.searchParams.get('userRating');

    if (!productId) {
      return NextResponse.json(
        { error: 'Product ID is required.' },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Get user's specific rating for this product
    if (userRating === 'true' && userId) {
      const rating = await getUserProductRating(userId, productId);
      return NextResponse.json({ rating }, { headers: corsHeaders, status: 200 });
    }

    // Get rating summary
    if (summary === 'true') {
      const ratingSummary = await getProductRatingSummary(productId);
      if (!ratingSummary) {
        return NextResponse.json(
          { error: 'Failed to get rating summary.' },
          { headers: corsHeaders, status: 500 }
        );
      }
      return NextResponse.json(ratingSummary, { headers: corsHeaders, status: 200 });
    }

    // Get paginated ratings
    const result = await getProductRatings(productId, page, limit, sortBy);
    if (!result) {
      return NextResponse.json(
        { error: 'Failed to fetch ratings.' },
        { headers: corsHeaders, status: 500 }
      );
    }

    return NextResponse.json(result, { headers: corsHeaders, status: 200 });
  } catch (error) {
    console.error('Failed to fetch product ratings:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}

export async function POST(
  req: NextRequest,
  { params }: { params: { productId: string } }
) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    const { productId } = params;
    const body = await req.json();
    const { userId, rating, review, title } = body;

    if (!productId || !userId || !rating) {
      return NextResponse.json(
        { error: 'Product ID, User ID, and rating are required.' },
        { headers: corsHeaders, status: 400 }
      );
    }

    if (rating < 1 || rating > 5) {
      return NextResponse.json(
        { error: 'Rating must be between 1 and 5.' },
        { headers: corsHeaders, status: 400 }
      );
    }

    const productRating = await createProductRating({
      userId,
      productId,
      rating,
      review,
      title
    });

    if (!productRating) {
      return NextResponse.json(
        { error: 'Failed to create rating. You may have already rated this product.' },
        { headers: corsHeaders, status: 400 }
      );
    }

    return NextResponse.json(
      { 
        message: 'Rating created successfully',
        rating: productRating
      },
      { headers: corsHeaders, status: 201 }
    );
  } catch (error) {
    console.error('Failed to create product rating:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}
