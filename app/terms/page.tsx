// app/terms/page.tsx
"use client"

import { motion } from "framer-motion"
import { FileText, Users, ShoppingCart, Shield, AlertTriangle, Mail } from "lucide-react"

export default function TermsOfServicePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      {/* Header */}
      <div className="bg-gradient-to-r from-[#2A7C6C] to-[#1E5A4F] text-white py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <FileText className="h-16 w-16 mx-auto mb-6 text-[#7FDBCA]" />
            <h1 className="text-4xl md:text-5xl font-bold mb-4" style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}>
              Terms of Service
            </h1>
            <p className="text-xl text-white/90 max-w-2xl mx-auto" style={{ fontFamily: "Avenir, sans-serif" }}>
              Please read these terms carefully before using our platform.
            </p>
          </motion.div>
        </div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="prose prose-lg max-w-none"
          >
            <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
              <div className="flex items-center mb-6">
                <Users className="h-6 w-6 text-[#2A7C6C] mr-3" />
                <h2 className="text-2xl font-bold text-gray-900" style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}>
                  Acceptance of Terms
                </h2>
              </div>
              <div className="text-gray-700 space-y-4" style={{ fontFamily: "Avenir, sans-serif" }}>
                <p>By accessing and using the Stokvel platform, you accept and agree to be bound by the terms and provision of this agreement.</p>
                <p>If you do not agree to abide by the above, please do not use this service.</p>
              </div>
            </div>

            <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
              <div className="flex items-center mb-6">
                <ShoppingCart className="h-6 w-6 text-[#2A7C6C] mr-3" />
                <h2 className="text-2xl font-bold text-gray-900" style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}>
                  Use of Platform
                </h2>
              </div>
              <div className="text-gray-700 space-y-4" style={{ fontFamily: "Avenir, sans-serif" }}>
                <p>You may use our platform to:</p>
                <ul className="list-disc pl-6 space-y-2">
                  <li>Join or create stokvel groups</li>
                  <li>Purchase products through group orders</li>
                  <li>Communicate with other group members</li>
                  <li>Access bulk pricing and discounts</li>
                </ul>
                <p>You agree not to use the platform for any unlawful or prohibited activities.</p>
              </div>
            </div>

            <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
              <div className="flex items-center mb-6">
                <Shield className="h-6 w-6 text-[#2A7C6C] mr-3" />
                <h2 className="text-2xl font-bold text-gray-900" style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}>
                  User Responsibilities
                </h2>
              </div>
              <div className="text-gray-700 space-y-4" style={{ fontFamily: "Avenir, sans-serif" }}>
                <p>As a user of our platform, you are responsible for:</p>
                <ul className="list-disc pl-6 space-y-2">
                  <li>Providing accurate and up-to-date information</li>
                  <li>Maintaining the security of your account</li>
                  <li>Respecting other users and community guidelines</li>
                  <li>Making timely payments for your orders</li>
                  <li>Complying with all applicable laws and regulations</li>
                </ul>
              </div>
            </div>

            <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
              <div className="flex items-center mb-6">
                <AlertTriangle className="h-6 w-6 text-[#2A7C6C] mr-3" />
                <h2 className="text-2xl font-bold text-gray-900" style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}>
                  Limitation of Liability
                </h2>
              </div>
              <div className="text-gray-700 space-y-4" style={{ fontFamily: "Avenir, sans-serif" }}>
                <p>Stokvel shall not be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses.</p>
                <p>Our total liability to you for all claims arising from or relating to the service shall not exceed the amount you paid us in the twelve months preceding the claim.</p>
              </div>
            </div>

            <div className="bg-white rounded-2xl shadow-lg p-8">
              <div className="flex items-center mb-6">
                <Mail className="h-6 w-6 text-[#2A7C6C] mr-3" />
                <h2 className="text-2xl font-bold text-gray-900" style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}>
                  Contact Information
                </h2>
              </div>
              <div className="text-gray-700 space-y-4" style={{ fontFamily: "Avenir, sans-serif" }}>
                <p>If you have any questions about these Terms of Service, please contact us:</p>
                <div className="bg-gray-50 rounded-lg p-4">
                  <p><strong>Email:</strong> <EMAIL></p>
                  <p><strong>Phone:</strong> +27 11 456 7890</p>
                  <p><strong>Address:</strong> 123 Community Street, Johannesburg, Gauteng 2000</p>
                </div>
                <p className="text-sm text-gray-600">Last updated: {new Date().toLocaleDateString()}</p>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  )
}
