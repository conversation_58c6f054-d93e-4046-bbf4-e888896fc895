"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { JoinGroupModal } from "@/components/modals/JoinGroupModal";

export default function TestJoinModalPage() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [lastResult, setLastResult] = useState<string | null>(null);

  const handleSuccess = (groupId?: string) => {
    console.log("Join success:", groupId);
    setLastResult(groupId ? `Joined group: ${groupId}` : "Join completed");
    setIsModalOpen(false);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-2xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Test Join Group Modal</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-gray-600">
              Test the new location selection modal system with the join group wizard.
            </p>
            
            <Button 
              onClick={() => setIsModalOpen(true)}
              className="w-full"
            >
              Open Join Group Modal
            </Button>

            {lastResult && (
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <p className="text-green-800 font-medium">Last Result:</p>
                <p className="text-green-700">{lastResult}</p>
              </div>
            )}

            <div className="space-y-2 text-sm text-gray-600">
              <h4 className="font-medium">Test Flow:</h4>
              <ol className="list-decimal list-inside space-y-1">
                <li>Enter your email address</li>
                <li>Select your location using the new modal</li>
                <li>Choose a group from the filtered results</li>
                <li>Complete registration or login</li>
                <li>Verify the group joining works</li>
              </ol>
            </div>

            <div className="space-y-2 text-sm text-gray-600">
              <h4 className="font-medium">Expected Behavior:</h4>
              <ul className="list-disc list-inside space-y-1">
                <li>Location modal should open above the main modal</li>
                <li>No z-index issues or hidden dropdowns</li>
                <li>Mobile-first responsive design</li>
                <li>Smooth animations and transitions</li>
                <li>Continue button should work after group selection</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        <JoinGroupModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onSuccess={handleSuccess}
        />
      </div>
    </div>
  );
}
