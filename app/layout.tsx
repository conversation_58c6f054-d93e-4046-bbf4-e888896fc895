import { Inter } from "next/font/google"
import "./globals.css"
import type { Metadata, Viewport } from "next"
import { AuthProvider } from "@/context/AuthContext"
import { AuthWrapper } from "@/components/AuthWrapper"
import { ChatBot } from "@/components/chat/ChatBot"
import { CartNotification } from "@/components/cart/CartNotification"
import type React from "react"
import { Toaster } from 'sonner';
import ClientProviders from "@/components/ClientProviders"
import { ReduxProvider } from "./providers/ReduxProvider"
import { ChunkErrorHandler } from "@/components/ui/ChunkErrorHandler"

const inter = Inter({ subsets: ["latin"] })

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
}

export const metadata: Metadata = {
  title: {
    default: "Stokvel Market - Community-Powered Grocery Shopping",
    template: "%s | Stokvel Market",
  },
  description:
    "Join Stokvel Market, South Africa's premier community-driven grocery marketplace. Shop together, save more with our unique stockvel-inspired platform.",
  keywords: ["Stokvel Market", "Grocery", "Community Shopping", "South Africa", "Group Buying"],
  authors: [{ name: "Stokvel Market Team" }],
  creator: "Stokvel Market",
  publisher: "Stokvel Market Inc.",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  openGraph: {
    type: "website",
    locale: "en_ZA",
    url: "https://www.stokvel.co.za",
    siteName: "Stokvel",
    title: "Stokvel - Revolutionizing Grocery Shopping in Gauteng",
    description:
      "Experience the power of community shopping with Stokvel. Join shopping zones, unlock bulk discounts, and transform your grocery experience.",
    images: [
      {
        url: "https://www.stokvel.co.za/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Stokvel - Community-Powered Grocery Shopping",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Stokvel - Community-Powered Grocery Shopping",
    description: "Join Stokvel and revolutionize your grocery shopping experience in Gauteng. Shop smarter, together.",
    images: ["https://www.stokvel.co.za/twitter-image.jpg"],
    creator: "@StokvelSA",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  icons: {
    icon: "/favicon.ico",
    shortcut: "/favicon-16x16.png",
    apple: "/apple-touch-icon.png",
  },
  manifest: "/site.webmanifest",
  verification: {
    google: "google-site-verification-code",
    yandex: "yandex-verification-code",
  },
}




export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <head>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fontsource/avenir@4.5.0/index.min.css" />
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fontsource/clash-display@4.5.0/index.min.css" />
        <link rel="manifest" href="/site.webmanifest" />
      </head>
      <body className={inter.className}>
      <ReduxProvider>
        <ClientProviders>
          <AuthProvider>
            <AuthWrapper>
              <ChunkErrorHandler />
              {children}
              <ChatBot />
              <CartNotification />
            </AuthWrapper>
          </AuthProvider>
          <Toaster richColors position="top-right" />
        </ClientProviders>
      </ReduxProvider>
      </body>
    </html>
  )
}


