// app/privacy/page.tsx
"use client"

import { motion } from "framer-motion"
import { Shield, Eye, Lock, Users, FileText, Mail } from "lucide-react"

export default function PrivacyPolicyPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      {/* Header */}
      <div className="bg-gradient-to-r from-[#2A7C6C] to-[#1E5A4F] text-white py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <Shield className="h-16 w-16 mx-auto mb-6 text-[#7FDBCA]" />
            <h1 className="text-4xl md:text-5xl font-bold mb-4" style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}>
              Privacy Policy
            </h1>
            <p className="text-xl text-white/90 max-w-2xl mx-auto" style={{ fontFamily: "Avenir, sans-serif" }}>
              Your privacy is important to us. Learn how we protect and handle your personal information.
            </p>
          </motion.div>
        </div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="prose prose-lg max-w-none"
          >
            <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
              <div className="flex items-center mb-6">
                <FileText className="h-6 w-6 text-[#2A7C6C] mr-3" />
                <h2 className="text-2xl font-bold text-gray-900" style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}>
                  Information We Collect
                </h2>
              </div>
              <div className="text-gray-700 space-y-4" style={{ fontFamily: "Avenir, sans-serif" }}>
                <p>We collect information you provide directly to us, such as when you:</p>
                <ul className="list-disc pl-6 space-y-2">
                  <li>Create an account or join a stokvel group</li>
                  <li>Make purchases through our platform</li>
                  <li>Contact us for support</li>
                  <li>Subscribe to our newsletter</li>
                </ul>
              </div>
            </div>

            <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
              <div className="flex items-center mb-6">
                <Eye className="h-6 w-6 text-[#2A7C6C] mr-3" />
                <h2 className="text-2xl font-bold text-gray-900" style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}>
                  How We Use Your Information
                </h2>
              </div>
              <div className="text-gray-700 space-y-4" style={{ fontFamily: "Avenir, sans-serif" }}>
                <p>We use the information we collect to:</p>
                <ul className="list-disc pl-6 space-y-2">
                  <li>Provide and improve our services</li>
                  <li>Process transactions and send confirmations</li>
                  <li>Communicate with you about your account</li>
                  <li>Send you marketing communications (with your consent)</li>
                  <li>Ensure platform security and prevent fraud</li>
                </ul>
              </div>
            </div>

            <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
              <div className="flex items-center mb-6">
                <Lock className="h-6 w-6 text-[#2A7C6C] mr-3" />
                <h2 className="text-2xl font-bold text-gray-900" style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}>
                  Data Security
                </h2>
              </div>
              <div className="text-gray-700 space-y-4" style={{ fontFamily: "Avenir, sans-serif" }}>
                <p>We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>
                <p>Your payment information is processed securely through encrypted connections and we never store your full payment details on our servers.</p>
              </div>
            </div>

            <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
              <div className="flex items-center mb-6">
                <Users className="h-6 w-6 text-[#2A7C6C] mr-3" />
                <h2 className="text-2xl font-bold text-gray-900" style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}>
                  Information Sharing
                </h2>
              </div>
              <div className="text-gray-700 space-y-4" style={{ fontFamily: "Avenir, sans-serif" }}>
                <p>We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except:</p>
                <ul className="list-disc pl-6 space-y-2">
                  <li>To trusted service providers who assist us in operating our platform</li>
                  <li>When required by law or to protect our rights</li>
                  <li>In connection with a business transfer or merger</li>
                </ul>
              </div>
            </div>

            <div className="bg-white rounded-2xl shadow-lg p-8">
              <div className="flex items-center mb-6">
                <Mail className="h-6 w-6 text-[#2A7C6C] mr-3" />
                <h2 className="text-2xl font-bold text-gray-900" style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}>
                  Contact Us
                </h2>
              </div>
              <div className="text-gray-700 space-y-4" style={{ fontFamily: "Avenir, sans-serif" }}>
                <p>If you have any questions about this Privacy Policy, please contact us:</p>
                <div className="bg-gray-50 rounded-lg p-4">
                  <p><strong>Email:</strong> <EMAIL></p>
                  <p><strong>Phone:</strong> +27 11 456 7890</p>
                  <p><strong>Address:</strong> 123 Community Street, Johannesburg, Gauteng 2000</p>
                </div>
                <p className="text-sm text-gray-600">Last updated: {new Date().toLocaleDateString()}</p>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  )
}
