"use client";

import { useState, useCallback, useEffect } from "react";
import { ProductGrid } from "@/components/store/ProductGrid";
import { ProductFilters } from "@/components/store/ProductFilters";
import { CategoryList } from "@/components/store/CategoryList";
import { StoreBanner } from "@/components/store/StoreBanner";
import { Pagination } from "@/components/store/Pagination";
import { useGetAllProductsQuery } from "@/lib/redux/features/products/productsApiSlice";
import { useGetCategoriesQuery } from "@/lib/redux/features/categories/categoriesApiSlice";
import { useGetWishlistSummaryQuery } from "@/lib/redux/features/wishlist/wishlistApiSlice";
import { useAuth } from "@/context/AuthContext";
import type { Product } from "@/types/product";
import { motion } from "framer-motion";
import { Search, Grid3X3, List, Filter, Heart, Star } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { StoreWishlistPanel } from "@/components/store/StoreWishlistPanel";

export default function StorePage() {
  const { user } = useAuth();
  const { data: products = [], isLoading: productsLoading } = useGetAllProductsQuery();
  const { data: categories = [], isLoading: categoriesLoading } = useGetCategoriesQuery();
  const { data: wishlistSummary } = useGetWishlistSummaryQuery(user?._id || '', { skip: !user?._id });
  const isLoading = productsLoading || categoriesLoading;

  // UI State
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [showWishlistPanel, setShowWishlistPanel] = useState(false);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const productsPerPage = 12; // Increased for better grid layout

  const [filters, setFilters] = useState({
    category: "All",  // Set default category to "All"
    minPrice: 0,
    maxPrice: Infinity,
    inStock: false,
    rating: null as number | null,
    sortBy: '' as 'newest' | 'price-low-high' | 'price-high-low' | 'popular' | '',
    brands: [] as string[],
    showWishlistOnly: false,
  });

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [filters]);

  // Get active filter count for badge
  const getActiveFilterCount = useCallback(() => {
    let count = 0;
    if (filters.category !== "All") count++;
    if (filters.minPrice > 0 || filters.maxPrice !== Infinity) count++;
    if (filters.inStock) count++;
    if (filters.rating) count++;
    if (filters.brands.length > 0) count++;
    if (filters.showWishlistOnly) count++;
    if (searchQuery.trim()) count++;
    return count;
  }, [filters, searchQuery]);

  // Memoize the filtered products to avoid unnecessary re-renders
  const filteredProducts = useCallback(() => {
    // First, filter the products based on the criteria
    let filtered = (products as Product[]).filter((product: Product) => {
      // Search query filter
      if (searchQuery.trim()) {
        const query = searchQuery.toLowerCase();
        const nameMatch = product.name.toLowerCase().includes(query);
        const descriptionMatch = product.description?.toLowerCase().includes(query);
        if (!nameMatch && !descriptionMatch) return false;
      }

      // Handle different possible category structures
      let categoryMatch = filters.category === "All";

      if (filters.category && filters.category !== "All" && product.category) {
        if (typeof product.category === 'object' && '_id' in product.category) {
          categoryMatch = product.category._id === filters.category;
        } else if (typeof product.category === 'string') {
          categoryMatch = product.category === filters.category;
        }
      }

      const priceMatch =
        product.price >= filters.minPrice &&
        product.price <= (filters.maxPrice || Infinity);

      const stockMatch = !filters.inStock || (product.stock !== undefined && product.stock > 0);

      // Rating filter
      const productRating = typeof product.averageRating === 'number' ? product.averageRating : 0;
      const ratingMatch = !filters.rating || (productRating >= (filters.rating || 0));

      // Brand filter
      const productBrand = typeof product.brand === 'string' ? product.brand : '';
      const brandMatch = filters.brands.length === 0 || filters.brands.includes(productBrand);

      // Wishlist filter
      let wishlistMatch = true;
      if (filters.showWishlistOnly && user && wishlistSummary) {
        // Check if product is in any of the user's wishlists
        // This is a simplified check - in a real implementation, you'd want to check against actual wishlist data
        wishlistMatch = false; // For now, this will be handled by the wishlist data
      }

      return categoryMatch && priceMatch && stockMatch && ratingMatch && brandMatch && wishlistMatch;
    });

    // Apply sorting
    if (filters.sortBy) {
      filtered = [...filtered].sort((a: Product, b: Product) => {
        switch (filters.sortBy) {
          case 'newest':
            return new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime();
          case 'price-low-high':
            return a.price - b.price;
          case 'price-high-low':
            return b.price - a.price;
          case 'popular':
            const ratingA = typeof a.averageRating === 'number' ? a.averageRating : 0;
            const ratingB = typeof b.averageRating === 'number' ? b.averageRating : 0;
            return ratingB - ratingA;
          default:
            return 0;
        }
      });
    }

    return filtered;
  }, [products, filters, searchQuery, user, wishlistSummary]);

  // Get current products for pagination
  const getCurrentPageProducts = useCallback(() => {
    const allFilteredProducts = filteredProducts();
    const indexOfLastProduct = currentPage * productsPerPage;
    const indexOfFirstProduct = indexOfLastProduct - productsPerPage;
    return allFilteredProducts.slice(indexOfFirstProduct, indexOfLastProduct);
  }, [filteredProducts, currentPage, productsPerPage]);

  // Calculate total pages
  const totalPages = Math.ceil(filteredProducts().length / productsPerPage);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-purple-50/30">
      <div className="container mx-auto px-4 py-8">
        {/* Modern Header Section - Mobile Optimized */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6 md:mb-8"
        >
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 md:gap-6 mb-4 md:mb-6">
            <div>
              <h1 className="text-2xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-1 md:mb-2"
                  style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}>
                Store
              </h1>
              <p className="text-gray-600 text-sm md:text-lg">Discover amazing products for your stokvel group</p>

              {/* Wishlist Quick Stats */}
              {user && wishlistSummary && wishlistSummary.totalItems > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                  className="mt-4 flex items-center gap-4 text-sm"
                >
                  <button
                    onClick={() => setShowWishlistPanel(true)}
                    className="flex items-center gap-2 bg-red-50 text-red-700 px-3 py-1 rounded-full hover:bg-red-100 transition-colors cursor-pointer"
                  >
                    <Heart className="h-4 w-4 fill-current" />
                    <span>{wishlistSummary.totalItems} items in wishlist</span>
                  </button>
                  {wishlistSummary.topPriority.length > 0 && (
                    <div className="flex items-center gap-2 bg-yellow-50 text-yellow-700 px-3 py-1 rounded-full">
                      <Star className="h-4 w-4 fill-current" />
                      <span>{wishlistSummary.topPriority.length} high priority</span>
                    </div>
                  )}
                </motion.div>
              )}
            </div>

            {/* Search and View Controls - Mobile Optimized */}
            <div className="flex flex-col gap-3 md:flex-row md:gap-4 lg:w-auto w-full">
              <div className="relative flex-1 lg:w-80">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className={`pl-10 h-10 md:h-11 transition-all duration-200 ${
                    searchQuery.trim()
                      ? 'border-purple-400 focus:border-purple-500 focus:ring-purple-300 bg-purple-50/50'
                      : 'border-gray-200 focus:border-purple-300 focus:ring-purple-200'
                  }`}
                />
              </div>

              <div className="flex gap-2 justify-between md:justify-start">
                <div className="flex gap-2">
                  {/* Wishlist Button */}
                  {user && (
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => setShowWishlistPanel(true)}
                      className="h-10 w-10 md:h-11 md:w-11 relative"
                    >
                      <Heart className="h-4 w-4" />
                      {wishlistSummary && wishlistSummary.totalItems > 0 && (
                        <Badge
                          variant="destructive"
                          className="absolute -top-1 -right-1 md:-top-2 md:-right-2 h-4 w-4 md:h-5 md:w-5 p-0 text-xs flex items-center justify-center"
                        >
                          {wishlistSummary.totalItems > 99 ? '99+' : wishlistSummary.totalItems}
                        </Badge>
                      )}
                    </Button>
                  )}

                  {/* Filter toggle button removed - filters are now hidden on mobile completely */}
                </div>

                {/* Sort dropdown - hidden on mobile, shown on tablet+ */}
                <div className="hidden sm:block">
                  <Select value={filters.sortBy || "default"} onValueChange={(value) => setFilters(prev => ({ ...prev, sortBy: value === "default" ? "" : value as 'newest' | 'price-low-high' | 'price-high-low' | 'popular' }))}>
                    <SelectTrigger className="w-32 md:w-40 h-10 md:h-11">
                      <SelectValue placeholder="Sort by" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="default">Default</SelectItem>
                      <SelectItem value="newest">Newest</SelectItem>
                      <SelectItem value="price-low-high">Price: Low to High</SelectItem>
                      <SelectItem value="price-high-low">Price: High to Low</SelectItem>
                      <SelectItem value="popular">Most Popular</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* View mode toggle - hidden on mobile */}
                <div className="hidden md:flex border border-gray-200 rounded-lg overflow-hidden">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="icon"
                    onClick={() => setViewMode('grid')}
                    className="h-10 w-10 md:h-11 md:w-11 rounded-none"
                  >
                    <Grid3X3 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="icon"
                    onClick={() => setViewMode('list')}
                    className="h-10 w-10 md:h-11 md:w-11 rounded-none"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Active Filters Display - Mobile Optimized */}
          {getActiveFilterCount() > 0 && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              className="flex flex-wrap items-center gap-2 mb-4 md:mb-6"
            >
              <span className="text-sm text-gray-600 flex items-center gap-2">
                <Filter className="h-4 w-4" />
                Active filters:
              </span>
              <Badge variant="secondary" className="bg-purple-100 text-purple-700">
                {getActiveFilterCount()} filter{getActiveFilterCount() > 1 ? 's' : ''} active
              </Badge>

              {/* Wishlist Filter Toggle */}
              {user && wishlistSummary && wishlistSummary.totalItems > 0 && (
                <Button
                  variant={filters.showWishlistOnly ? "default" : "outline"}
                  size="sm"
                  onClick={() => setFilters(prev => ({ ...prev, showWishlistOnly: !prev.showWishlistOnly }))}
                  className={`h-7 ${filters.showWishlistOnly ? 'bg-red-500 hover:bg-red-600' : 'text-red-600 border-red-200 hover:bg-red-50'}`}
                >
                  <Heart className={`h-3 w-3 mr-1 ${filters.showWishlistOnly ? 'fill-current' : ''}`} />
                  {filters.showWishlistOnly ? 'Showing Wishlist' : 'Show Wishlist Only'}
                </Button>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setFilters({
                    category: "All",
                    minPrice: 0,
                    maxPrice: Infinity,
                    inStock: false,
                    rating: null,
                    sortBy: '',
                    brands: [],
                    showWishlistOnly: false,
                  });
                  setSearchQuery('');
                }}
                className="text-purple-600 hover:text-purple-700 h-7"
              >
                Clear all
              </Button>
            </motion.div>
          )}
        </motion.div>

        <div className="flex flex-col lg:flex-row gap-4 lg:gap-8">
          {/* Enhanced Sidebar with filters - Hidden on mobile, visible on tablets and larger */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="hidden md:block w-full lg:w-80"
          >
            <div className="sticky top-8">
              <ProductFilters filters={filters} setFilters={setFilters} />
            </div>
          </motion.div>

          {/* Main content area */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex-1 min-w-0"
          >
            {/* Store Hero Banner - Hidden on mobile and when searching */}
            {!searchQuery.trim() && (
              <div className="hidden md:block">
                <StoreBanner />
              </div>
            )}

            {/* Search Results Header - Show when searching */}
            {searchQuery.trim() && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.4, ease: [0.25, 0.46, 0.45, 0.94] }}
                className="hidden md:block mb-6"
              >
                <div className="bg-gradient-to-r from-purple-600 via-indigo-600 to-blue-600 rounded-2xl p-8 text-white shadow-2xl border border-white/10">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-3">
                        <div className="p-2 bg-white/20 rounded-full backdrop-blur-sm">
                          <Search className="h-6 w-6 text-white" />
                        </div>
                        <h2 className="text-3xl font-bold" style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}>
                          Search Results
                        </h2>
                      </div>
                      <p className="text-purple-100 text-lg">
                        {filteredProducts().length > 0
                          ? `Found ${filteredProducts().length} product${filteredProducts().length !== 1 ? 's' : ''} for "${searchQuery}"`
                          : `No products found for "${searchQuery}"`
                        }
                      </p>
                    </div>
                    <div className="flex items-center gap-4">
                      <Badge variant="secondary" className="bg-white/20 text-white border-white/30 px-4 py-2 text-lg backdrop-blur-sm">
                        {filteredProducts().length} Result{filteredProducts().length !== 1 ? 's' : ''}
                      </Badge>
                      <Button
                        variant="outline"
                        onClick={() => setSearchQuery('')}
                        className="bg-white/10 border-white/30 text-white hover:bg-white/20 hover:text-white transition-all duration-200 backdrop-blur-sm"
                      >
                        <Search className="h-4 w-4 mr-2" />
                        Clear Search
                      </Button>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Category list - Hidden when searching on desktop, always shown on mobile */}
            <div className={`mb-4 md:mb-8 ${searchQuery.trim() ? 'md:hidden' : ''}`}>
              <CategoryList
                selectedCategory={filters.category}
                onCategoryChange={(categoryId) => setFilters(prev => ({ ...prev, category: categoryId }))}
              />
            </div>

            {/* Search-specific category filter for desktop */}
            {searchQuery.trim() && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.1 }}
                className="hidden md:block mb-6"
              >
                <div className="bg-white/70 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Filter className="h-5 w-5 text-purple-600" />
                      <span className="font-medium text-gray-900">Refine your search:</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CategoryList
                        selectedCategory={filters.category}
                        onCategoryChange={(categoryId) => setFilters(prev => ({ ...prev, category: categoryId }))}
                      />
                    </div>
                  </div>
                </div>
              </motion.div>
            )}

            {/* No Search Results Message */}
            {searchQuery.trim() && filteredProducts().length === 0 && !isLoading && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="text-center py-16 bg-white/70 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 mb-8"
              >
                <div className="max-w-md mx-auto">
                  <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center">
                    <Search className="h-12 w-12 text-gray-400" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-3" style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}>
                    No products found
                  </h3>
                  <p className="text-gray-600 mb-6">
                    We couldn't find any products matching "<span className="font-semibold text-purple-600">{searchQuery}</span>".
                    Try adjusting your search or browse our categories.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-3 justify-center">
                    <Button
                      onClick={() => setSearchQuery('')}
                      className="bg-purple-600 hover:bg-purple-700 text-white"
                    >
                      Clear Search
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setSearchQuery('');
                        setFilters(prev => ({ ...prev, category: "All" }));
                      }}
                      className="border-purple-200 text-purple-600 hover:bg-purple-50"
                    >
                      Browse All Products
                    </Button>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Product grid */}
            {(!searchQuery.trim() || filteredProducts().length > 0) && (
              <ProductGrid
                products={getCurrentPageProducts()}
                isLoading={isLoading}
                selectedCategory={filters.category ? categories.find(c => c._id === filters.category)?.name : "All Products"}
                onResetCategory={() => setFilters(prev => ({ ...prev, category: "All" }))}
                viewMode={viewMode}
              />
            )}

            {/* Enhanced Pagination */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="mt-6 md:mt-8"
            >
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
              />
            </motion.div>
          </motion.div>
        </div>
      </div>

      {/* Wishlist Panel */}
      {user && (
        <StoreWishlistPanel
          userId={user._id}
          isOpen={showWishlistPanel}
          onClose={() => setShowWishlistPanel(false)}
        />
      )}
    </div>
  );
}
