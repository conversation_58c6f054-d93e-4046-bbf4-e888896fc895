// app/(group)/group/[groupId]/groupmembers/page.tsx
"use client";

import React from 'react';
import { useParams } from 'next/navigation';
import { useGroupMembership } from '@/lib/redux/hooks/useGroupMembership';
import { useGetGroupStatsQuery, useGetOnlineMembersQuery } from '@/lib/redux/features/groupStats/groupStatsApiSlice';
import GroupMembersTable from '@/components/admin/tables/GroupMembersTable';
import { LoadingScreen } from '@/components/ui/loading-screen';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Users, UserCheck, TrendingUp, Activity, Crown, Calendar } from 'lucide-react';

export default function GroupMembersPage() {
    const { groupId } = useParams();
    const { userGroups, isLoading } = useGroupMembership();

    // Ensure groupId is a string
    const groupIdString = Array.isArray(groupId) ? groupId[0] : groupId;

    // Fetch group statistics and online members
    const {
        data: groupStats,
        isLoading: isLoadingStats
    } = useGetGroupStatsQuery(groupIdString || '', {
        skip: !groupIdString
    });

    const {
        data: onlineMembersData,
        isLoading: isLoadingOnline
    } = useGetOnlineMembersQuery(groupIdString || '', {
        skip: !groupIdString
    });

    // Get group details
    const groupDetails = userGroups.find(group => group._id === groupIdString);

    if (isLoading) {
        return <LoadingScreen />;
    }

    return (
        <div className="container mx-auto p-4 space-y-6">
            {/* Page Header */}
            <div className="flex flex-col space-y-2">
                <h1 className="text-3xl font-bold tracking-tight">Group Members</h1>
                <p className="text-muted-foreground">
                    Manage and view all members of {groupDetails?.name || 'your group'}
                </p>
            </div>

            {/* Member Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Total Members */}
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Members</CardTitle>
                        <Users className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        {isLoadingStats ? (
                            <Skeleton className="h-8 w-16" />
                        ) : (
                            <div className="text-2xl font-bold">{groupStats?.totalMembers || 0}</div>
                        )}
                        <p className="text-xs text-muted-foreground">
                            Active group members
                        </p>
                    </CardContent>
                </Card>

                {/* Online Members */}
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Online Now</CardTitle>
                        <UserCheck className="h-4 w-4 text-green-600" />
                    </CardHeader>
                    <CardContent>
                        {isLoadingOnline ? (
                            <Skeleton className="h-8 w-16" />
                        ) : (
                            <div className="text-2xl font-bold text-green-600">
                                {onlineMembersData?.metadata?.onlineCount || 0}
                            </div>
                        )}
                        <p className="text-xs text-muted-foreground">
                            Currently active
                        </p>
                    </CardContent>
                </Card>

                {/* Member Participation */}
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Participation Rate</CardTitle>
                        <TrendingUp className="h-4 w-4 text-blue-600" />
                    </CardHeader>
                    <CardContent>
                        {isLoadingStats ? (
                            <Skeleton className="h-8 w-16" />
                        ) : (
                            <div className="text-2xl font-bold text-blue-600">
                                {groupStats?.memberParticipationRate?.toFixed(0) || 0}%
                            </div>
                        )}
                        <p className="text-xs text-muted-foreground">
                            Active participants
                        </p>
                    </CardContent>
                </Card>

                {/* Recent Activity */}
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Recent Activity</CardTitle>
                        <Activity className="h-4 w-4 text-purple-600" />
                    </CardHeader>
                    <CardContent>
                        {isLoadingStats ? (
                            <Skeleton className="h-8 w-16" />
                        ) : (
                            <div className="text-2xl font-bold text-purple-600">
                                {groupStats?.recentActivityCount || 0}
                            </div>
                        )}
                        <p className="text-xs text-muted-foreground">
                            Last 7 days
                        </p>
                    </CardContent>
                </Card>
            </div>

            {/* Top Contributors Section */}
            {groupStats?.topSpenders && groupStats.topSpenders.length > 0 && (
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <Crown className="h-5 w-5 text-yellow-500" />
                            <span>Top Contributors</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
                            {groupStats.topSpenders.slice(0, 5).map((contributor, index) => (
                                <div key={contributor.userId} className="text-center space-y-2">
                                    <div className="relative">
                                        <div className="h-12 w-12 mx-auto rounded-full bg-gradient-to-br from-yellow-400 to-orange-500 flex items-center justify-center text-white font-bold">
                                            {contributor.name.charAt(0).toUpperCase()}
                                        </div>
                                        {index === 0 && (
                                            <Badge className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 bg-yellow-500">
                                                <Crown className="h-3 w-3" />
                                            </Badge>
                                        )}
                                    </div>
                                    <div>
                                        <p className="font-medium text-sm">{contributor.name}</p>
                                        <p className="text-xs text-muted-foreground">
                                            R{contributor.totalSpent.toLocaleString()}
                                        </p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>
            )}

            {/* Members Table */}
            {groupIdString && typeof groupIdString === 'string' && (
                <GroupMembersTable groupId={groupIdString} />
            )}
        </div>
    );
}