// app/(group)/group/[groupId]/layout.tsx
"use client";

import { Inter } from "next/font/google";
import "../../../globals.css";
import { GroupSidebar } from "@/components/navigation/GroupSidebar";
import { GroupMobileSidebar } from "@/components/navigation/GroupMobileSidebar";
import { GroupTopNavigation } from "@/components/navigation/GroupTopNavigation";
import { ReduxProvider } from "@/app/providers/ReduxProvider";
import { useGroupMembership } from "@/lib/redux/hooks/useGroupMembership";
import AuthProvider, { useAuth } from "@/context/AuthContext";
import { ProfessionalLoader } from "@/components/ui/professional-loader";
import { PageTransition } from "@/components/ui/page-transition";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Menu } from "lucide-react";
// import { GroupOrderProvider } from "@/context/GroupOrderContext";
import type React from "react";
import { CartProvider } from "@/components/cart/CartProvider";

const inter = Inter({ subsets: ["latin"] });

interface GroupLayoutProps {
  children: React.ReactNode;
}

const GroupLayoutContent = ({ children }: GroupLayoutProps) => {
  const { user } = useAuth();
  const params = useParams<{ groupId: string }>();
  const groupId = params.groupId;
  const { userGroups, isLoading: isLoadingGroups } = useGroupMembership(user?._id);
  const [isLoading, setIsLoading] = useState(true);
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);

  // Wait until the user, the groupId parameter, and the user groups are available.
  useEffect(() => {
    if (user && groupId && userGroups.length > 0 && !isLoadingGroups) {
      setIsLoading(false);
    }
  }, [user, groupId, userGroups, isLoadingGroups]);

  if (isLoading) {
    return <ProfessionalLoader isLoading={true} type="page" message="Loading group dashboard..." />;
  }

  const groupDetails = userGroups.find((group) => group._id === params.groupId);

  if (!user || !groupDetails) {
    return null;
  }

  return (
    <CartProvider groupId={params.groupId as string}>
      <div className={`flex h-screen bg-gray-50 ${inter.className}`}>
        {/* Desktop Sidebar - Hidden on mobile */}
        <div className="hidden lg:block">
          <GroupSidebar groupName={groupDetails.name} groupId={groupDetails._id} />
        </div>

        {/* Mobile Sidebar */}
        <GroupMobileSidebar
          isOpen={isMobileSidebarOpen}
          onClose={() => setIsMobileSidebarOpen(false)}
          groupName={groupDetails.name}
          groupId={groupDetails._id}
        />

        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Mobile Header with Hamburger Menu */}
          <div className="lg:hidden flex items-center justify-between p-4 bg-white border-b border-gray-200">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsMobileSidebarOpen(true)}
              className="h-10 w-10 rounded-lg bg-gray-100 hover:bg-[#2A7C6C] hover:text-white text-gray-700 transition-all duration-300"
              aria-label="Open group navigation"
            >
              <Menu className="h-5 w-5" />
            </Button>
            <div className="flex items-center gap-2">
              <h1 className="text-lg font-semibold text-[#2A7C6C] truncate">
                {groupDetails.name}
              </h1>
            </div>
            <div className="w-10" /> {/* Spacer for centering */}
          </div>

          {/* Desktop Top Navigation - Hidden on mobile */}
          <div className="hidden lg:block">
            <GroupTopNavigation
              groupProgress={{
                totalAmount: groupDetails.totalSales || 0,
                targetAmount: 100000,
                nextDeliveryDate: "To be scheduled",
                groupId: groupDetails._id,
              }}
            />
          </div>

          <main className="flex-1 overflow-x-hidden overflow-y-auto p-4 lg:p-8">
            <PageTransition>
              {children}
            </PageTransition>
          </main>
        </div>
      </div>
    </CartProvider>
  );
};

const ProviderWrapper = ({ children }: GroupLayoutProps) => {
  return (
    <ReduxProvider>
      {children}
    </ReduxProvider>
  );
};

export default function GroupLayout({ children }: GroupLayoutProps) {
  return (
    <AuthProvider>
      <ProviderWrapper>
        <GroupLayoutContent>{children}</GroupLayoutContent>
      </ProviderWrapper>
    </AuthProvider>
  );
}