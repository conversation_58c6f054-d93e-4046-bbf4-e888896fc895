// app/(group)/group/[groupId]/products/page.tsx

"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { motion } from "framer-motion";
import { useGetAllProductsQuery } from "@/lib/redux/features/products/productsApiSlice";
import { useGetCategoriesQuery } from "@/lib/redux/features/categories/categoriesApiSlice";
import { useGroupMembership } from "@/lib/redux/hooks/useGroupMembership";
import { SimplifiedProductSearch } from "@/components/products/SimplifiedProductSearch";
import { ReduxProductGrid } from "@/components/products/ReduxProductGrid";
import { ProductCategoryTabs } from "@/components/products/ProductCategoryTabs";
import { ProfessionalLoader, ProductGridSkeleton, SearchSkeleton } from "@/components/ui/professional-loader";
import { StaggeredContainer, StaggeredItem } from "@/components/ui/page-transition";
import type { StokvelGroup } from "@/types/stokvelgroup";

export default function ProductsPage() {
  const { groupId } = useParams();
  const { data: products = [], isLoading: productsLoading } = useGetAllProductsQuery();
  const { data: categories = [], isLoading: categoriesLoading } = useGetCategoriesQuery();
  const { userGroups, isLoading: _isLoadingGroups, error: groupsError } = useGroupMembership();
  const [filteredProducts, setFilteredProducts] = useState<typeof products>([]);
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [isInitialized, setIsInitialized] = useState(false);

  // Store the current group ID in localStorage for use in other components
  useEffect(() => {
    if (groupId) {
      localStorage.setItem('currentGroupId', groupId as string);
      console.log('ProductsPage - Set currentGroupId in localStorage:', groupId);
    }
  }, [groupId]);

  // Log any errors with groups
  useEffect(() => {
    if (groupsError) {
      console.error('ProductsPage - Error loading groups:', groupsError);
    }
  }, [groupsError]);

  const currentGroup = userGroups.find((group) => group._id === groupId) as StokvelGroup | undefined;

  useEffect(() => {
    // Only update filtered products if products array has items and we haven't initialized yet
    // or if products length has changed (indicating new data has loaded)
    if (products.length > 0 && (!isInitialized || filteredProducts.length === 0)) {
      setFilteredProducts(products);
      setIsInitialized(true);
    }
  }, [products, isInitialized, filteredProducts.length]);

  const handleSearch = (query: string) => {
    const filtered = products.filter((product) => {
      const matchesQuery = !query || product.name.toLowerCase().includes(query.toLowerCase()) ||
                          product.description?.toLowerCase().includes(query.toLowerCase());
      const matchesCategory = selectedCategory === "all" || product.category._id === selectedCategory;
      return matchesQuery && matchesCategory;
    });
    setFilteredProducts(filtered);
  };

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);

    // Re-apply current search with new category
    const filtered = products.filter((product) => {
      const matchesCategory = category === "all" || product.category._id === category;
      return matchesCategory;
    });

    setFilteredProducts(filtered);
  };

  // Show loading states with better UX
  if (productsLoading || categoriesLoading) {
    return (
      <div className="space-y-6">
        <div className="space-y-4">
          <div className="h-8 bg-gray-200 rounded-lg w-64 animate-pulse" />
          <SearchSkeleton />
          <div className="flex gap-2 overflow-x-auto">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-10 bg-gray-200 rounded-lg w-20 flex-shrink-0 animate-pulse" />
            ))}
          </div>
        </div>
        <ProductGridSkeleton />
      </div>
    );
  }

  // If groups are still loading, we can proceed with products
  // This allows the products page to work even if group membership data is not available

  return (
    <StaggeredContainer className="space-y-6 lg:space-y-8">
      {/* Mobile-First Header */}
      <StaggeredItem>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-4"
        >
          <div className="flex flex-col space-y-2">
            <h1 className="text-2xl lg:text-3xl font-bold text-[#2F4858] tracking-tight">
              Products
            </h1>
            <p className="text-sm lg:text-base text-gray-600">
              Browse and shop from {currentGroup?.name || "your group"}'s product catalog
            </p>
          </div>
        </motion.div>
      </StaggeredItem>

      {/* Simplified Search */}
      <StaggeredItem>
        <div className="space-y-4">
          {/* Mobile-optimized search */}
          <div className="block lg:hidden">
            <SimplifiedProductSearch
              onSearch={handleSearch}
              placeholder="Search products..."
              className="w-full"
            />
          </div>

          {/* Desktop search */}
          <div className="hidden lg:block">
            <SimplifiedProductSearch
              onSearch={handleSearch}
              placeholder="Search products by name or description..."
              className="max-w-md"
            />
          </div>
        </div>
      </StaggeredItem>

      {/* Category Tabs - Optimized for mobile */}
      <StaggeredItem>
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <ProductCategoryTabs
            categories={categories}
            selectedCategory={selectedCategory}
            onCategoryChange={handleCategoryChange}
          />
        </motion.div>
      </StaggeredItem>

      {/* Product Grid */}
      <StaggeredItem>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <ReduxProductGrid
            products={filteredProducts}
            groupId={groupId as string}
            isLoading={productsLoading}
          />
        </motion.div>
      </StaggeredItem>
    </StaggeredContainer>
  );
}
