// app/(group)/group/[groupId]/page.tsx

"use client"

import { useEffect } from "react"
import { useAuth } from "@/context/AuthContext"
import { useParams, useRouter } from "next/navigation"
import { LoadingScreen } from "@/components/ui/loading-screen"
import { useGroupMembership } from "@/lib/redux/hooks/useGroupMembership"
import GroupDashboard from "@/components/groups/GroupDashboard"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import GroupOrdersReduxTable from "@/components/admin/tables/GroupOrdersReduxTable"




export default function GroupPage() {
  const params = useParams<{ groupId: string }>()
  // Using params.groupId directly instead of a separate variable
  const router = useRouter()
  const { user } = useAuth()
  const { userGroups, isLoading, error } = useGroupMembership(user?._id)
  const groupDetails = userGroups.find((group) => group._id === params.groupId)





  useEffect(() => {
    if (!isLoading && !groupDetails) {
      router.push("/profile")
    }
  }, [isLoading, groupDetails, router])

  if (isLoading) {
    return <LoadingScreen />
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-100">
        <Alert variant="destructive" className="max-w-md">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            {typeof error === 'string'
              ? error
              : 'An error occurred while loading group data. Please try again.'}
          </AlertDescription>
          <Button className="mt-4" onClick={() => router.push("/profile")}>
            Return to Profile
          </Button>
        </Alert>
      </div>
    )
  }

  if (!groupDetails) {
    return null
  }

  return (
    <div className="space-y-6 lg:space-y-8">
      {/* Mobile-First Header */}
      <div className="space-y-4">
        <div className="flex flex-col space-y-2">
          <h1 className="text-2xl lg:text-3xl font-bold text-[#2F4858] tracking-tight">
            Group Dashboard
          </h1>
          <p className="text-sm lg:text-base text-gray-600">
            Welcome to {groupDetails.name} - Manage your group activities and orders
          </p>
        </div>
      </div>

      {/* Group Dashboard Component */}
      <div className="bg-white rounded-lg lg:rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <GroupDashboard groupDetails={groupDetails} />
      </div>

      {/* Group Orders Section */}
      <div className="bg-white rounded-lg lg:rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="p-4 lg:p-6 border-b border-gray-200">
          <h2 className="text-lg lg:text-xl font-semibold text-[#2F4858]">
            Group Orders
          </h2>
          <p className="text-sm text-gray-600 mt-1">
            Track and manage all group orders
          </p>
        </div>
        <div className="p-4 lg:p-6">
          <GroupOrdersReduxTable groupId={params.groupId} />
        </div>
      </div>
    </div>
  )
}
