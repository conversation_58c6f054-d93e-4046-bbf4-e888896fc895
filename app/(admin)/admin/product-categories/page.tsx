// app/(admin)/admin/product-categories/page.tsx

"use client"

import React, { Suspense } from 'react';
import dynamic from 'next/dynamic'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

// Dynamically import components
const CategoryStats = dynamic(
  () => import('@/components/admin/stats/CategoryStats').then(mod => mod.CategoryStats),
  {
    loading: () => <div>Loading category stats...</div>
  }
)

const ProductCategoriesTable = dynamic(
  () => import('@/components/admin/tables/ProductCategoriesTable').then(mod => mod.ProductCategoriesTable),
  {
    loading: () => <div>Loading categories table...</div>
  }
)

const AddProductCategoryModal = dynamic(
  () => import('@/components/admin/forms/AddProductCategoryModal').then(mod => mod.AddProductCategoryModal)
)



export default function ProductCategoriesPage() {
  return (
    <div className="space-y-6 p-8">
      <div className="flex justify-between items-center">
        <h2
          className="text-3xl font-bold tracking-tight"
          style={{
            fontFamily: "ClashDisplay-Variable, sans-serif",
            letterSpacing: "-0.02em",
          }}
        >
          Product Categories
        </h2>
        <AddProductCategoryModal />
      </div>

      {/* Category Statistics */}
      <Suspense fallback={<div>Loading category stats...</div>}>
        <CategoryStats />
      </Suspense>



      {/* Categories Table */}
      <Card className="hover:shadow-md transition-shadow">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <span>Category Management</span>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Manage your product categories and view their performance metrics.
          </p>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<div>Loading categories table...</div>}>
            <ProductCategoriesTable />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  )
}

