// app/(admin)/admin/page.tsx

"use client"

import { DashboardStats } from "@/components/admin/DashboardStats"
import { SalesChart } from "@/components/admin/SalesChart"
import { TopStoresTable } from "@/components/admin/tables/TopStoresTable"
import { StokvelGroupsTable } from "@/components/admin/tables/StokvelGroupsTable"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Video, Users, BarChart3, Activity } from 'lucide-react'
import { LocationCreationModal } from "@/components/admin/forms/LocationCreationModal"
import { useGetPendingRequestsCountQuery } from "@/lib/redux/features/groupRequests/groupRequestsApiSlice"
import { Badge } from "@/components/ui/badge"
import { UserPlus } from "lucide-react"
import Link from "next/link"

export default function AdminDashboard() {
  const { data: pendingRequestsData } = useGetPendingRequestsCountQuery();
  const pendingCount = pendingRequestsData?.count || 0;

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="flex items-center justify-between">
        <div>
          <h2
            className="text-3xl text-[#2F4858] font-bold tracking-tight"
            style={{
              fontFamily: "ClashDisplay-Variable, sans-serif",
              letterSpacing: "-0.02em",
            }}
          >
            Dashboard
          </h2>
          <p className="text-muted-foreground">
            Welcome back to your Stokvel group buying overview.
          </p>
        </div>

        <div className="flex items-center gap-3">
          {/* Group Requests Notification */}
          {pendingCount > 0 && (
            <Link href="/admin/group-requests">
              <Button variant="outline" className="relative">
                <UserPlus className="h-4 w-4 mr-2" />
                Group Requests
                <Badge
                  variant="destructive"
                  className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
                >
                  {pendingCount}
                </Badge>
              </Button>
            </Link>
          )}

          {/* Location-based group creation modal */}
          <LocationCreationModal />
        </div>
      </div>

      {/* Stats Cards */}
      <DashboardStats />

      {/* Charts Section - Mobile First Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-6">
        {/* Sales Chart - Takes 2 columns on large screens */}
        <div className="lg:col-span-2">
          <SalesChart />
        </div>

        {/* Quick Actions Card - Mobile Optimized */}
        <div className="space-y-4">
          <Card className="border-gray-200">
            <CardHeader className="pb-3">
              <CardTitle className="text-base font-semibold text-gray-900 flex items-center gap-2">
                <Video className="h-4 w-4 text-blue-600" />
                Quick Actions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button
                variant="outline"
                className="w-full justify-start text-sm"
                size="sm"
              >
                <Video className="h-4 w-4 mr-2" />
                Daily Meeting (2:30 PM)
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start text-sm"
                size="sm"
              >
                <Users className="h-4 w-4 mr-2" />
                View All Groups
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start text-sm"
                size="sm"
              >
                <BarChart3 className="h-4 w-4 mr-2" />
                Generate Report
              </Button>
            </CardContent>
          </Card>

          {/* System Status Card */}
          <Card className="border-gray-200">
            <CardHeader className="pb-3">
              <CardTitle className="text-base font-semibold text-gray-900 flex items-center gap-2">
                <Activity className="h-4 w-4 text-green-600" />
                System Status
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">API Status</span>
                <Badge variant="default" className="bg-green-100 text-green-800">
                  Operational
                </Badge>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Database</span>
                <Badge variant="default" className="bg-green-100 text-green-800">
                  Healthy
                </Badge>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Last Backup</span>
                <span className="text-gray-900 font-medium">2 hours ago</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Tables Section - Mobile First Layout */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 lg:gap-6">
        {/* Top Performing Groups */}
        <TopStoresTable />

        {/* All Stokvel Groups */}
        <StokvelGroupsTable />
      </div>
    </div>
  )
}
