// app/(admin)/admin/products/page.tsx

"use client"

import React, { Suspense } from 'react';
import dynamic from 'next/dynamic'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

// Dynamically import components
const ProductStats = dynamic(
  () => import('@/components/admin/stats/ProductStats').then(mod => mod.ProductStats),
  {
    loading: () => <div>Loading stats...</div>
  }
)

const ProductsTable = dynamic(
  () => import('@/components/admin/tables/ProductTable').then(mod => mod.ProductsTable),
  {
    loading: () => <div>Loading table...</div>
  }
)

const AddProductModal = dynamic(
  () => import('@/components/admin/forms/AddProductModal').then(mod => mod.AddProductModal)
)

const AddProductCategoryModal = dynamic(
  () => import('@/components/admin/forms/AddProductCategoryModal').then(mod => mod.AddProductCategoryModal)
)



const TopSellingProductsChart = dynamic(
  () => import('./TopSellingProductsChart').then(mod => mod.TopSellingProductsChart),
  {
    loading: () => <div>Loading chart...</div>
  }
)

const ProductSalesTrendChart = dynamic(
  () => import('./ProductSalesTrendChart').then(mod => mod.ProductSalesTrendChart),
  {
    loading: () => <div>Loading chart...</div>
  }
)

export default function ProductsPage() {
  return (
    <div className="space-y-6 p-8">
      <div className="flex justify-between items-center">
        <h2
          className="text-3xl font-bold tracking-tight"
          style={{
            fontFamily: "ClashDisplay-Variable, sans-serif",
            letterSpacing: "-0.02em",
          }}
        >
          Products
        </h2>
        <div className="space-x-2">
          <AddProductCategoryModal />
          <AddProductModal />
        </div>
      </div>

      <Suspense fallback={<div>Loading product stats...</div>}>
        <ProductStats />
      </Suspense>



      <div className="grid gap-6 md:grid-cols-2">
        <Suspense fallback={<div>Loading charts...</div>}>
          <TopSellingProductsChart />
          <ProductSalesTrendChart />
        </Suspense>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Product Inventory</CardTitle>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<div>Loading product table...</div>}>
            <ProductsTable />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  )
}
