"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "recharts"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { RefreshCw, AlertCircle, TrendingUp, Package } from "lucide-react"
import { useRealTimeProductAnalytics } from "@/lib/redux/features/products/productAnalyticsApiSlice"
import { useState } from "react"

const COLORS = ['#2A7C6C', '#34A853', '#4285F4', '#EA4335', '#FBBC04', '#9AA0A6', '#FF6D01', '#9C27B0'];

export function TopSellingProductsChart() {
  const [period, setPeriod] = useState(30);
  const {
    data: response,
    isLoading,
    isError,
    error,
    refetch
  } = useRealTimeProductAnalytics({ period });

  // Custom tooltip component
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900">{label}</p>
          <p className="text-sm text-gray-600">
            <span className="font-medium text-[#2A7C6C]">{payload[0].value}</span> units sold
          </p>
          <p className="text-sm text-gray-600">
            Revenue: <span className="font-medium">R {data.totalRevenue?.toLocaleString('en-ZA', { minimumFractionDigits: 2 }) || '0.00'}</span>
          </p>
        </div>
      );
    }
    return null;
  };

  // Loading state
  if (isLoading) {
    return (
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-[#2A7C6C]" />
              Top Selling Products
            </CardTitle>
            <p className="text-sm text-gray-600 mt-1">Best performing products by units sold</p>
          </div>
          <Skeleton className="h-8 w-16" />
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center justify-between">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-6 w-16" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  // Error state
  if (isError || !response?.success) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-[#2A7C6C]" />
            Top Selling Products
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert className="border-red-200 bg-red-50">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              Failed to load top selling products data.
              <Button
                variant="outline"
                size="sm"
                onClick={() => refetch()}
                className="ml-2"
              >
                <RefreshCw className="h-3 w-3 mr-1" />
                Retry
              </Button>
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  const analytics = response.analytics;
  const topProducts = analytics.topSellingProducts || [];
  const hasData = topProducts.length > 0;

  // Prepare chart data
  const chartData = topProducts.slice(0, 8).map((product, index) => ({
    name: product.name.length > 15 ? `${product.name.substring(0, 15)}...` : product.name,
    fullName: product.name,
    sales: product.totalSold,
    totalRevenue: product.totalRevenue,
    color: COLORS[index % COLORS.length]
  }));

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-[#2A7C6C]" />
            Top Selling Products
          </CardTitle>
          <p className="text-sm text-gray-600 mt-1">Best performing products by units sold</p>
        </div>

        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-xs">
            {period} days
          </Badge>
          <Button
            variant="outline"
            size="sm"
            onClick={() => refetch()}
            className="h-8"
          >
            <RefreshCw className="h-3 w-3" />
          </Button>
        </div>
      </CardHeader>

      <CardContent>
        {hasData ? (
          <div className="space-y-4">
            {/* Chart */}
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <XAxis
                  dataKey="name"
                  tick={{ fontSize: 12 }}
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis tick={{ fontSize: 12 }} />
                <Tooltip content={<CustomTooltip />} />
                <Bar dataKey="sales" radius={[4, 4, 0, 0]}>
                  {chartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>

            {/* Summary stats */}
            <div className="grid grid-cols-2 gap-4 pt-4 border-t">
              <div className="text-center">
                <p className="text-2xl font-bold text-[#2A7C6C]">
                  {topProducts.reduce((sum, p) => sum + p.totalSold, 0).toLocaleString()}
                </p>
                <p className="text-sm text-gray-600">Total Units Sold</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-green-600">
                  R {topProducts.reduce((sum, p) => sum + p.totalRevenue, 0).toLocaleString('en-ZA', { minimumFractionDigits: 2 })}
                </p>
                <p className="text-sm text-gray-600">Total Revenue</p>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <Package className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Sales Data</h3>
            <p className="text-sm text-gray-600 max-w-sm">
              No product sales data available for the selected period. Data will appear once orders are completed.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

