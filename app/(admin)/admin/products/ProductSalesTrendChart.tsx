"use client"

import { <PERSON>, <PERSON>Chart, ResponsiveContainer, XAxis, YAxis, Tooltip, CartesianGrid, Area, AreaChart } from "recharts"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { RefreshCw, AlertCircle, BarChart3, TrendingUp, TrendingDown } from "lucide-react"
import { useRealTimeProductAnalytics } from "@/lib/redux/features/products/productAnalyticsApiSlice"
import { useState } from "react"

export function ProductSalesTrendChart() {
  const [period, setPeriod] = useState(30);
  const [chartType, setChartType] = useState<'line' | 'area'>('area');
  const {
    data: response,
    isLoading,
    isError,
    error,
    refetch
  } = useRealTimeProductAnalytics({ period });

  // Custom tooltip component
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900">{label}</p>
          <div className="space-y-1">
            {payload.map((entry: any, index: number) => (
              <p key={index} className="text-sm">
                <span className="font-medium" style={{ color: entry.color }}>
                  {entry.dataKey === 'sales' ? 'Sales: ' : 'Orders: '}
                </span>
                {entry.dataKey === 'sales'
                  ? `R ${entry.value.toLocaleString('en-ZA', { minimumFractionDigits: 2 })}`
                  : entry.value.toLocaleString()
                }
              </p>
            ))}
          </div>
        </div>
      );
    }
    return null;
  };

  // Loading state
  if (isLoading) {
    return (
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-[#2A7C6C]" />
              Sales Trend (Last 7 Days)
            </CardTitle>
            <p className="text-sm text-gray-600 mt-1">Daily sales performance overview</p>
          </div>
          <Skeleton className="h-8 w-16" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[300px] w-full" />
        </CardContent>
      </Card>
    );
  }

  // Error state
  if (isError || !response?.success) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-[#2A7C6C]" />
            Sales Trend (Last 7 Days)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert className="border-red-200 bg-red-50">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              Failed to load sales trend data.
              <Button
                variant="outline"
                size="sm"
                onClick={() => refetch()}
                className="ml-2"
              >
                <RefreshCw className="h-3 w-3 mr-1" />
                Retry
              </Button>
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  const analytics = response.analytics;
  const salesTrend = analytics.salesTrend || [];
  const hasData = salesTrend.some(day => day.sales > 0 || day.orders > 0);

  // Calculate trend
  const totalSales = salesTrend.reduce((sum, day) => sum + day.sales, 0);
  const totalOrders = salesTrend.reduce((sum, day) => sum + day.orders, 0);
  const avgDailySales = salesTrend.length > 0 ? totalSales / salesTrend.length : 0;

  // Calculate trend direction (compare first half vs second half)
  const midPoint = Math.floor(salesTrend.length / 2);
  const firstHalf = salesTrend.slice(0, midPoint);
  const secondHalf = salesTrend.slice(midPoint);
  const firstHalfAvg = firstHalf.reduce((sum, day) => sum + day.sales, 0) / Math.max(firstHalf.length, 1);
  const secondHalfAvg = secondHalf.reduce((sum, day) => sum + day.sales, 0) / Math.max(secondHalf.length, 1);
  const trendDirection = secondHalfAvg > firstHalfAvg ? 'up' : secondHalfAvg < firstHalfAvg ? 'down' : 'neutral';

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-[#2A7C6C]" />
            Sales Trend (Last 7 Days)
          </CardTitle>
          <p className="text-sm text-gray-600 mt-1">Daily sales performance overview</p>
        </div>

        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1">
            <Button
              variant={chartType === 'line' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setChartType('line')}
              className="h-8 px-2"
            >
              Line
            </Button>
            <Button
              variant={chartType === 'area' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setChartType('area')}
              className="h-8 px-2"
            >
              Area
            </Button>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => refetch()}
            className="h-8"
          >
            <RefreshCw className="h-3 w-3" />
          </Button>
        </div>
      </CardHeader>

      <CardContent>
        {hasData ? (
          <div className="space-y-4">
            {/* Chart */}
            <ResponsiveContainer width="100%" height={300}>
              {chartType === 'area' ? (
                <AreaChart data={salesTrend} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                  <defs>
                    <linearGradient id="salesGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#2A7C6C" stopOpacity={0.3}/>
                      <stop offset="95%" stopColor="#2A7C6C" stopOpacity={0.05}/>
                    </linearGradient>
                    <linearGradient id="ordersGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#34A853" stopOpacity={0.3}/>
                      <stop offset="95%" stopColor="#34A853" stopOpacity={0.05}/>
                    </linearGradient>
                  </defs>
                  <XAxis dataKey="date" tick={{ fontSize: 12 }} />
                  <YAxis yAxisId="sales" orientation="left" tick={{ fontSize: 12 }} />
                  <YAxis yAxisId="orders" orientation="right" tick={{ fontSize: 12 }} />
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <Tooltip content={<CustomTooltip />} />
                  <Area
                    yAxisId="sales"
                    type="monotone"
                    dataKey="sales"
                    stroke="#2A7C6C"
                    strokeWidth={2}
                    fill="url(#salesGradient)"
                  />
                  <Area
                    yAxisId="orders"
                    type="monotone"
                    dataKey="orders"
                    stroke="#34A853"
                    strokeWidth={2}
                    fill="url(#ordersGradient)"
                  />
                </AreaChart>
              ) : (
                <LineChart data={salesTrend} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                  <XAxis dataKey="date" tick={{ fontSize: 12 }} />
                  <YAxis yAxisId="sales" orientation="left" tick={{ fontSize: 12 }} />
                  <YAxis yAxisId="orders" orientation="right" tick={{ fontSize: 12 }} />
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <Tooltip content={<CustomTooltip />} />
                  <Line
                    yAxisId="sales"
                    type="monotone"
                    dataKey="sales"
                    stroke="#2A7C6C"
                    strokeWidth={3}
                    dot={{ fill: '#2A7C6C', strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6, stroke: '#2A7C6C', strokeWidth: 2 }}
                  />
                  <Line
                    yAxisId="orders"
                    type="monotone"
                    dataKey="orders"
                    stroke="#34A853"
                    strokeWidth={3}
                    dot={{ fill: '#34A853', strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6, stroke: '#34A853', strokeWidth: 2 }}
                  />
                </LineChart>
              )}
            </ResponsiveContainer>

            {/* Summary stats */}
            <div className="grid grid-cols-3 gap-4 pt-4 border-t">
              <div className="text-center">
                <p className="text-xl font-bold text-[#2A7C6C]">
                  R {totalSales.toLocaleString('en-ZA', { minimumFractionDigits: 2 })}
                </p>
                <p className="text-sm text-gray-600">Total Sales</p>
              </div>
              <div className="text-center">
                <p className="text-xl font-bold text-green-600">
                  {totalOrders.toLocaleString()}
                </p>
                <p className="text-sm text-gray-600">Total Orders</p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center gap-1">
                  <p className="text-xl font-bold text-gray-900">
                    R {avgDailySales.toLocaleString('en-ZA', { minimumFractionDigits: 2 })}
                  </p>
                  {trendDirection === 'up' && <TrendingUp className="h-4 w-4 text-green-600" />}
                  {trendDirection === 'down' && <TrendingDown className="h-4 w-4 text-red-600" />}
                </div>
                <p className="text-sm text-gray-600">Avg Daily Sales</p>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <BarChart3 className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Sales Data</h3>
            <p className="text-sm text-gray-600 max-w-sm">
              No sales data available for the last 7 days. Data will appear once orders are completed.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

