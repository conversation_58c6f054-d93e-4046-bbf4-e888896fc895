// app/(profile)/profile/page.tsx

"use client"

import { useAuth } from "@/context/AuthContext"
import { useGetAllProductsQuery } from "@/lib/redux/features/products/productsApiSlice"
import { useGroupMembership } from "@/lib/redux/hooks/useGroupMembership"
import { useAppSelector } from "@/lib/redux/hooks"
import { selectCartItems } from "@/lib/redux/features/cart/cartSlice"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Eye, Users, ShoppingBag, DollarSign, Activity, Search } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { UserGroupsHistory } from "@/components/profile/UserGroupsHistory"
import { EnhancedGroupMembership } from "@/components/profile/EnhancedGroupMembership"

// Define the interface for an order
interface Order {
  id: string
  product: string
  date: string
  amount: number
  status: "Completed" | "Processing"
}

export default function UserProfile() {
  const { user } = useAuth()
  const { userGroups } = useGroupMembership()
  const { data: products = [] } = useGetAllProductsQuery()
  const cartItems = useAppSelector(selectCartItems)
  const router = useRouter()

  // Use the Order interface to type the state
  const [recentOrders, setRecentOrders] = useState<Order[]>([])

  useEffect(() => {
    // Here you would typically fetch the recent orders from an API
    // For now, we'll use dummy data
    setRecentOrders([
      { id: "1", product: "Organic Vegetables Bundle", date: "2024-01-18", amount: 150.0, status: "Completed" },
      { id: "2", product: "Bulk Rice Package", date: "2024-01-17", amount: 280.5, status: "Processing" },
      { id: "3", product: "Fresh Fruits Box", date: "2024-01-16", amount: 95.0, status: "Completed" },
    ])
  }, [])

  return (
    <div className="space-y-6">
      {/* Search and Welcome Section */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h2
            className="text-3xl text-[#2F4858] font-bold tracking-tight"
            style={{
              fontFamily: "ClashDisplay-Variable, sans-serif",
              letterSpacing: "-0.02em",
            }}
          >
            Hello {user?.name || "User"}
          </h2>
          <p className="text-muted-foreground">Welcome back to your dashboard</p>
        </div>
        <div className="flex w-full md:w-auto gap-2">
          <Input placeholder="Search for anything..." className="max-w-sm" />
          <Button>
            <Search className="h-4 w-4 mr-2" />
            Search
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-none">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Products</p>
                <h3 className="text-2xl font-bold">{products.length}</h3>
                <p className="text-xs text-muted-foreground mt-1">Available Products</p>
              </div>
              <div className="h-12 w-12 rounded-full bg-blue-500/10 flex items-center justify-center">
                <Eye className="h-6 w-6 text-blue-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100 border-none">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Cart Items</p>
                <h3 className="text-2xl font-bold">{cartItems.length || 0}</h3>
                <p className="text-xs text-muted-foreground mt-1">In Your Cart</p>
              </div>
              <div className="h-12 w-12 rounded-full bg-green-500/10 flex items-center justify-center">
                <ShoppingBag className="h-6 w-6 text-green-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-none">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Groups</p>
                <h3 className="text-2xl font-bold">{userGroups.length}</h3>
                <p className="text-xs text-muted-foreground mt-1">Stokvel Groups</p>
              </div>
              <div className="h-12 w-12 rounded-full bg-purple-500/10 flex items-center justify-center">
                <Users className="h-6 w-6 text-purple-500" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Activity Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-lg font-medium">Recent Orders</CardTitle>
            <Button variant="ghost" size="sm">
              View all
            </Button>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {recentOrders.map((order) => (
                  <TableRow key={order.id}>
                    <TableCell className="font-medium">{order.product}</TableCell>
                    <TableCell>R{order.amount.toFixed(2)}</TableCell>
                    <TableCell>
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          order.status === "Completed" ? "bg-green-100 text-green-800" : "bg-yellow-100 text-yellow-800"
                        }`}
                      >
                        {order.status}
                      </span>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-lg font-medium">My Stokvel Groups</CardTitle>
            <Button variant="ghost" size="sm">
              View all
            </Button>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Group Name</TableHead>
                  <TableHead>Members</TableHead>
                  <TableHead>Action</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {userGroups.slice(0, 3).map((group) => (
                  <TableRow key={group._id}>
                    <TableCell className="font-medium">{group.name}</TableCell>
                    <TableCell>{group.members.length}</TableCell>
                    <TableCell>
                      <Button
                        variant="secondary"
                        size="sm"
                        className="bg-[#2A7C6C]/10 text-[#2A7C6C] hover:bg-[#2A7C6C]/20"
                        onClick={() => router.push(`/group/${group._id}`)}
                      >
                        View
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Group Membership Section */}
      <EnhancedGroupMembership />

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="pt-6">
            <div className="flex flex-col items-center text-center">
              <div className="h-12 w-12 rounded-full bg-blue-500/10 flex items-center justify-center mb-4">
                <ShoppingBag className="h-6 w-6 text-blue-500" />
              </div>
              <h4 className="font-semibold mb-1">My Orders</h4>
              <p className="text-sm text-muted-foreground mb-4">View your order history</p>
              <Button variant="outline" className="w-full">
                View Orders
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="pt-6">
            <div className="flex flex-col items-center text-center">
              <div className="h-12 w-12 rounded-full bg-green-500/10 flex items-center justify-center mb-4">
                <Users className="h-6 w-6 text-green-500" />
              </div>
              <h4 className="font-semibold mb-1">My Groups</h4>
              <p className="text-sm text-muted-foreground mb-4">Manage your Stokvel groups</p>
              <Button variant="outline" className="w-full">
                View Groups
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="pt-6">
            <div className="flex flex-col items-center text-center">
              <div className="h-12 w-12 rounded-full bg-purple-500/10 flex items-center justify-center mb-4">
                <DollarSign className="h-6 w-6 text-purple-500" />
              </div>
              <h4 className="font-semibold mb-1">Payments</h4>
              <p className="text-sm text-muted-foreground mb-4">Manage payment methods</p>
              <Button variant="outline" className="w-full">
                View Payments
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="pt-6">
            <div className="flex flex-col items-center text-center">
              <div className="h-12 w-12 rounded-full bg-orange-500/10 flex items-center justify-center mb-4">
                <Activity className="h-6 w-6 text-orange-500" />
              </div>
              <h4 className="font-semibold mb-1">Activity</h4>
              <p className="text-sm text-muted-foreground mb-4">View your recent activity</p>
              <Button variant="outline" className="w-full">
                View Activity
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
