"use client";

import { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import Image from "next/image";
import {
  ShoppingCart,
  ChevronLeft,
  Star,
  Truck,
  Package,
  Shield,
  Users
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/context/AuthContext";
import { useAddToCartMutation } from "@/lib/redux/features/cart/cartApiSlice";
import { JoinGroupModal } from "@/components/modals/JoinGroupModal";
import { WishlistButton } from "@/components/wishlist/WishlistButton";
import { RatingStars } from "@/components/product/RatingStars";
import { RatingDisplay } from "@/components/product/RatingDisplay";
import { RelatedProducts } from "@/components/product/RelatedProducts";
import { QuickRatingSection } from "@/components/product/QuickRatingSection";
import { useGetProductByIdQuery } from "@/lib/redux/features/products/productsApiSlice";
import { useGroupMembership } from "@/lib/redux/hooks/useGroupMembership";
import { LoadingScreen } from "@/components/ui/loading-screen";

export default function ProductDetailPage() {
  const { productId } = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const { userGroups } = useGroupMembership(user?._id);
  const [isJoinGroupOpen, setIsJoinGroupOpen] = useState(false);
  const [quantity, setQuantity] = useState(1);
  const [selectedGroupId, setSelectedGroupId] = useState<string | null>(null);
  const [addToCart] = useAddToCartMutation();
  const [isAdding, setIsAdding] = useState(false);

  // Fetch product details
  const { data: product, isLoading, error } = useGetProductByIdQuery(productId as string);

  // Set the selected group ID when user groups are loaded
  useEffect(() => {
    if (userGroups.length > 0 && !selectedGroupId) {
      setSelectedGroupId(userGroups[0]._id);

      // Store the current group ID in localStorage
      localStorage.setItem('currentGroupId', userGroups[0]._id);
    }
  }, [userGroups, selectedGroupId]);

  // Handle quantity changes
  const incrementQuantity = () => {
    if (product && quantity < product.stock) {
      setQuantity(quantity + 1);
    }
  };

  const decrementQuantity = () => {
    if (quantity > 1) {
      setQuantity(quantity - 1);
    }
  };

  // Handle add to cart
  const handleAddToCart = async () => {
    // If user is not logged in, open the join group modal
    if (!user) {
      setIsJoinGroupOpen(true);
      return;
    }

    // If user is logged in but not in any group, open the join group modal
    if (userGroups.length === 0 || !selectedGroupId) {
      setIsJoinGroupOpen(true);
      return;
    }

    // User is logged in and in a group, proceed with adding to cart directly
    try {
      // Validate required fields before making the API call
      if (!user._id || !product?._id || !selectedGroupId) {
        console.error('Missing required fields:', {
          userId: user._id,
          productId: product?._id,
          groupId: selectedGroupId
        });
        return;
      }

      // Set loading state
      setIsAdding(true);

      const cartItem = {
        userId: user._id,
        productId: product._id,
        quantity,
        groupId: selectedGroupId
      };

      console.log('Adding item to cart:', cartItem);

      // Add to cart
      const result = await addToCart(cartItem).unwrap();

      if (result) {
        console.log('Successfully added to cart:', result);

        // Show cart notification instead of redirecting
        // This provides a consistent experience with the ProductCard component
        // The notification will allow users to view their cart in an overlay

        // Wait a moment before showing the notification
        setTimeout(() => {
          // Show success message and stay on the same page
          setIsAdding(false);

          // Show a confirmation message
          alert(`${product.name} has been added to your cart!`);
        }, 500);
      }
    } catch (error: unknown) {
      console.error('Failed to add item to cart:', error);

      // Extract error message
      // Type guard to safely access error properties
      const errorMessage =
        typeof error === 'object' && error !== null
          ? (
              // Check if error has data.error property
              'data' in error &&
              typeof error.data === 'object' &&
              error.data !== null &&
              'error' in error.data &&
              typeof error.data.error === 'string'
                ? error.data.error
                // Check if error has error.data.error property
                : 'error' in error &&
                  typeof error.error === 'object' &&
                  error.error !== null &&
                  'data' in error.error &&
                  typeof error.error.data === 'object' &&
                  error.error.data !== null &&
                  'error' in error.error.data &&
                  typeof error.error.data.error === 'string'
                  ? error.error.data.error
                  : 'Failed to add item to cart. Please try again.'
            )
          : 'Failed to add item to cart. Please try again.';

      // Show error message to the user
      alert(errorMessage);

      // Reset loading state
      setIsAdding(false);
    }
  };

  // Handle back button click
  const handleBackClick = () => {
    router.back();
  };

  if (isLoading) {
    return <LoadingScreen />;
  }

  if (error || !product) {
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <h1 className="text-2xl font-bold mb-4">Product Not Found</h1>
        <p className="mb-8">Sorry, we couldn't find the product you're looking for.</p>
        <Button onClick={handleBackClick}>
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back to Products
        </Button>
      </div>
    );
  }

  // Determine button text based on user authentication and group membership
  const getButtonText = () => {
    if (!user) {
      return "Join a Group to Shop";
    }

    if (userGroups.length === 0) {
      return "Join a Group to Shop";
    }

    return "Add to Cart";
  };

  return (
    <div className="container mx-auto px-3 md:px-4 py-4 md:py-8">
      {/* Breadcrumb navigation - Mobile optimized */}
      <div className="mb-4 md:mb-6">
        <Button
          variant="ghost"
          className="pl-0 text-gray-600 hover:text-gray-900 text-sm md:text-base"
          onClick={handleBackClick}
        >
          <ChevronLeft className="mr-1 md:mr-2 h-4 w-4" />
          Back to Products
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-8">
        {/* Product Image - Mobile Optimized */}
        <div className="bg-white rounded-lg md:rounded-xl p-3 md:p-6 flex items-center justify-center shadow-sm">
          <div className="relative h-[280px] md:h-[400px] w-full">
            <Image
              src={product.image && product.image.trim() !== '' ? `/api/images/${product.image}` : "/placeholder.svg"}
              alt={product.name || "Product image"}
              fill
              className="object-contain rounded-lg"
              priority
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = "/placeholder.svg";
              }}
            />
          </div>
        </div>

        {/* Product Details - Mobile Optimized */}
        <div className="flex flex-col space-y-3 md:space-y-4">
          {/* Category */}
          <div className="mb-1 md:mb-2">
            <Badge variant="outline" className="text-xs md:text-sm">
              {product.category.name}
            </Badge>
          </div>

          {/* Product Name */}
          <h1 className="text-xl md:text-3xl font-bold mb-2 leading-tight">{product.name}</h1>

          {/* Price */}
          <div className="text-xl md:text-2xl font-bold text-purple-600 mb-3 md:mb-4">
            R{product.price.toFixed(2)}
          </div>

          {/* Rating Display - Mobile Optimized */}
          {product.averageRating && product.averageRating > 0 ? (
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-3 md:mb-4">
              <div className="flex items-center gap-2">
                <RatingStars
                  rating={product.averageRating}
                  size="md"
                  showValue={true}
                />
                <span className="text-sm md:text-base font-medium text-purple-600">
                  {product.averageRating.toFixed(1)}
                </span>
              </div>
              <span className="text-sm md:text-base text-gray-600">
                ({product.reviewCount || 0} review{(product.reviewCount || 0) !== 1 ? 's' : ''})
              </span>
            </div>
          ) : (
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-3 md:mb-4">
              <div className="flex items-center gap-2">
                <RatingStars rating={0} size="md" />
                <span className="text-sm md:text-base text-gray-500">0.0</span>
              </div>
              <span className="text-sm md:text-base text-gray-600">No reviews yet</span>
            </div>
          )}

          {/* Quick Rating Section - Mobile Optimized */}
          {user ? (
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-3 md:p-4 mb-4 md:mb-6">
              <h4 className="font-medium text-purple-900 mb-2 text-sm md:text-base">Rate this Product</h4>
              <p className="text-xs md:text-sm text-purple-700 mb-3">
                Share your experience with other shoppers
              </p>
              <QuickRatingSection
                productId={product._id}
                userId={user._id}
              />
            </div>
          ) : (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 md:p-4 mb-4 md:mb-6">
              <h4 className="font-medium text-gray-900 mb-2 text-sm md:text-base">Rate this Product</h4>
              <p className="text-xs md:text-sm text-gray-600 mb-3">
                Share your experience with other shoppers
              </p>
              <Button
                variant="outline"
                onClick={() => setIsJoinGroupOpen(true)}
                className="border-purple-200 text-purple-600 hover:bg-purple-50 text-sm md:text-base h-9 md:h-10"
                size="sm"
              >
                <Star className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
                Log in to Rate
              </Button>
            </div>
          )}

          {/* Description - Mobile Optimized */}
          <p className="text-sm md:text-base text-gray-700 mb-4 md:mb-6 leading-relaxed">{product.description}</p>

          <Separator className="mb-4 md:mb-6" />

          {/* Stock - Mobile Optimized */}
          <div className="mb-4 md:mb-6">
            <p className="text-xs md:text-sm font-medium mb-2">Availability:</p>
            <p className={`text-xs md:text-sm font-medium ${product.stock > 0 ? 'text-green-600' : 'text-red-600'}`}>
              {product.stock > 0 ? `In Stock (${product.stock} available)` : 'Out of Stock'}
            </p>
          </div>

          {/* Group Selection (if user is in multiple groups) - Mobile Optimized */}
          {user && userGroups.length > 1 && (
            <div className="mb-4 md:mb-6">
              <p className="text-xs md:text-sm font-medium mb-2">Select Group:</p>
              <select
                className="w-full p-2 md:p-3 border border-gray-300 rounded-md text-sm md:text-base"
                value={selectedGroupId || ''}
                onChange={(e) => {
                  setSelectedGroupId(e.target.value);
                  localStorage.setItem('currentGroupId', e.target.value);
                }}
              >
                {userGroups.map(group => (
                  <option key={group._id} value={group._id}>
                    {group.name}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* Quantity Selector - Mobile Optimized */}
          <div className="mb-4 md:mb-6">
            <p className="text-xs md:text-sm font-medium mb-2">Quantity:</p>
            <div className="flex items-center">
              <Button
                variant="outline"
                size="icon"
                onClick={decrementQuantity}
                disabled={quantity <= 1}
                className="h-9 w-9 md:h-10 md:w-10 text-lg"
              >
                -
              </Button>
              <span className="mx-3 md:mx-4 w-8 md:w-10 text-center text-sm md:text-base font-medium">{quantity}</span>
              <Button
                variant="outline"
                size="icon"
                onClick={incrementQuantity}
                disabled={product.stock <= quantity}
                className="h-9 w-9 md:h-10 md:w-10 text-lg"
              >
                +
              </Button>
            </div>
          </div>

          {/* Action Buttons - Mobile Optimized */}
          <div className="space-y-3 md:space-y-4">
            {/* Add to Cart Button */}
            <Button
              className="w-full bg-purple-600 hover:bg-purple-700 py-4 md:py-6 text-base md:text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-200"
              onClick={handleAddToCart}
              disabled={isAdding || product.stock <= 0}
            >
              {isAdding ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 md:h-5 md:w-5 border-2 border-white border-t-transparent mr-2"></div>
                  Adding to Cart...
                </div>
              ) : (
                <>
                  <ShoppingCart className="mr-2 h-4 w-4 md:h-5 md:w-5" />
                  {getButtonText()}
                </>
              )}
            </Button>

            {/* Wishlist Button */}
            {user && (
              <WishlistButton
                productId={product._id}
                userId={user._id}
                size="lg"
                variant="outline"
                showText={true}
                className="w-full h-12 md:h-14 text-sm md:text-base border-2 hover:bg-purple-50"
              />
            )}
          </div>

          {/* Product Features - Mobile Optimized */}
          <div className="mt-6 md:mt-8 grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4">
            <div className="flex items-start p-3 bg-gray-50 rounded-lg">
              <Truck className="h-4 w-4 md:h-5 md:w-5 mr-2 md:mr-3 text-purple-600 flex-shrink-0 mt-0.5" />
              <div>
                <p className="text-xs md:text-sm font-medium">Free Delivery</p>
                <p className="text-xs text-gray-500">For group orders over R1000</p>
              </div>
            </div>
            <div className="flex items-start p-3 bg-gray-50 rounded-lg">
              <Package className="h-4 w-4 md:h-5 md:w-5 mr-2 md:mr-3 text-purple-600 flex-shrink-0 mt-0.5" />
              <div>
                <p className="text-xs md:text-sm font-medium">Bulk Discounts</p>
                <p className="text-xs text-gray-500">Save more when buying together</p>
              </div>
            </div>
            <div className="flex items-start p-3 bg-gray-50 rounded-lg">
              <Shield className="h-4 w-4 md:h-5 md:w-5 mr-2 md:mr-3 text-purple-600 flex-shrink-0 mt-0.5" />
              <div>
                <p className="text-xs md:text-sm font-medium">Quality Guarantee</p>
                <p className="text-xs text-gray-500">30-day money back guarantee</p>
              </div>
            </div>
            <div className="flex items-start p-3 bg-gray-50 rounded-lg">
              <Users className="h-4 w-4 md:h-5 md:w-5 mr-2 md:mr-3 text-purple-600 flex-shrink-0 mt-0.5" />
              <div>
                <p className="text-xs md:text-sm font-medium">Community Buying</p>
                <p className="text-xs text-gray-500">Shop together with your group</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Product Reviews Section - Mobile Optimized */}
      <div className="mt-8 md:mt-12" data-reviews-section>
        <RatingDisplay
          productId={product._id}
          userId={user?._id}
        />
      </div>

      {/* Related Products Section - Mobile Optimized */}
      <div className="mt-8 md:mt-12">
        <RelatedProducts
          productId={product._id}
          limit={6}
        />
      </div>

      {/* Join Group Modal */}
      <JoinGroupModal
        isOpen={isJoinGroupOpen}
        onClose={() => setIsJoinGroupOpen(false)}
        productId={productId as string}
      />
    </div>
  );
}
