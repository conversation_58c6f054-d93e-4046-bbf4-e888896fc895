// scripts/test-group-request-system.js
// Simple integration test for the group request system

const mongoose = require('mongoose');

// Test configuration
const TEST_CONFIG = {
  mongoUri: process.env.MONGODB_URI || 'mongodb://localhost:27017/stockvelmarket',
  testUser: {
    email: '<EMAIL>',
    name: 'Test User',
    phone: '+1234567890',
    password: 'testpassword123',
    role: 'customer'
  },
  testAdmin: {
    email: '<EMAIL>',
    name: 'Test Admin',
    password: 'adminpassword123',
    role: 'admin'
  },
  testLocation: {
    provinceName: 'Test Province',
    cityName: 'Test City',
    townshipName: 'Test Township',
    locationName: 'Test Location'
  },
  testGroupRequest: {
    requestedGroupName: 'Test Stokvel Group',
    groupDescription: 'A test group for integration testing'
  }
};

async function connectToDatabase() {
  try {
    await mongoose.connect(TEST_CONFIG.mongoUri);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    process.exit(1);
  }
}

async function createTestData() {
  console.log('\n📝 Creating test data...');
  
  try {
    // Import models
    const { User } = require('../models/User');
    const { Province } = require('../models/Province');
    const { City } = require('../models/City');
    const { Township } = require('../models/Township');
    const { Location } = require('../models/Location');
    const { GroupRequest } = require('../models/GroupRequest');

    // Clean up existing test data
    await User.deleteMany({ email: { $in: [TEST_CONFIG.testUser.email, TEST_CONFIG.testAdmin.email] } });
    await GroupRequest.deleteMany({ requestedGroupName: TEST_CONFIG.testGroupRequest.requestedGroupName });

    // Create test users
    const testUser = new User(TEST_CONFIG.testUser);
    await testUser.save();
    console.log('✅ Created test user');

    const testAdmin = new User(TEST_CONFIG.testAdmin);
    await testAdmin.save();
    console.log('✅ Created test admin');

    // Create or find test location hierarchy
    let province = await Province.findOne({ name: TEST_CONFIG.testLocation.provinceName });
    if (!province) {
      province = new Province({
        name: TEST_CONFIG.testLocation.provinceName,
        code: 'TP',
        isActive: true
      });
      await province.save();
      console.log('✅ Created test province');
    }

    let city = await City.findOne({ name: TEST_CONFIG.testLocation.cityName, provinceId: province._id });
    if (!city) {
      city = new City({
        name: TEST_CONFIG.testLocation.cityName,
        provinceId: province._id,
        isActive: true
      });
      await city.save();
      console.log('✅ Created test city');
    }

    let township = await Township.findOne({ name: TEST_CONFIG.testLocation.townshipName, cityId: city._id });
    if (!township) {
      township = new Township({
        name: TEST_CONFIG.testLocation.townshipName,
        cityId: city._id,
        isActive: true
      });
      await township.save();
      console.log('✅ Created test township');
    }

    let location = await Location.findOne({ name: TEST_CONFIG.testLocation.locationName, townshipId: township._id });
    if (!location) {
      location = new Location({
        name: TEST_CONFIG.testLocation.locationName,
        townshipId: township._id,
        isActive: true
      });
      await location.save();
      console.log('✅ Created test location');
    }

    return {
      testUser,
      testAdmin,
      province,
      city,
      township,
      location
    };

  } catch (error) {
    console.error('❌ Failed to create test data:', error);
    throw error;
  }
}

async function testGroupRequestCreation(testData) {
  console.log('\n🧪 Testing group request creation...');
  
  try {
    const { GroupRequest } = require('../models/GroupRequest');
    const { testUser, province, city, township, location } = testData;

    // Create a group request
    const groupRequest = new GroupRequest({
      userId: testUser._id,
      userEmail: testUser.email,
      userName: testUser.name,
      userPhone: testUser.phone,
      provinceId: province._id,
      provinceName: province.name,
      cityId: city._id,
      cityName: city.name,
      townshipId: township._id,
      townshipName: township.name,
      locationId: location._id,
      locationName: location.name,
      fullLocationPath: `${province.name} > ${city.name} > ${township.name} > ${location.name}`,
      requestedGroupName: TEST_CONFIG.testGroupRequest.requestedGroupName,
      groupDescription: TEST_CONFIG.testGroupRequest.groupDescription,
      status: 'pending'
    });

    await groupRequest.save();
    console.log('✅ Group request created successfully');
    console.log(`   Request ID: ${groupRequest._id}`);
    console.log(`   Group Name: ${groupRequest.requestedGroupName}`);
    console.log(`   Status: ${groupRequest.status}`);

    return groupRequest;

  } catch (error) {
    console.error('❌ Failed to create group request:', error);
    throw error;
  }
}

async function testGroupRequestApproval(groupRequest, testData) {
  console.log('\n✅ Testing group request approval...');
  
  try {
    const { StokvelGroup } = require('../models/StokvelGroup');
    const { User } = require('../models/User');
    const { testAdmin, testUser, location } = testData;

    // Simulate approval process
    groupRequest.status = 'approved';
    groupRequest.reviewDate = new Date();
    groupRequest.reviewedBy = testAdmin._id;
    groupRequest.reviewNotes = 'Approved for testing purposes';

    // Create the StokvelGroup
    const newGroup = new StokvelGroup({
      name: groupRequest.requestedGroupName,
      description: groupRequest.groupDescription,
      admin: groupRequest.userId,
      members: [groupRequest.userId],
      locationId: groupRequest.locationId,
      totalSales: 0,
      avgOrderValue: 0,
      activeOrders: 0,
      bulkOrderThreshold: 1000,
      pendingOrderAmount: 0,
      deliveryStatus: 'pending'
    });

    await newGroup.save();

    // Update user's groups
    await User.findByIdAndUpdate(
      testUser._id,
      { $addToSet: { stokvelGroups: newGroup._id } }
    );

    // Update group request with created group
    groupRequest.createdGroupId = newGroup._id;
    await groupRequest.save();

    console.log('✅ Group request approved and group created successfully');
    console.log(`   Group ID: ${newGroup._id}`);
    console.log(`   Group Name: ${newGroup.name}`);
    console.log(`   Admin: ${testUser.name}`);
    console.log(`   Members: ${newGroup.members.length}`);

    return newGroup;

  } catch (error) {
    console.error('❌ Failed to approve group request:', error);
    throw error;
  }
}

async function testDataRetrieval() {
  console.log('\n📊 Testing data retrieval...');
  
  try {
    const { GroupRequest } = require('../models/GroupRequest');
    const { StokvelGroup } = require('../models/StokvelGroup');

    // Test getting all group requests
    const allRequests = await GroupRequest.find({})
      .populate('userId', 'name email')
      .populate('reviewedBy', 'name email')
      .populate('createdGroupId', 'name');

    console.log(`✅ Found ${allRequests.length} group requests`);

    // Test getting pending requests
    const pendingRequests = await GroupRequest.find({ status: 'pending' });
    console.log(`✅ Found ${pendingRequests.length} pending requests`);

    // Test getting approved requests
    const approvedRequests = await GroupRequest.find({ status: 'approved' });
    console.log(`✅ Found ${approvedRequests.length} approved requests`);

    // Test getting created groups
    const createdGroups = await StokvelGroup.find({
      name: TEST_CONFIG.testGroupRequest.requestedGroupName
    }).populate('admin', 'name email');

    console.log(`✅ Found ${createdGroups.length} created groups from requests`);

    return {
      allRequests,
      pendingRequests,
      approvedRequests,
      createdGroups
    };

  } catch (error) {
    console.error('❌ Failed to retrieve data:', error);
    throw error;
  }
}

async function cleanupTestData() {
  console.log('\n🧹 Cleaning up test data...');
  
  try {
    const { User } = require('../models/User');
    const { GroupRequest } = require('../models/GroupRequest');
    const { StokvelGroup } = require('../models/StokvelGroup');

    // Clean up test data
    await User.deleteMany({ email: { $in: [TEST_CONFIG.testUser.email, TEST_CONFIG.testAdmin.email] } });
    await GroupRequest.deleteMany({ requestedGroupName: TEST_CONFIG.testGroupRequest.requestedGroupName });
    await StokvelGroup.deleteMany({ name: TEST_CONFIG.testGroupRequest.requestedGroupName });

    console.log('✅ Test data cleaned up successfully');

  } catch (error) {
    console.error('❌ Failed to cleanup test data:', error);
  }
}

async function runIntegrationTest() {
  console.log('🚀 Starting Group Request System Integration Test\n');

  try {
    // Connect to database
    await connectToDatabase();

    // Create test data
    const testData = await createTestData();

    // Test group request creation
    const groupRequest = await testGroupRequestCreation(testData);

    // Test group request approval
    const createdGroup = await testGroupRequestApproval(groupRequest, testData);

    // Test data retrieval
    const retrievalResults = await testDataRetrieval();

    // Cleanup
    await cleanupTestData();

    console.log('\n🎉 Integration test completed successfully!');
    console.log('\n📋 Test Summary:');
    console.log('✅ Database connection');
    console.log('✅ Test data creation');
    console.log('✅ Group request creation');
    console.log('✅ Group request approval');
    console.log('✅ Group creation from request');
    console.log('✅ Data retrieval and queries');
    console.log('✅ Test data cleanup');

  } catch (error) {
    console.error('\n💥 Integration test failed:', error);
    await cleanupTestData();
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('\n👋 Disconnected from MongoDB');
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  runIntegrationTest();
}

module.exports = {
  runIntegrationTest,
  TEST_CONFIG
};
