# AI Chat System Testing Guide

## 🧪 **Testing Priority System**

The AI chat system now follows this priority order:

1. **🥇 Primary**: Full AI with OpenAI GPT (when quota/billing is available)
2. **🥈 Fallback**: Static knowledge base (only when OpenAI quota exceeded)
3. **🥉 Error**: User-friendly error messages (for other technical issues)

## 🔍 **How to Test Each Mode**

### **1. Test Full AI Mode (Primary)**

**Prerequisites:**
- Valid OpenAI API key with available quota
- Proper billing setup in OpenAI account

**Test Steps:**
1. Open the chat
2. Look for "🟢 AI Active" status in header
3. Ask: "How can I join a group?"
4. **Expected**: Intelligent, contextual response from GPT
5. **Response should**: Be conversational, personalized, and detailed

**Sample Questions to Test:**
```
- "What products do you have in electronics?"
- "How does bulk ordering work?"
- "Can you help me track my order?"
- "What are the benefits of joining a group?"
```

### **2. Test Fallback Mode (Quota Exceeded)**

**To Simulate Quota Issues:**
1. Temporarily set invalid API key: `OPENAI_API_KEY=invalid_key`
2. Or wait for actual quota to be exceeded

**Test Steps:**
1. Open the chat
2. Look for "🟡 Basic Mode" status in header
3. Ask: "How can I join a group?"
4. **Expected**: Static response with warning message
5. **Response should start with**: "⚠️ I'm currently running in limited mode..."

**Fallback Responses Available:**
- Group joining instructions
- Product browsing help
- Order tracking guidance
- Account management help
- General platform information

### **3. Test Error Handling (Other Issues)**

**To Simulate Network Issues:**
1. Disconnect internet briefly
2. Or block API requests in browser dev tools

**Test Steps:**
1. Ask any question
2. **Expected**: User-friendly error message
3. **Should NOT**: Show technical error details
4. **Should NOT**: Hang or show infinite loading

## 🎯 **Current Status Verification**

### **Check Your Current Mode:**

1. **Visit Health Check**: `http://localhost:3000/api/ai/health`
2. **Look for**:
   ```json
   {
     "status": "healthy" | "needs_attention",
     "services": {
       "openai": {
         "configured": true,
         "keyLength": 107
       }
     }
   }
   ```

### **Chat Status Indicators:**

- **🟢 AI Active**: Full OpenAI integration working
- **🟡 Basic Mode**: Using fallback responses (quota/billing issue)
- **🔄 Checking...**: System checking service status

## 🔧 **Troubleshooting Guide**

### **Issue: Always in Basic Mode**

**Possible Causes:**
1. **OpenAI Quota Exceeded**
   - Check: [OpenAI Usage Dashboard](https://platform.openai.com/usage)
   - Fix: Add billing or wait for quota reset

2. **Invalid API Key**
   - Check: API key format (starts with `sk-proj-` or `sk-`)
   - Fix: Generate new key from OpenAI dashboard

3. **Billing Issues**
   - Check: Payment method in OpenAI account
   - Fix: Add valid payment method

### **Issue: No Response at All**

**Possible Causes:**
1. **Missing Environment Variables**
   - Check: `OPENAI_API_KEY`, `MONGODB_URL`, `JWT_SECRET`
   - Fix: Add missing variables to `.env.local`

2. **Database Connection Issues**
   - Check: MongoDB connection string
   - Fix: Verify database is accessible

### **Issue: Technical Error Messages**

**Possible Causes:**
1. **Network Connectivity**
   - Check: Internet connection
   - Fix: Retry when connection is stable

2. **Server Issues**
   - Check: Application logs
   - Fix: Restart application if needed

## 📊 **Expected Behavior Matrix**

| Scenario | Status Indicator | Response Type | User Experience |
|----------|------------------|---------------|-----------------|
| OpenAI Working | 🟢 AI Active | Full AI Response | Best - Intelligent, contextual |
| Quota Exceeded | 🟡 Basic Mode | Static Fallback | Good - Helpful but limited |
| Network Error | 🔄 Checking... | Error Message | Fair - Clear error explanation |
| Invalid Config | 🟡 Basic Mode | Static Fallback | Good - Still functional |

## ✅ **Success Criteria**

### **Full AI Mode Success:**
- [ ] Responses are intelligent and contextual
- [ ] Can answer complex questions about platform
- [ ] Provides personalized recommendations
- [ ] Status shows "AI Active"

### **Fallback Mode Success:**
- [ ] Clear warning about limited mode
- [ ] Provides helpful static responses
- [ ] Explains how to restore full functionality
- [ ] Status shows "Basic Mode"

### **Error Handling Success:**
- [ ] No infinite loading states
- [ ] User-friendly error messages
- [ ] Clear instructions for resolution
- [ ] Graceful degradation

## 🚀 **Recommended Testing Sequence**

1. **Start with Health Check**: Visit `/api/ai/health`
2. **Test Current Mode**: Ask a simple question
3. **Verify Status Indicator**: Check header status
4. **Test Edge Cases**: Try complex questions
5. **Monitor Logs**: Check console for errors

## 💡 **Pro Tips**

1. **Monitor OpenAI Usage**: Set up billing alerts
2. **Test Regularly**: Verify system works after deployments
3. **User Feedback**: Ask users about response quality
4. **Performance**: Monitor response times
5. **Fallback Quality**: Regularly update static responses

## 🔄 **Switching Between Modes**

### **To Enable Full AI:**
1. Ensure valid OpenAI API key
2. Add billing to OpenAI account
3. Restart application
4. Status should show "AI Active"

### **To Test Fallback:**
1. Temporarily invalidate API key
2. Or exhaust quota deliberately
3. Status should show "Basic Mode"
4. Restore key when testing complete

This testing approach ensures your AI chat system provides excellent user experience regardless of OpenAI service availability!
