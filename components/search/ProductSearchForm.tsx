// components/search/ProductSearchForm.tsx

"use client"

import type React from "react"
import { useState, useCallback, useEffect, useMemo, useRef } from "react"
import { debounce } from "lodash"
import { Search, X, Clock, TrendingUp, Filter } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { motion, AnimatePresence } from "framer-motion"
import { useGetAllProductsQuery } from "@/lib/redux/features/products/productsApiSlice"
import { useGetCategoriesQuery } from "@/lib/redux/features/categories/categoriesApiSlice"
import type { Product } from "@/types/product"
import type { ProductCategory } from "@/types/productCategory"

// Runtime product type that handles both string and object categories
interface RuntimeProduct extends Omit<Product, 'category'> {
  category: string | ProductCategory;
}

interface ProductSearchFormProps {
  onSearch: (results: Product[]) => void
  setIsLoading?: (loading: boolean) => void
}

export function ProductSearchForm({ onSearch, setIsLoading }: ProductSearchFormProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string>("")
  const [searchHistory, setSearchHistory] = useState<string[]>([])
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [suggestions, setSuggestions] = useState<Product[]>([])
  const searchRef = useRef<HTMLDivElement>(null)

  const { data: products = [] } = useGetAllProductsQuery()
  const { data: categories = [] } = useGetCategoriesQuery()

  // Load search history from localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const history = localStorage.getItem('searchHistory')
      if (history) {
        setSearchHistory(JSON.parse(history))
      }
    }
  }, [])

  // Click outside handler
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowSuggestions(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // Save search to history
  const saveToHistory = useCallback((term: string) => {
    if (term.trim() && !searchHistory.includes(term)) {
      const newHistory = [term, ...searchHistory.slice(0, 4)] // Keep only 5 recent searches
      setSearchHistory(newHistory)
      if (typeof window !== 'undefined') {
        localStorage.setItem('searchHistory', JSON.stringify(newHistory))
      }
    }
  }, [searchHistory])

  // Get category name helper
  const getCategoryName = useCallback((product: RuntimeProduct): string => {
    // Handle case where category is a string ID
    if (typeof product.category === 'string') {
      const categoryObj = categories.find(cat => cat._id === product.category)
      return categoryObj?.name || ''
    }
    // Handle case where category is a populated object
    else if (product.category && typeof product.category === 'object' && 'name' in product.category) {
      return String(product.category.name)
    }
    // Handle case where category might be null/undefined
    return ''
  }, [categories])

  // Enhanced search function
  const performSearch = useCallback((term: string, category: string = "") => {
    setIsLoading?.(true)

    let results = products.filter((product) => {
      const matchesSearch = !term ||
        product.name.toLowerCase().includes(term.toLowerCase()) ||
        product.description.toLowerCase().includes(term.toLowerCase())

      const runtimeProduct = product as unknown as RuntimeProduct
      const matchesCategory = !category || getCategoryName(runtimeProduct) === category

      return matchesSearch && matchesCategory
    })

    // Sort results by relevance
    if (term) {
      results = results.sort((a, b) => {
        const aNameMatch = a.name.toLowerCase().includes(term.toLowerCase())
        const bNameMatch = b.name.toLowerCase().includes(term.toLowerCase())

        if (aNameMatch && !bNameMatch) return -1
        if (!aNameMatch && bNameMatch) return 1

        return 0
      })
    }

    setTimeout(() => {
      onSearch(results)
      setIsLoading?.(false)
    }, 300) // Simulate search delay for better UX
  }, [products, onSearch, setIsLoading, getCategoryName])

  // Debounced search
  const debouncedSearch = useMemo(
    () => debounce((term: string, category: string) => {
      performSearch(term, category)
      if (term) {
        saveToHistory(term)
      }
    }, 300),
    [performSearch, saveToHistory]
  )

  // Get suggestions
  const getSuggestions = useCallback((term: string) => {
    if (!term || term.length < 2) {
      setSuggestions([])
      return
    }

    const filtered = products
      .filter(product =>
        product.name.toLowerCase().includes(term.toLowerCase()) ||
        product.description.toLowerCase().includes(term.toLowerCase())
      )
      .slice(0, 5)

    setSuggestions(filtered)
  }, [products])

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const term = e.target.value
    setSearchTerm(term)
    setShowSuggestions(true)

    getSuggestions(term)
    debouncedSearch(term, selectedCategory)
  }

  const handleSuggestionClick = (product: Product) => {
    setSearchTerm(product.name)
    setShowSuggestions(false)
    performSearch(product.name, selectedCategory)
    saveToHistory(product.name)
  }

  const handleHistoryClick = (term: string) => {
    setSearchTerm(term)
    setShowSuggestions(false)
    performSearch(term, selectedCategory)
  }

  const handleCategorySelect = (category: string) => {
    setSelectedCategory(category)
    performSearch(searchTerm, category)
  }

  const clearSearch = () => {
    setSearchTerm("")
    setSelectedCategory("")
    setShowSuggestions(false)
    performSearch("", "")
  }

  const popularSearches = ["Electronics", "Clothing", "Home", "Beauty", "Sports"]

  return (
    <div className="space-y-4">
      {/* Main Search Input */}
      <div className="relative" ref={searchRef}>
        <div className="relative">
          <Search className="absolute left-3 sm:left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4 sm:h-5 sm:w-5" />
          <Input
            type="text"
            placeholder="Search for products, brands, categories..."
            value={searchTerm}
            onChange={handleSearchChange}
            onFocus={() => setShowSuggestions(true)}
            className="w-full pl-10 sm:pl-12 pr-10 sm:pr-12 py-3 sm:py-4 text-base sm:text-lg rounded-xl sm:rounded-2xl border-2 border-gray-200/50 focus:border-[#2A7C6C] focus:ring-4 focus:ring-[#2A7C6C]/20 transition-all duration-300 bg-white/70 backdrop-blur-sm shadow-lg hover:shadow-xl"
            style={{ fontFamily: "Avenir, sans-serif" }}
            autoFocus
          />
          {searchTerm && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearSearch}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 rounded-full hover:bg-gray-100"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* Suggestions Dropdown */}
        <AnimatePresence>
          {showSuggestions && (searchTerm || searchHistory.length > 0) && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="absolute top-full left-0 right-0 mt-2 bg-white/95 backdrop-blur-xl rounded-xl sm:rounded-2xl shadow-2xl border border-gray-200/50 z-[60] max-h-60 sm:max-h-80 overflow-y-auto"
            >
              {/* Search Suggestions */}
              {suggestions.length > 0 && (
                <div className="p-3 sm:p-4">
                  <h4 className="text-xs sm:text-sm font-semibold text-gray-700 mb-2 sm:mb-3 flex items-center gap-2">
                    <Search className="h-3 w-3 sm:h-4 sm:w-4" />
                    Product Suggestions
                  </h4>
                  <div className="space-y-2">
                    {suggestions.map((product) => (
                      <button
                        key={product._id}
                        onClick={() => handleSuggestionClick(product)}
                        className="w-full text-left p-2 sm:p-3 rounded-lg sm:rounded-xl hover:bg-gray-50 transition-colors duration-200 flex items-center gap-2 sm:gap-3"
                      >
                        <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                          <Search className="h-4 w-4 text-gray-400" />
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">{product.name}</p>
                          <p className="text-sm text-gray-500">R{product.price.toFixed(2)}</p>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Search History */}
              {searchHistory.length > 0 && !searchTerm && (
                <div className="p-4 border-t border-gray-100">
                  <h4 className="text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    Recent Searches
                  </h4>
                  <div className="space-y-2">
                    {searchHistory.map((term, index) => (
                      <button
                        key={index}
                        onClick={() => handleHistoryClick(term)}
                        className="w-full text-left p-2 rounded-lg hover:bg-gray-50 transition-colors duration-200 text-gray-700"
                      >
                        {term}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Popular Searches */}
              {!searchTerm && (
                <div className="p-4 border-t border-gray-100">
                  <h4 className="text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                    <TrendingUp className="h-4 w-4" />
                    Popular Searches
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {popularSearches.map((term) => (
                      <button
                        key={term}
                        onClick={() => handleHistoryClick(term)}
                        className="px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-full text-sm text-gray-700 transition-colors duration-200"
                      >
                        {term}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Category Filters */}
      {categories.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="flex items-center gap-3 overflow-x-auto pb-2"
        >
          <div className="flex items-center gap-2 flex-shrink-0">
            <Filter className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">Categories:</span>
          </div>

          <Button
            variant={selectedCategory === "" ? "default" : "outline"}
            size="sm"
            onClick={() => handleCategorySelect("")}
            className={`rounded-full flex-shrink-0 ${
              selectedCategory === ""
                ? "bg-[#2A7C6C] text-white"
                : "bg-white/70 text-gray-700 hover:bg-white"
            }`}
          >
            All
          </Button>

          {categories.map((category) => (
            <Button
              key={category._id}
              variant={selectedCategory === category.name ? "default" : "outline"}
              size="sm"
              onClick={() => handleCategorySelect(category.name)}
              className={`rounded-full flex-shrink-0 ${
                selectedCategory === category.name
                  ? "bg-[#2A7C6C] text-white"
                  : "bg-white/70 text-gray-700 hover:bg-white"
              }`}
            >
              {category.name}
            </Button>
          ))}
        </motion.div>
      )}

      {/* Active Filters */}
      {(searchTerm || selectedCategory) && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center gap-2 flex-wrap"
        >
          <span className="text-sm text-gray-600">Active filters:</span>
          {searchTerm && (
            <Badge variant="secondary" className="bg-blue-100 text-blue-800">
              Search: "{searchTerm}"
              <button
                onClick={() => {
                  setSearchTerm("")
                  performSearch("", selectedCategory)
                }}
                className="ml-2 hover:text-blue-900"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
          {selectedCategory && (
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              Category: {selectedCategory}
              <button
                onClick={() => {
                  setSelectedCategory("")
                  performSearch(searchTerm, "")
                }}
                className="ml-2 hover:text-green-900"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
        </motion.div>
      )}
    </div>
  )
}

