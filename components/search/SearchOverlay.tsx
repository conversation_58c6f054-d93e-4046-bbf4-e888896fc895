// components/search/SearchOverlay.tsx
"use client"

import { useEffect, useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { X, Search, Star,  Grid, List, Sparkles, TrendingUp, Heart, Eye } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ProductSearchForm } from "@/components/search/ProductSearchForm"
import Image from "next/image"
import { useRouter } from "next/navigation"
import type { Product } from "@/types/product"

interface SearchOverlayProps {
  isOpen: boolean
  onClose: () => void
}

type ViewMode = "grid" | "list"
type SortOption = "relevance" | "price-low" | "price-high" | "newest" | "rating"

export function SearchOverlay({ isOpen, onClose }: SearchOverlayProps) {
  const [searchResults, setSearchResults] = useState<Product[]>([])
  const [viewMode, setViewMode] = useState<ViewMode>("grid")
  const [sortBy, setSortBy] = useState<SortOption>("relevance")
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden"
    } else {
      document.body.style.overflow = "unset"
    }

    return () => {
      document.body.style.overflow = "unset"
    }
  }, [isOpen])

  // Sort results based on selected option
  const sortedResults = [...searchResults].sort((a, b) => {
    switch (sortBy) {
      case "price-low":
        return a.price - b.price
      case "price-high":
        return b.price - a.price
      case "newest":
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      case "rating":
        return (b.averageRating || 0) - (a.averageRating || 0)
      default:
        return 0
    }
  })

  if (!isOpen) return null

  // Modern product card component
  const ModernProductCard = ({ product, viewMode }: { product: Product; viewMode: ViewMode }) => {
    const [isFavorite, setIsFavorite] = useState(false)

    // Format price function
    const formatPrice = (price: number) => {
      return new Intl.NumberFormat('en-ZA', {
        style: 'currency',
        currency: 'ZAR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(price);
    };

    // Check if product is popular or trending
    const isPopular = (product.averageRating && product.averageRating >= 4.5) || product.stock > 50;
    const isTrending = (product.originalPrice && product.originalPrice > product.price) ||
                      (new Date(product.createdAt).getTime() > Date.now() - 7 * 24 * 60 * 60 * 1000);

    if (viewMode === "list") {
      return (
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100"
        >
          <div className="flex p-4 gap-4">
            <div className="relative w-24 h-24 flex-shrink-0">
              <Image
                src={product.image && product.image.trim() !== '' ? `/api/images/${product.image}` : "/placeholder.svg"}
                alt={product.name}
                fill
                className="object-contain rounded-xl"
                onError={(e) => {
                  const target = e.target as HTMLImageElement
                  target.src = "/placeholder.svg"
                }}
              />
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h3 className="font-bold text-lg text-gray-900 line-clamp-1 mb-1">{product.name}</h3>
                  <p className="text-sm text-gray-600 line-clamp-2 mb-2">{product.description}</p>
                  <div className="flex items-center gap-2 mb-2">
                    {product.averageRating && (
                      <div className="flex items-center">
                        <Star className="h-4 w-4 text-yellow-400 fill-current" />
                        <span className="text-sm text-gray-600 ml-1">{product.averageRating.toFixed(1)}</span>
                      </div>
                    )}
                    {isPopular && (
                      <Badge className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs">
                        <Sparkles className="h-3 w-3 mr-1" />
                        Popular
                      </Badge>
                    )}
                    {isTrending && !isPopular && (
                      <Badge className="bg-gradient-to-r from-pink-500 to-purple-600 text-white text-xs">
                        <TrendingUp className="h-3 w-3 mr-1" />
                        Trending
                      </Badge>
                    )}
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-[#2A7C6C] mb-2">{formatPrice(product.price)}</div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setIsFavorite(!isFavorite)}
                      className={`h-8 w-8 p-0 ${isFavorite ? 'text-red-500 border-red-500' : ''}`}
                    >
                      <Heart className={`h-4 w-4 ${isFavorite ? 'fill-current' : ''}`} />
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => router.push(`/products/${product._id}`)}
                      className="bg-[#2A7C6C] hover:bg-[#236358] text-white"
                    >
                      <Eye className="mr-1 h-4 w-4" />
                      View
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )
    }

    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        whileHover={{ y: -8, scale: 1.02 }}
        transition={{ duration: 0.3 }}
        className="group h-full"
      >
        <div className="h-full flex flex-col overflow-hidden bg-white/95 backdrop-blur-sm rounded-2xl md:rounded-3xl shadow-md md:shadow-lg hover:shadow-xl md:hover:shadow-2xl transition-all duration-300 md:duration-500 border border-gray-100 relative">
          {/* Gradient Border Effect - Simplified on mobile */}
          <div className="hidden md:block absolute inset-0 rounded-3xl bg-gradient-to-br from-[#2A7C6C]/20 via-[#7FDBCA]/10 to-[#2A7C6C]/20 p-[1px] opacity-0 group-hover:opacity-100 transition-opacity duration-500">
            <div className="h-full w-full rounded-3xl bg-white" />
          </div>

          {/* Product Image Container - Mobile Optimized */}
          <div className="relative h-40 md:h-48 w-full overflow-hidden bg-gradient-to-br from-gray-50/50 via-white to-purple-50/30 rounded-t-2xl md:rounded-t-3xl">
            <Image
              src={product.image && product.image.trim() !== '' ? `/api/images/${product.image}` : "/placeholder.svg"}
              alt={product.name}
              fill
              className="object-contain p-3 md:p-4 group-hover:scale-105 md:group-hover:scale-110 transition-all duration-300 md:duration-500 ease-out"
              onError={(e) => {
                const target = e.target as HTMLImageElement
                target.src = "/placeholder.svg"
              }}
            />

            {/* Badges - Mobile Optimized */}
            <div className="absolute top-2 md:top-3 left-2 md:left-3 right-2 md:right-3 flex justify-between items-start">
              <div className="flex flex-col gap-2">
                {product.originalPrice && product.originalPrice > product.price && (
                  <Badge className="bg-gradient-to-r from-red-500 to-pink-600 text-white font-bold text-xs">
                    -{Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}% OFF
                  </Badge>
                )}
                {isPopular && (
                  <Badge className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white font-semibold text-xs">
                    <Sparkles className="h-3 w-3 mr-1" />
                    Popular
                  </Badge>
                )}
                {isTrending && !isPopular && (
                  <Badge className="bg-gradient-to-r from-pink-500 to-purple-600 text-white font-semibold text-xs">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    Trending
                  </Badge>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsFavorite(!isFavorite)}
                  className={`h-8 w-8 p-0 rounded-full bg-white/90 backdrop-blur-sm ${
                    isFavorite ? 'text-red-500 border-red-500' : 'text-gray-600 hover:text-red-500'
                  }`}
                >
                  <Heart className={`h-4 w-4 ${isFavorite ? 'fill-current' : ''}`} />
                </Button>
              </div>
            </div>
          </div>

          {/* Product Details */}
          <div className="flex-1 p-6 relative z-10">
            <div className="space-y-4 h-full flex flex-col">
              {/* Product Name */}
              <h3 className="font-bold text-xl text-gray-900 line-clamp-2 leading-tight group-hover:text-[#2A7C6C] transition-colors duration-300">
                {product.name}
              </h3>

              {/* Rating */}
              {product.averageRating && (
                <div className="flex items-center gap-2">
                  <div className="flex items-center">
                    <Star className="h-4 w-4 text-yellow-400 fill-current" />
                    <span className="text-sm text-gray-600 ml-1">{product.averageRating.toFixed(1)}</span>
                  </div>
                  {product.reviewCount && (
                    <span className="text-sm text-gray-500">({product.reviewCount} reviews)</span>
                  )}
                </div>
              )}

              {/* Price Section */}
              <div className="space-y-2 flex-1">
                <div className="flex items-baseline gap-2">
                  <span className="text-2xl font-black text-[#2A7C6C]">
                    {formatPrice(product.price)}
                  </span>
                  {product.originalPrice && product.originalPrice > product.price && (
                    <span className="text-lg text-gray-400 line-through">
                      {formatPrice(product.originalPrice)}
                    </span>
                  )}
                </div>

                {/* Stock Status */}
                <div className="flex items-center gap-2">
                  <div className="h-2 w-2 bg-green-500 rounded-full" />
                  <span className="text-sm text-gray-600">
                    {product.stock > 10 ? 'In Stock' : `Only ${product.stock} left`}
                  </span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="space-y-3 pt-4">
                <Button
                  onClick={() => router.push(`/products/${product._id}`)}
                  className="w-full bg-gradient-to-r from-[#2A7C6C] to-[#1e5b4f] hover:from-[#236358] hover:to-[#164239] text-white font-semibold py-3 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl"
                >
                  <Eye className="mr-2 h-4 w-4" />
                  View Details
                </Button>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    )
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
          className="fixed inset-0 bg-black/60 backdrop-blur-md z-50 flex items-end md:items-center justify-center p-0 md:p-6 lg:p-8"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.9, y: 100 }}
            animate={{ scale: 1, y: 0 }}
            exit={{ scale: 0.9, y: 100 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            className="bg-white/95 backdrop-blur-xl rounded-t-3xl md:rounded-3xl w-full max-w-7xl h-[90vh] md:h-[90vh] overflow-hidden shadow-2xl border border-white/20 relative flex flex-col"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Gradient Background Overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-purple-50/30 to-emerald-50/50 rounded-3xl" />

            {/* Decorative Elements */}
            <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl" />
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-emerald-400/20 to-teal-400/20 rounded-full blur-2xl" />

            {/* Header - Mobile Optimized */}
            <div className="relative p-3 md:p-6 lg:p-8 border-b border-gray-200/30 flex-shrink-0">
              {/* Mobile drag handle */}
              <div className="md:hidden w-12 h-1 bg-gray-300 rounded-full mx-auto mb-4"></div>

              <div className="flex justify-between items-center mb-3 md:mb-6">
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="flex items-center gap-2 md:gap-4"
                >
                  <div className="h-8 w-8 md:h-12 md:w-12 rounded-lg md:rounded-2xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center shadow-lg">
                    <Search className="h-4 w-4 md:h-6 md:w-6 text-white" />
                  </div>
                  <div>
                    <h2
                      className="text-lg md:text-2xl lg:text-3xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent"
                      style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
                    >
                      Discover Products
                    </h2>
                    <p
                      className="text-gray-600 mt-0.5 md:mt-1 text-xs md:text-base"
                      style={{ fontFamily: "Avenir, sans-serif" }}
                    >
                      Search through our premium collection
                    </p>
                  </div>
                </motion.div>

                <Button
                  variant="ghost"
                  size="icon"
                  onClick={onClose}
                  className="h-8 w-8 md:h-10 md:w-10 rounded-full bg-white/80 backdrop-blur-sm border border-gray-200/50 text-gray-600 hover:text-gray-800 hover:bg-white/90 shadow-lg transition-all duration-200 flex-shrink-0"
                >
                  <X className="h-4 w-4 md:h-5 md:w-5" />
                </Button>
              </div>

              {/* Search Form */}
              <ProductSearchForm onSearch={setSearchResults} setIsLoading={setIsLoading} />

              {/* Controls - Mobile Optimized */}
              {searchResults.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                  className="flex flex-col gap-3 mt-3 md:mt-6 p-3 md:p-4 bg-white/50 backdrop-blur-sm rounded-xl border border-gray-200/30"
                >
                  <div className="flex flex-col md:flex-row md:items-center justify-between gap-3">
                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4">
                      <span className="text-xs md:text-sm text-gray-600 font-medium">
                        {searchResults.length} product{searchResults.length !== 1 ? 's' : ''} found
                      </span>

                      {/* View Mode Toggle - Hidden on mobile */}
                      <div className="hidden md:flex items-center bg-gray-100 rounded-lg p-1">
                        <Button
                          variant={viewMode === "grid" ? "default" : "ghost"}
                          size="sm"
                          onClick={() => setViewMode("grid")}
                          className={`h-7 md:h-8 px-2 md:px-3 ${viewMode === "grid" ? "bg-white shadow-sm" : ""}`}
                        >
                          <Grid className="h-3 w-3 md:h-4 md:w-4" />
                        </Button>
                        <Button
                          variant={viewMode === "list" ? "default" : "ghost"}
                          size="sm"
                          onClick={() => setViewMode("list")}
                          className={`h-7 md:h-8 px-2 md:px-3 ${viewMode === "list" ? "bg-white shadow-sm" : ""}`}
                        >
                          <List className="h-3 w-3 md:h-4 md:w-4" />
                        </Button>
                      </div>
                    </div>

                    {/* Sort Options */}
                    <div className="flex items-center gap-2">
                      <span className="text-xs md:text-sm text-gray-600">Sort:</span>
                      <select
                        value={sortBy}
                        onChange={(e) => setSortBy(e.target.value as SortOption)}
                        className="text-xs md:text-sm bg-white border border-gray-200 rounded-lg px-2 md:px-3 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="relevance">Relevance</option>
                        <option value="price-low">Price: Low to High</option>
                        <option value="price-high">Price: High to Low</option>
                        <option value="newest">Newest First</option>
                        <option value="rating">Highest Rated</option>
                      </select>
                    </div>
                  </div>
                </motion.div>
              )}
            </div>

            {/* Results */}
            <div className="relative p-4 sm:p-6 lg:p-8 overflow-y-auto flex-1 min-h-0">
              {isLoading ? (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="flex items-center justify-center py-20"
                >
                  <div className="flex items-center gap-3">
                    <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
                    <span className="text-gray-700 font-medium">Searching products...</span>
                  </div>
                </motion.div>
              ) : searchResults.length > 0 ? (
                <motion.div
                  layout
                  className={
                    viewMode === "grid"
                      ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-6"
                      : "space-y-3 md:space-y-4"
                  }
                >
                  <AnimatePresence>
                    {sortedResults.map((product, index) => (
                      <motion.div
                        key={product._id}
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.9 }}
                        transition={{ duration: 0.3, delay: index * 0.05 }}
                      >
                        <ModernProductCard product={product} viewMode={viewMode} />
                      </motion.div>
                    ))}
                  </AnimatePresence>
                </motion.div>
              ) : (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="text-center py-20"
                >
                  <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center">
                    <Search className="h-10 w-10 text-gray-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-800 mb-2">No products found</h3>
                  <p className="text-gray-600 max-w-md mx-auto">
                    Try adjusting your search terms or browse our categories to discover amazing products.
                  </p>
                </motion.div>
              )}
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

