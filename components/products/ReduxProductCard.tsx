"use client";

import { useState } from "react";
import Image from "next/image";
import { ShoppingCart, CheckCircle, Users } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { useAddToCartMutation } from "@/lib/redux/features/cart/cartApiSlice";
import { useAuth } from "@/context/AuthContext";
import { useGroupMembership } from "@/lib/redux/hooks/useGroupMembership";
import type { Product } from "@/types/product";
import { ConfirmationModal } from "@/components/ConfirmationModal";
import { JoinGroupModal } from "@/components/modals/JoinGroupModal";
import { useRouter } from "next/navigation";

interface ProductCardProps {
  product: Product;
  groupId: string;
}

export function ReduxProductCard({ product, groupId }: ProductCardProps) {
  const [addToCart, { isLoading: isAdding }] = useAddToCartMutation();
  const { user } = useAuth();
  const { userGroups } = useGroupMembership(user?._id);
  const [success, setSuccess] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isJoinGroupOpen, setIsJoinGroupOpen] = useState(false);
  const router = useRouter();

  const handleAddToCart = async () => {
    // If user is not logged in, open the join group modal
    if (!user) {
      setIsJoinGroupOpen(true);
      return;
    }

    // If user is logged in but not in any group, open the join group modal
    if (userGroups.length === 0) {
      setIsJoinGroupOpen(true);
      return;
    }

    // Store the current group ID in localStorage
    localStorage.setItem('currentGroupId', groupId);
    console.log('ReduxProductCard - Set currentGroupId in localStorage:', groupId);

    try {
      const cartItem = {
        userId: user._id,
        productId: product._id,
        quantity: 1, // Always add exactly 1 item when clicked
        groupId
      };

      await addToCart(cartItem).unwrap();
      setSuccess(true);
      setIsModalOpen(true);

      // Reset success state after 2 seconds
      setTimeout(() => setSuccess(false), 2000);
    } catch (error) {
      console.error('Failed to add item to cart:', error);
      // Optionally show an error message to the user
    }
  };

  const getButtonText = () => {
    if (isAdding) {
      return 'Adding...';
    }

    if (success) {
      return 'Added';
    }

    if (!user || userGroups.length === 0) {
      return 'Join Group to Shop';
    }

    return 'Add to Cart';
  };

  const getButtonIcon = () => {
    if (success) {
      return <CheckCircle className="mr-2 h-4 w-4" />;
    }

    if (!user || userGroups.length === 0) {
      return <Users className="mr-2 h-4 w-4" />;
    }

    return <ShoppingCart className="mr-2 h-4 w-4" />;
  };

  return (
    <>
      <Card className="overflow-hidden transition-all duration-200 hover:shadow-md">
        <div className="relative h-48 w-full bg-gray-50">
          <Image
            src={`/api/images/${product.image}`}
            alt={product.name}
            width={300}
            height={300}
            className="object-contain h-full w-full p-2"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = "/placeholder.svg";
            }}
          />
        </div>
        <CardContent className="p-4">
          <h3 className="font-semibold text-lg mb-2 line-clamp-2 h-14">{product.name}</h3>
          <p className="text-purple-600 font-bold">R{product.price.toFixed(2)}</p>
        </CardContent>
        <CardFooter className="flex flex-col gap-2">
          <Button
            className="w-full bg-purple-600 hover:bg-purple-700"
            onClick={() => router.push(`/products/${product._id}`)}
          >
            View Details
          </Button>
          <Button
            className={`w-full ${
              !user || userGroups.length === 0
                ? 'bg-emerald-600 hover:bg-emerald-700 text-white'
                : 'bg-gray-100 hover:bg-gray-200 text-gray-800'
            }`}
            onClick={handleAddToCart}
            disabled={isAdding}
          >
            {getButtonIcon()}
            {getButtonText()}
          </Button>
        </CardFooter>
      </Card>

      <ConfirmationModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        productName={product.name}
      />

      <JoinGroupModal
        isOpen={isJoinGroupOpen}
        onClose={() => setIsJoinGroupOpen(false)}
      />
    </>
  );
}
