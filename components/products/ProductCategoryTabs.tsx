// components/products/ProductCategoryTabs.tsx

"use client";

import { motion } from "framer-motion";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { cn } from "@/lib/utils";
import type { ProductCategory } from "@/types/productCategory";

interface ProductCategoryTabsProps {
  categories: ProductCategory[];
  selectedCategory: string;
  onCategoryChange: (category: string) => void;
  className?: string;
}

export function ProductCategoryTabs({
  categories,
  selectedCategory,
  onCategoryChange,
  className
}: ProductCategoryTabsProps) {
  return (
    <div className={cn("w-full", className)}>
      <Tabs value={selectedCategory} onValueChange={onCategoryChange}>
        <TabsList className="w-full justify-start overflow-x-auto scrollbar-hide bg-gray-100/80 backdrop-blur-sm p-1 h-auto">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex gap-1 min-w-max"
          >
            <TabsTrigger
              value="all"
              className={cn(
                "px-4 py-2.5 text-sm font-medium rounded-lg transition-all duration-300",
                "data-[state=active]:bg-[#2A7C6C] data-[state=active]:text-white data-[state=active]:shadow-md",
                "hover:bg-white/60 whitespace-nowrap flex-shrink-0"
              )}
            >
              All Products
            </TabsTrigger>

            {categories.map((category, index) => (
              <motion.div
                key={category._id}
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.05 }}
              >
                <TabsTrigger
                  value={category._id}
                  className={cn(
                    "px-4 py-2.5 text-sm font-medium rounded-lg transition-all duration-300",
                    "data-[state=active]:bg-[#2A7C6C] data-[state=active]:text-white data-[state=active]:shadow-md",
                    "hover:bg-white/60 whitespace-nowrap flex-shrink-0"
                  )}
                >
                  {category.name}
                </TabsTrigger>
              </motion.div>
            ))}
          </motion.div>
        </TabsList>
      </Tabs>

      {/* Mobile scroll indicator */}
      <div className="lg:hidden mt-2 flex justify-center">
        <div className="flex gap-1">
          {[...Array(Math.min(categories.length + 1, 5))].map((_, i) => (
            <div
              key={i}
              className="w-1.5 h-1.5 rounded-full bg-gray-300"
            />
          ))}
        </div>
      </div>
    </div>
  );
}

