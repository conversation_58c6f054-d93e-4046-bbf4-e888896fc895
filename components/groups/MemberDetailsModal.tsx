// components/groups/MemberDetailsModal.tsx
"use client";

import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON>ton } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { 
  Mail, 
  Phone, 
  Calendar, 
  ShoppingBag, 
  DollarSign, 
  TrendingUp, 
  MapPin,
  Clock,
  Star,
  MessageCircle,
  UserMinus,
  Shield
} from 'lucide-react';
import type { GroupMember } from '@/lib/redux/features/groupStats/groupStatsApiSlice';

interface MemberDetailsModalProps {
  member: GroupMember | null;
  isOpen: boolean;
  onClose: () => void;
  isAdmin?: boolean;
}

export default function MemberDetailsModal({ 
  member, 
  isOpen, 
  onClose, 
  isAdmin = false 
}: MemberDetailsModalProps) {
  if (!member) return null;

  const totalSpentNum = parseFloat(member.totalSpent?.replace(/[^0-9.-]+/g, "") || "0");
  const totalOrdersNum = parseInt(member.totalOrders || "0");
  const joinedDate = member.joinedAt ? new Date(member.joinedAt) : null;
  const daysSinceJoined = joinedDate ? Math.floor((Date.now() - joinedDate.getTime()) / (1000 * 60 * 60 * 24)) : 0;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-3">
            <Avatar className="h-12 w-12">
              <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${member.name}`} />
              <AvatarFallback>
                {member.name?.split(' ').map(n => n[0]).join('').toUpperCase() || 'NA'}
              </AvatarFallback>
            </Avatar>
            <div>
              <h3 className="text-xl font-semibold">{member.name}</h3>
              <div className="flex items-center space-x-2">
                {member.isOnline && (
                  <Badge className="bg-green-100 text-green-700">
                    Online
                  </Badge>
                )}
                {totalSpentNum > 5000 && (
                  <Badge className="bg-yellow-100 text-yellow-800">
                    <Star className="h-3 w-3 mr-1" />
                    Top Contributor
                  </Badge>
                )}
              </div>
            </div>
          </DialogTitle>
          <DialogDescription>
            Member since {joinedDate?.toLocaleDateString() || 'Unknown'} • {daysSinceJoined} days ago
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Contact Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center space-x-3">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span>{member.email || 'Not provided'}</span>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="h-4 w-4 text-muted-foreground" />
                <span>{member.phone || 'Not provided'}</span>
              </div>
              <div className="flex items-center space-x-3">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span>Joined {joinedDate?.toLocaleDateString() || 'Unknown'}</span>
              </div>
              {member.lastSeen && (
                <div className="flex items-center space-x-3">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span>Last seen {new Date(member.lastSeen).toLocaleDateString()}</span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Activity Statistics */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Activity & Contributions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="flex items-center justify-center space-x-2 mb-2">
                    <ShoppingBag className="h-5 w-5 text-blue-600" />
                    <span className="text-2xl font-bold text-blue-600">{totalOrdersNum}</span>
                  </div>
                  <p className="text-sm text-muted-foreground">Total Orders</p>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="flex items-center justify-center space-x-2 mb-2">
                    <DollarSign className="h-5 w-5 text-green-600" />
                    <span className="text-2xl font-bold text-green-600">
                      {member.totalSpent || 'R0.00'}
                    </span>
                  </div>
                  <p className="text-sm text-muted-foreground">Total Spent</p>
                </div>
              </div>
              
              {totalOrdersNum > 0 && (
                <div className="mt-4 p-4 bg-purple-50 rounded-lg">
                  <div className="flex items-center space-x-2 mb-2">
                    <TrendingUp className="h-5 w-5 text-purple-600" />
                    <span className="font-medium text-purple-600">Average Order Value</span>
                  </div>
                  <p className="text-2xl font-bold text-purple-600">
                    R{(totalSpentNum / totalOrdersNum).toLocaleString(undefined, { maximumFractionDigits: 2 })}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Member Insights */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Member Insights</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Activity Level</span>
                <Badge variant={totalOrdersNum > 10 ? "default" : totalOrdersNum > 5 ? "secondary" : "outline"}>
                  {totalOrdersNum > 10 ? "Very Active" : totalOrdersNum > 5 ? "Active" : totalOrdersNum > 0 ? "Moderate" : "Inactive"}
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Spending Category</span>
                <Badge variant={totalSpentNum > 5000 ? "default" : totalSpentNum > 1000 ? "secondary" : "outline"}>
                  {totalSpentNum > 5000 ? "High Spender" : totalSpentNum > 1000 ? "Regular Spender" : "Light Spender"}
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Member Status</span>
                <Badge variant={member.isOnline ? "default" : "outline"}>
                  {member.isOnline ? "Online" : "Offline"}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Admin Actions */}
          {isAdmin && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center space-x-2">
                  <Shield className="h-5 w-5" />
                  <span>Admin Actions</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  <Button variant="outline" size="sm">
                    <MessageCircle className="h-4 w-4 mr-2" />
                    Send Message
                  </Button>
                  <Button variant="outline" size="sm">
                    <Mail className="h-4 w-4 mr-2" />
                    Send Email
                  </Button>
                  <Button variant="outline" size="sm" className="text-orange-600 border-orange-200 hover:bg-orange-50">
                    <UserMinus className="h-4 w-4 mr-2" />
                    Remove from Group
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <Separator />

        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          <Button>
            <MessageCircle className="h-4 w-4 mr-2" />
            Contact Member
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
