"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useJoinGroupMutation } from "@/lib/redux/features/groups/groupsApiSlice";
import { useAuth } from "@/context/AuthContext";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Users,
  MapPin,
  TrendingUp,
  ShoppingCart,
  Star,
  DollarSign,
  CheckCircle,
  AlertCircle,
  Loader2,
  UserPlus,
  Info,
  LogIn,
  Mail,
  Lock,
  User,
  Phone,
  Eye,
  EyeOff
} from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import type { StokvelGroup } from "@/types/stokvelgroup";
import { toast } from "sonner";

interface JoinGroupModalProps {
  isOpen: boolean;
  onClose: () => void;
  group: StokvelGroup | null;
  onSuccess?: () => void;
}

export function JoinGroupModal({ isOpen, onClose, group, onSuccess }: JoinGroupModalProps) {
  const { user, login, signup } = useAuth();
  const [joinGroup, { isLoading }] = useJoinGroupMutation();
  const [step, setStep] = useState<'confirm' | 'auth' | 'relocation' | 'success' | 'error'>('confirm');
  const [authMode, setAuthMode] = useState<'login' | 'register'>('login');
  const [authLoading, setAuthLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [membershipStatus, setMembershipStatus] = useState<any>(null);
  const [membershipLoading, setMembershipLoading] = useState(false);

  // Auth form state
  const [authForm, setAuthForm] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    name: '',
    phone: ''
  });

  const [authErrors, setAuthErrors] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    name: '',
    phone: '',
    general: ''
  });

  // Check user's group membership status when modal opens and user is authenticated
  useEffect(() => {
    if (isOpen && user && group) {
      checkMembershipStatus();
    }
  }, [isOpen, user, group]);

  const checkMembershipStatus = async () => {
    if (!user) return;

    setMembershipLoading(true);
    try {
      const response = await fetch(`/api/users/${user._id}/group-membership-status`);
      const data = await response.json();

      if (data.success) {
        setMembershipStatus(data.data);

        // If user is already a member of this group, show success
        const isAlreadyMember = data.data.activeGroups.some((g: any) => g._id === group._id);
        if (isAlreadyMember) {
          setStep('success');
          return;
        }

        // If user has active groups and is trying to join a different group, show relocation step
        if (data.data.hasActiveGroup && data.data.requiresRelocation) {
          setStep('relocation');
          return;
        }
      }
    } catch (error) {
      console.error('Error checking membership status:', error);
    } finally {
      setMembershipLoading(false);
    }
  };

  // Check membership status after authentication
  const checkMembershipStatusAfterAuth = async (authenticatedUser: any) => {
    setMembershipLoading(true);
    try {
      const response = await fetch(`/api/users/${authenticatedUser._id}/group-membership-status`);
      const data = await response.json();

      if (data.success) {
        setMembershipStatus(data.data);

        // If user is already a member of this group, show success
        const isAlreadyMember = data.data.activeGroups.some((g: any) => g._id === group._id);
        if (isAlreadyMember) {
          setStep('success');
          return;
        }

        // If user has active groups and is trying to join a different group, show relocation step
        if (data.data.hasActiveGroup && data.data.requiresRelocation) {
          setStep('relocation');
          return;
        }

        // If user has no active groups, proceed to join directly
        await handleJoinGroup(authenticatedUser);
      }
    } catch (error) {
      console.error('Error checking membership status after auth:', error);
      // If membership check fails, try to join anyway
      await handleJoinGroup(authenticatedUser);
    } finally {
      setMembershipLoading(false);
    }
  };

  // Validation functions
  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone: string) => {
    const phoneRegex = /^(\+27|0)[6-8][0-9]{8}$/;
    return phoneRegex.test(phone.replace(/\s/g, ''));
  };

  const validateForm = () => {
    const errors = {
      email: '',
      password: '',
      confirmPassword: '',
      name: '',
      phone: '',
      general: ''
    };

    if (!authForm.email) {
      errors.email = 'Email is required';
    } else if (!validateEmail(authForm.email)) {
      errors.email = 'Please enter a valid email address';
    }

    if (!authForm.password) {
      errors.password = 'Password is required';
    } else if (authForm.password.length < 6) {
      errors.password = 'Password must be at least 6 characters';
    }

    if (authMode === 'register') {
      if (!authForm.name) {
        errors.name = 'Name is required';
      }

      if (!authForm.phone) {
        errors.phone = 'Phone number is required';
      } else if (!validatePhone(authForm.phone)) {
        errors.phone = 'Please enter a valid South African phone number';
      }

      if (!authForm.confirmPassword) {
        errors.confirmPassword = 'Please confirm your password';
      } else if (authForm.password !== authForm.confirmPassword) {
        errors.confirmPassword = 'Passwords do not match';
      }
    }

    setAuthErrors(errors);
    return !Object.values(errors).some(error => error !== '');
  };

  const handleAuth = async () => {
    if (!validateForm()) return;

    setAuthLoading(true);
    setAuthErrors(prev => ({ ...prev, general: '' }));

    try {
      let authenticatedUser;

      if (authMode === 'login') {
        authenticatedUser = await login(authForm.email, authForm.password, true, false);
        toast.success('Successfully logged in!');
      } else {
        // Normalize phone number for registration
        const normalizedPhone = authForm.phone.replace(/\s/g, '').replace(/^0/, '+27');
        authenticatedUser = await signup(authForm.name, authForm.email, normalizedPhone, authForm.password);
        toast.success('Account created and logged in successfully!');
      }

      // After successful authentication, check membership status before proceeding
      if (authenticatedUser && group) {
        await checkMembershipStatusAfterAuth(authenticatedUser);
      }
    } catch (error: any) {
      console.error('Authentication failed:', error);
      setAuthErrors(prev => ({
        ...prev,
        general: error.message || `${authMode === 'login' ? 'Login' : 'Registration'} failed. Please try again.`
      }));
    } finally {
      setAuthLoading(false);
    }
  };

  const handleJoinGroup = async (userToJoin = user, isRelocation = false) => {
    if (!userToJoin || !group) {
      setStep('auth');
      return;
    }

    try {
      await joinGroup({
        userId: userToJoin._id,
        groupId: group._id,
        isRelocation
      }).unwrap();

      setStep('success');
      toast.success(isRelocation
        ? `Successfully relocated to ${group.name}!`
        : `Successfully joined ${group.name}!`);
      onSuccess?.();
    } catch (error: any) {
      console.error('Failed to join group:', error);
      setStep('error');
      toast.error(error?.data?.message || "Failed to join group. Please try again.");
    }
  };

  const handleRelocation = async () => {
    await handleJoinGroup(user, true);
  };

  const handleClose = () => {
    setStep('confirm');
    setAuthMode('login');
    setAuthForm({
      email: '',
      password: '',
      confirmPassword: '',
      name: '',
      phone: ''
    });
    setAuthErrors({
      email: '',
      password: '',
      confirmPassword: '',
      name: '',
      phone: '',
      general: ''
    });
    onClose();
  };

  if (!group) return null;

  const isUserMember = user && group.members.includes(user._id);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-[#2F4858]">
            {step === 'confirm' && 'Join Stokvel Group'}
            {step === 'auth' && (authMode === 'login' ? 'Login to Continue' : 'Create Account')}
            {step === 'relocation' && 'Relocate to New Group'}
            {step === 'success' && 'Welcome to the Group!'}
            {step === 'error' && 'Join Failed'}
          </DialogTitle>
          <DialogDescription>
            {step === 'confirm' && `Review the details and join ${group.name}`}
            {step === 'auth' && (authMode === 'login'
              ? 'Please login to join the group'
              : 'Create an account to join the group')}
            {step === 'relocation' && 'You can only be a member of one group at a time. Relocating will move you from your current group to this new group.'}
            {step === 'success' && 'You have successfully joined the group'}
            {step === 'error' && 'There was an issue joining the group'}
          </DialogDescription>
        </DialogHeader>

        <AnimatePresence mode="wait">
          {step === 'confirm' && (
            <motion.div
              key="confirm"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              {/* Group Info Card */}
              <Card className="bg-gradient-to-r from-[#2A7C6C]/5 to-[#1E5A4F]/5 border-[#2A7C6C]/20">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h3 className="text-xl font-bold text-[#2F4858] mb-2">{group.name}</h3>
                      <p className="text-gray-600 mb-3">{group.description}</p>
                      {group.geolocation && (
                        <div className="flex items-center text-gray-500">
                          <MapPin className="h-4 w-4 mr-1" />
                          {group.geolocation}
                        </div>
                      )}
                    </div>
                    {isUserMember && (
                      <Badge variant="secondary" className="bg-green-100 text-green-700">
                        Already a Member
                      </Badge>
                    )}
                  </div>

                  {/* Stats Grid */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="bg-white rounded-lg p-4 text-center shadow-sm">
                      <Users className="h-6 w-6 mx-auto mb-2 text-[#2A7C6C]" />
                      <div className="text-lg font-semibold text-gray-900">{group.members.length}</div>
                      <div className="text-xs text-gray-600">Members</div>
                    </div>
                    
                    <div className="bg-white rounded-lg p-4 text-center shadow-sm">
                      <TrendingUp className="h-6 w-6 mx-auto mb-2 text-purple-600" />
                      <div className="text-lg font-semibold text-gray-900">
                        {formatCurrency(group.totalSales)}
                      </div>
                      <div className="text-xs text-gray-600">Total Sales</div>
                    </div>
                    
                    <div className="bg-white rounded-lg p-4 text-center shadow-sm">
                      <ShoppingCart className="h-6 w-6 mx-auto mb-2 text-orange-600" />
                      <div className="text-lg font-semibold text-gray-900">{group.activeOrders}</div>
                      <div className="text-xs text-gray-600">Active Orders</div>
                    </div>
                    
                    <div className="bg-white rounded-lg p-4 text-center shadow-sm">
                      <Star className="h-6 w-6 mx-auto mb-2 text-yellow-600" />
                      <div className="text-lg font-semibold text-gray-900">
                        {formatCurrency(group.avgOrderValue)}
                      </div>
                      <div className="text-xs text-gray-600">Avg Order</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Benefits Section */}
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    <Info className="h-5 w-5 text-[#2A7C6C] mr-2" />
                    <h4 className="font-semibold text-gray-900">Benefits of Joining</h4>
                  </div>
                  <ul className="space-y-2 text-sm text-gray-600">
                    <li className="flex items-center">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                      Access to bulk buying discounts and group orders
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                      Participate in community savings and collective purchasing
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                      Connect with neighbors and build local community
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                      Share delivery costs and coordinate group purchases
                    </li>
                  </ul>
                </CardContent>
              </Card>

              <Separator />

              {/* Action Buttons */}
              <div className="flex gap-3 pt-4">
                <Button 
                  variant="outline" 
                  onClick={handleClose}
                  className="flex-1"
                  disabled={isLoading}
                >
                  Cancel
                </Button>
                <Button
                  onClick={() => {
                    if (!user) {
                      setStep('auth');
                    } else {
                      handleJoinGroup();
                    }
                  }}
                  disabled={isLoading || isUserMember}
                  className="flex-1 bg-gradient-to-r from-[#2A7C6C] to-[#1E5A4F] hover:from-[#1E5A4F] hover:to-[#2A7C6C] text-white"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Joining...
                    </>
                  ) : isUserMember ? (
                    <>
                      <Users className="h-4 w-4 mr-2" />
                      Already a Member
                    </>
                  ) : !user ? (
                    <>
                      <LogIn className="h-4 w-4 mr-2" />
                      Login to Join
                    </>
                  ) : (
                    <>
                      <UserPlus className="h-4 w-4 mr-2" />
                      Join Group
                    </>
                  )}
                </Button>
              </div>
            </motion.div>
          )}

          {step === 'auth' && (
            <motion.div
              key="auth"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              {/* Auth Mode Toggle */}
              <div className="flex bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => setAuthMode('login')}
                  className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all ${
                    authMode === 'login'
                      ? 'bg-white text-[#2A7C6C] shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  Login
                </button>
                <button
                  onClick={() => setAuthMode('register')}
                  className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all ${
                    authMode === 'register'
                      ? 'bg-white text-[#2A7C6C] shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  Create Account
                </button>
              </div>

              {/* Auth Form */}
              <div className="space-y-4">
                {authMode === 'register' && (
                  <div className="space-y-2">
                    <Label htmlFor="name" className="text-sm font-medium text-gray-700">
                      Full Name
                    </Label>
                    <div className="relative">
                      <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                        <User className="h-4 w-4 text-gray-400" />
                      </div>
                      <Input
                        id="name"
                        type="text"
                        placeholder="Enter your full name"
                        value={authForm.name}
                        onChange={(e) => setAuthForm(prev => ({ ...prev, name: e.target.value }))}
                        className={`pl-10 h-12 ${authErrors.name ? 'border-red-500' : ''}`}
                      />
                    </div>
                    {authErrors.name && (
                      <p className="text-sm text-red-600">{authErrors.name}</p>
                    )}
                  </div>
                )}

                <div className="space-y-2">
                  <Label htmlFor="email" className="text-sm font-medium text-gray-700">
                    Email Address
                  </Label>
                  <div className="relative">
                    <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                      <Mail className="h-4 w-4 text-gray-400" />
                    </div>
                    <Input
                      id="email"
                      type="email"
                      placeholder="Enter your email"
                      value={authForm.email}
                      onChange={(e) => setAuthForm(prev => ({ ...prev, email: e.target.value }))}
                      className={`pl-10 h-12 ${authErrors.email ? 'border-red-500' : ''}`}
                    />
                  </div>
                  {authErrors.email && (
                    <p className="text-sm text-red-600">{authErrors.email}</p>
                  )}
                </div>

                {authMode === 'register' && (
                  <div className="space-y-2">
                    <Label htmlFor="phone" className="text-sm font-medium text-gray-700">
                      Phone Number
                    </Label>
                    <div className="relative">
                      <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                        <Phone className="h-4 w-4 text-gray-400" />
                      </div>
                      <Input
                        id="phone"
                        type="tel"
                        placeholder="0XX XXX XXXX"
                        value={authForm.phone}
                        onChange={(e) => setAuthForm(prev => ({ ...prev, phone: e.target.value }))}
                        className={`pl-10 h-12 ${authErrors.phone ? 'border-red-500' : ''}`}
                      />
                    </div>
                    {authErrors.phone && (
                      <p className="text-sm text-red-600">{authErrors.phone}</p>
                    )}
                  </div>
                )}

                <div className="space-y-2">
                  <Label htmlFor="password" className="text-sm font-medium text-gray-700">
                    Password
                  </Label>
                  <div className="relative">
                    <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                      <Lock className="h-4 w-4 text-gray-400" />
                    </div>
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      placeholder="Enter your password"
                      value={authForm.password}
                      onChange={(e) => setAuthForm(prev => ({ ...prev, password: e.target.value }))}
                      className={`pl-10 pr-10 h-12 ${authErrors.password ? 'border-red-500' : ''}`}
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                  </div>
                  {authErrors.password && (
                    <p className="text-sm text-red-600">{authErrors.password}</p>
                  )}
                </div>

                {authMode === 'register' && (
                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword" className="text-sm font-medium text-gray-700">
                      Confirm Password
                    </Label>
                    <div className="relative">
                      <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                        <Lock className="h-4 w-4 text-gray-400" />
                      </div>
                      <Input
                        id="confirmPassword"
                        type={showConfirmPassword ? "text" : "password"}
                        placeholder="Confirm your password"
                        value={authForm.confirmPassword}
                        onChange={(e) => setAuthForm(prev => ({ ...prev, confirmPassword: e.target.value }))}
                        className={`pl-10 pr-10 h-12 ${authErrors.confirmPassword ? 'border-red-500' : ''}`}
                      />
                      <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                      >
                        {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                    </div>
                    {authErrors.confirmPassword && (
                      <p className="text-sm text-red-600">{authErrors.confirmPassword}</p>
                    )}
                  </div>
                )}

                {authErrors.general && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                    <p className="text-sm text-red-600">{authErrors.general}</p>
                  </div>
                )}
              </div>

              <Separator />

              {/* Action Buttons */}
              <div className="flex gap-3 pt-4">
                <Button
                  variant="outline"
                  onClick={() => setStep('confirm')}
                  className="flex-1"
                  disabled={authLoading}
                >
                  Back
                </Button>
                <Button
                  onClick={handleAuth}
                  disabled={authLoading}
                  className="flex-1 bg-gradient-to-r from-[#2A7C6C] to-[#1E5A4F] hover:from-[#1E5A4F] hover:to-[#2A7C6C] text-white"
                >
                  {authLoading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      {authMode === 'login' ? 'Logging in...' : 'Creating Account...'}
                    </>
                  ) : (
                    <>
                      {authMode === 'login' ? (
                        <>
                          <LogIn className="h-4 w-4 mr-2" />
                          Login & Join Group
                        </>
                      ) : (
                        <>
                          <UserPlus className="h-4 w-4 mr-2" />
                          Create Account & Join
                        </>
                      )}
                    </>
                  )}
                </Button>
              </div>
            </motion.div>
          )}

          {step === 'relocation' && (
            <motion.div
              key="relocation"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              {/* Membership Restriction Notice */}
              <Card className="bg-blue-50 border-blue-200">
                <CardContent className="p-6">
                  <div className="flex items-start">
                    <Info className="h-5 w-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold text-blue-800 mb-2">Group Membership Policy</h4>
                      <p className="text-sm text-blue-700">
                        You cannot be a member of more than one group at a time. To join this group,
                        you'll need to relocate from your current group. This ensures focused participation
                        and better group dynamics.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Current Group Info */}
              {membershipStatus?.activeGroups?.length > 0 && (
                <Card className="bg-orange-50 border-orange-200">
                  <CardContent className="p-6">
                    <div className="flex items-center mb-4">
                      <AlertCircle className="h-5 w-5 text-orange-600 mr-2" />
                      <h4 className="font-semibold text-orange-800">Current Group Membership</h4>
                    </div>

                    {membershipStatus.multipleActiveGroups && (
                      <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                        <p className="text-sm text-red-700 font-medium">
                          ⚠️ You are currently a member of multiple groups. This is not allowed under our new policy.
                        </p>
                      </div>
                    )}

                    <div className="space-y-3">
                      {membershipStatus.activeGroups.map((activeGroup: any) => (
                        <div key={activeGroup._id} className="flex items-center justify-between p-3 bg-white rounded-lg border">
                          <div>
                            <p className="font-medium text-gray-900">{activeGroup.name}</p>
                            <p className="text-sm text-gray-600">{activeGroup.geolocation}</p>
                            <p className="text-xs text-gray-500">{activeGroup.members} members</p>
                          </div>
                          {activeGroup.isLatest && membershipStatus.multipleActiveGroups && (
                            <Badge variant="secondary" className="bg-blue-100 text-blue-700">
                              Latest
                            </Badge>
                          )}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* New Group Info */}
              <Card className="bg-green-50 border-green-200">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                    <h4 className="font-semibold text-green-800">New Group</h4>
                  </div>

                  <div className="p-3 bg-white rounded-lg border">
                    <p className="font-medium text-gray-900">{group.name}</p>
                    <p className="text-sm text-gray-600">{group.description}</p>
                    {group.geolocation && (
                      <div className="flex items-center text-gray-500 text-sm mt-1">
                        <MapPin className="h-4 w-4 mr-1" />
                        {group.geolocation}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Relocation Warning */}
              <Card className="bg-amber-50 border-amber-200">
                <CardContent className="p-6">
                  <div className="flex items-start">
                    <Info className="h-5 w-5 text-amber-600 mr-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold text-amber-800 mb-2">Important Information</h4>
                      <ul className="text-sm text-amber-700 space-y-1">
                        <li>• You can only be a member of one group at a time</li>
                        <li>• Relocating will remove you from your current group(s)</li>
                        <li>• Any pending orders will be transferred to the new group</li>
                        <li>• Your purchase history will be preserved</li>
                        {membershipStatus?.multipleActiveGroups && (
                          <li className="font-medium">• This will resolve your multiple group membership issue</li>
                        )}
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Separator />

              {/* Action Buttons */}
              <div className="flex gap-3 pt-4">
                <Button
                  variant="outline"
                  onClick={() => setStep('confirm')}
                  className="flex-1"
                  disabled={isLoading}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleRelocation}
                  disabled={isLoading}
                  className="flex-1 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Relocating...
                    </>
                  ) : (
                    <>
                      <UserPlus className="h-4 w-4 mr-2" />
                      {membershipStatus?.multipleActiveGroups ? 'Fix & Relocate' : 'Relocate to This Group'}
                    </>
                  )}
                </Button>
              </div>
            </motion.div>
          )}

          {step === 'success' && (
            <motion.div
              key="success"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className="text-center py-8"
            >
              <div className="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Welcome to {group.name}!
              </h3>
              <p className="text-gray-600 mb-6">
                You have successfully joined the group. You can now participate in group orders and access exclusive benefits.
              </p>
              <Button onClick={handleClose} className="bg-gradient-to-r from-[#2A7C6C] to-[#1E5A4F] text-white">
                Continue
              </Button>
            </motion.div>
          )}

          {step === 'error' && (
            <motion.div
              key="error"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className="text-center py-8"
            >
              <div className="bg-red-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <AlertCircle className="h-8 w-8 text-red-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Failed to Join Group
              </h3>
              <p className="text-gray-600 mb-6">
                There was an issue joining the group. Please try again or contact support if the problem persists.
              </p>
              <div className="flex gap-3 justify-center">
                <Button variant="outline" onClick={handleClose}>
                  Cancel
                </Button>
                <Button 
                  onClick={() => setStep('confirm')}
                  className="bg-gradient-to-r from-[#2A7C6C] to-[#1E5A4F] text-white"
                >
                  Try Again
                </Button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </DialogContent>
    </Dialog>
  );
}
