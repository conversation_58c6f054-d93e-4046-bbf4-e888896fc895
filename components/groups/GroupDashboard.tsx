import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Users, ShoppingBag, Truck, Target, Search, Package, Calendar, TrendingUp, Activity } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import type { StokvelGroup } from "@/types/stokvelgroup"
import { useGetGroupStatsQuery, useGetGroupActivitiesQuery, useGetGroupMembersQuery } from "@/lib/redux/features/groupStats/groupStatsApiSlice"



interface GroupDashboardProps {
  groupDetails: StokvelGroup
}

export default function GroupDashboard({ groupDetails }: GroupDashboardProps) {
  // Fetch dynamic data using RTK Query
  const {
    data: groupStats,
    isLoading: isLoadingStats,
    error: statsError
  } = useGetGroupStatsQuery(groupDetails._id);

  const {
    data: activitiesData,
    isLoading: isLoadingActivities,
    error: activitiesError
  } = useGetGroupActivitiesQuery({
    groupId: groupDetails._id,
    limit: 5,
    timeRange: '7d'
  });

  const {
    data: groupMembers,
    isLoading: isLoadingMembers,
    error: membersError
  } = useGetGroupMembersQuery(groupDetails._id);

  // Use dynamic data or fallback to static data
  const stats = groupStats || {
    totalMembers: groupDetails.members.length,
    onlineMembers: Math.floor(groupDetails.members.length * 0.3),
    totalSavings: groupDetails.totalSales,
    savingsTarget: 100000,
    savingsProgress: ((groupDetails.totalSales / 100000) * 100),
    activeOrders: groupDetails.activeOrders,
    averageOrderValue: groupDetails.avgOrderValue,
    memberParticipationRate: 75,
    orderCompletionRate: 85
  };

  const recentActivity = activitiesData?.data?.slice(0, 3).map(activity => ({
    id: activity._id,
    member: activity.userName,
    action: activity.title,
    date: new Date(activity.createdAt).toLocaleDateString(),
    amount: activity.metadata?.amount || null
  })) || [];

  // Show error state if critical data fails to load
  if (statsError && !isLoadingStats) {
    return (
      <div className="space-y-4 lg:space-y-6">
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center space-x-4">
              <div className="h-12 w-12 rounded-full bg-red-100 flex items-center justify-center">
                <Activity className="h-6 w-6 text-red-500" />
              </div>
              <div>
                <h3 className="font-semibold text-red-900">Unable to load group statistics</h3>
                <p className="text-sm text-red-700">
                  We're having trouble loading the latest group data. Please try refreshing the page.
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-2 border-red-300 text-red-700 hover:bg-red-100"
                  onClick={() => window.location.reload()}
                >
                  Refresh Page
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Fallback basic info */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-none">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Members</p>
                  <h3 className="text-2xl font-bold">{groupDetails.members.length}</h3>
                  <p className="text-xs text-muted-foreground mt-1">Group members</p>
                </div>
                <div className="h-12 w-12 rounded-full bg-blue-500/10 flex items-center justify-center">
                  <Users className="h-6 w-6 text-blue-500" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }
  return (
    <div className="space-y-4 lg:space-y-6">
      {/* Search Section - Mobile Optimized */}
      <div className="flex flex-col sm:flex-row gap-3 sm:gap-2">
        <Input
          placeholder="Search products..."
          className="flex-1 h-10 lg:h-11"
        />
        <Button className="h-10 lg:h-11 px-4 lg:px-6">
          <Search className="h-4 w-4 mr-2" />
          <span className="hidden sm:inline">Search</span>
        </Button>
      </div>

      {/* Stats Overview - Mobile First Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
        <Card className="bg-gradient-to-br from-green-50 to-green-100 border-none">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Saved</p>
                {isLoadingStats ? (
                  <Skeleton className="h-8 w-24 mb-1" />
                ) : (
                  <h3 className="text-2xl font-bold">R{stats.totalSavings.toLocaleString()}</h3>
                )}
                <p className="text-xs text-muted-foreground mt-1">
                  of R{stats.savingsTarget.toLocaleString()} target
                </p>
              </div>
              <div className="h-12 w-12 rounded-full bg-green-500/10 flex items-center justify-center">
                <Target className="h-6 w-6 text-green-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-none">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Members</p>
                {isLoadingStats ? (
                  <Skeleton className="h-8 w-16 mb-1" />
                ) : (
                  <h3 className="text-2xl font-bold">{stats.totalMembers}</h3>
                )}
                <p className="text-xs text-muted-foreground mt-1">
                  {stats.onlineMembers} online now
                </p>
              </div>
              <div className="h-12 w-12 rounded-full bg-blue-500/10 flex items-center justify-center">
                <Users className="h-6 w-6 text-blue-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-none">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Orders</p>
                {isLoadingStats ? (
                  <Skeleton className="h-8 w-12 mb-1" />
                ) : (
                  <h3 className="text-2xl font-bold">{stats.activeOrders}</h3>
                )}
                <p className="text-xs text-muted-foreground mt-1">Orders in Progress</p>
              </div>
              <div className="h-12 w-12 rounded-full bg-purple-500/10 flex items-center justify-center">
                <ShoppingBag className="h-6 w-6 text-purple-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-none">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Order Value</p>
                {isLoadingStats ? (
                  <Skeleton className="h-8 w-20 mb-1" />
                ) : (
                  <h3 className="text-2xl font-bold">R{stats.averageOrderValue.toLocaleString()}</h3>
                )}
                <p className="text-xs text-muted-foreground mt-1">Per Order</p>
              </div>
              <div className="h-12 w-12 rounded-full bg-orange-500/10 flex items-center justify-center">
                <Truck className="h-6 w-6 text-orange-500" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Activity and Progress Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-lg font-medium">Recent Activity</CardTitle>
            <Button variant="ghost" size="sm">
              View all
            </Button>
          </CardHeader>
          <CardContent>
            {isLoadingActivities ? (
              <div className="space-y-3">
                {Array.from({ length: 3 }).map((_, index) => (
                  <div key={index} className="flex items-center space-x-4">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-4 w-16" />
                  </div>
                ))}
              </div>
            ) : activitiesError ? (
              <div className="flex items-center justify-center py-8 text-muted-foreground">
                <Activity className="h-5 w-5 mr-2" />
                <span>Unable to load recent activity</span>
              </div>
            ) : recentActivity.length === 0 ? (
              <div className="flex items-center justify-center py-8 text-muted-foreground">
                <Activity className="h-5 w-5 mr-2" />
                <span>No recent activity</span>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Member</TableHead>
                    <TableHead>Action</TableHead>
                    <TableHead>Amount</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {recentActivity.map((activity) => (
                    <TableRow key={activity.id}>
                      <TableCell className="font-medium">{activity.member}</TableCell>
                      <TableCell>{activity.action}</TableCell>
                      <TableCell>{activity.amount ? `R${activity.amount.toFixed(2)}` : "-"}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-lg font-medium">Group Progress</CardTitle>
            <Button variant="ghost" size="sm">
              View details
            </Button>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Savings Progress</span>
                {isLoadingStats ? (
                  <Skeleton className="h-4 w-12" />
                ) : (
                  <span className="font-medium">{stats.savingsProgress.toFixed(0)}%</span>
                )}
              </div>
              <div className="h-2 rounded-full bg-slate-100">
                <div
                  className="h-full rounded-full bg-green-500 transition-all duration-500"
                  style={{ width: `${Math.min(stats.savingsProgress, 100)}%` }}
                />
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Member Participation</span>
                {isLoadingStats ? (
                  <Skeleton className="h-4 w-12" />
                ) : (
                  <span className="font-medium">{stats.memberParticipationRate.toFixed(0)}%</span>
                )}
              </div>
              <div className="h-2 rounded-full bg-slate-100">
                <div
                  className="h-full rounded-full bg-blue-500 transition-all duration-500"
                  style={{ width: `${Math.min(stats.memberParticipationRate, 100)}%` }}
                />
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Order Completion</span>
                {isLoadingStats ? (
                  <Skeleton className="h-4 w-12" />
                ) : (
                  <span className="font-medium">{stats.orderCompletionRate.toFixed(0)}%</span>
                )}
              </div>
              <div className="h-2 rounded-full bg-slate-100">
                <div
                  className="h-full rounded-full bg-orange-500 transition-all duration-500"
                  style={{ width: `${Math.min(stats.orderCompletionRate, 100)}%` }}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="pt-6">
            <div className="flex flex-col items-center text-center">
              <div className="h-12 w-12 rounded-full bg-green-500/10 flex items-center justify-center mb-4">
                <Package className="h-6 w-6 text-green-500" />
              </div>
              <h4 className="font-semibold mb-1">Browse Products</h4>
              <p className="text-sm text-muted-foreground mb-4">View available products</p>
              <Button variant="outline" className="w-full">
                View Products
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="pt-6">
            <div className="flex flex-col items-center text-center">
              <div className="h-12 w-12 rounded-full bg-blue-500/10 flex items-center justify-center mb-4">
                <Users className="h-6 w-6 text-blue-500" />
              </div>
              <h4 className="font-semibold mb-1">Members</h4>
              <p className="text-sm text-muted-foreground mb-4">View group members</p>
              <Button variant="outline" className="w-full">
                View Members
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="pt-6">
            <div className="flex flex-col items-center text-center">
              <div className="h-12 w-12 rounded-full bg-orange-500/10 flex items-center justify-center mb-4">
                <Calendar className="h-6 w-6 text-orange-500" />
              </div>
              <h4 className="font-semibold mb-1">Delivery Schedule</h4>
              <p className="text-sm text-muted-foreground mb-4">View delivery dates</p>
              <Button variant="outline" className="w-full">
                View Schedule
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="pt-6">
            <div className="flex flex-col items-center text-center">
              <div className="h-12 w-12 rounded-full bg-purple-500/10 flex items-center justify-center mb-4">
                <TrendingUp className="h-6 w-6 text-purple-500" />
              </div>
              <h4 className="font-semibold mb-1">Savings Goals</h4>
              <p className="text-sm text-muted-foreground mb-4">Track group progress</p>
              <Button variant="outline" className="w-full">
                View Goals
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Monthly Trends Section */}
      {stats.monthlyTrends && stats.monthlyTrends.length > 0 && (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-lg font-medium">Monthly Trends</CardTitle>
            <Button variant="ghost" size="sm">
              View details
            </Button>
          </CardHeader>
          <CardContent>
            {isLoadingStats ? (
              <div className="space-y-4">
                <Skeleton className="h-32 w-full" />
                <div className="grid grid-cols-3 gap-4">
                  <Skeleton className="h-16 w-full" />
                  <Skeleton className="h-16 w-full" />
                  <Skeleton className="h-16 w-full" />
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Simple trend visualization */}
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <p className="text-2xl font-bold text-green-600">
                      {stats.monthlyTrends.slice(-1)[0]?.orders || 0}
                    </p>
                    <p className="text-sm text-muted-foreground">Orders This Month</p>
                  </div>
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <p className="text-2xl font-bold text-blue-600">
                      R{(stats.monthlyTrends.slice(-1)[0]?.revenue || 0).toLocaleString()}
                    </p>
                    <p className="text-sm text-muted-foreground">Revenue This Month</p>
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <p className="text-2xl font-bold text-purple-600">
                      {stats.monthlyTrends.slice(-1)[0]?.members || 0}
                    </p>
                    <p className="text-sm text-muted-foreground">Active Members</p>
                  </div>
                </div>

                {/* Last 3 months comparison */}
                <div className="space-y-2">
                  <h4 className="font-medium text-sm">Recent Performance</h4>
                  <div className="space-y-2">
                    {stats.monthlyTrends.slice(-3).map((trend, index) => (
                      <div key={trend.month} className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">
                          {new Date(trend.month + '-01').toLocaleDateString('en-US', {
                            month: 'short',
                            year: 'numeric'
                          })}
                        </span>
                        <div className="flex items-center space-x-4">
                          <span>{trend.orders} orders</span>
                          <span className="font-medium">R{trend.revenue.toLocaleString()}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Top Contributors Section */}
      {stats.topSpenders && stats.topSpenders.length > 0 && (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-lg font-medium">Top Contributors</CardTitle>
            <Button variant="ghost" size="sm">
              View all
            </Button>
          </CardHeader>
          <CardContent>
            {isLoadingStats ? (
              <div className="space-y-3">
                {Array.from({ length: 3 }).map((_, index) => (
                  <div key={index} className="flex items-center space-x-4">
                    <Skeleton className="h-10 w-10 rounded-full" />
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-3 w-16" />
                    </div>
                    <Skeleton className="h-4 w-16 ml-auto" />
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-3">
                {stats.topSpenders.slice(0, 5).map((spender, index) => (
                  <div key={spender.userId} className="flex items-center space-x-4">
                    <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-500 flex items-center justify-center text-white font-semibold">
                      {spender.name.charAt(0).toUpperCase()}
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-sm">{spender.name}</p>
                      <p className="text-xs text-muted-foreground">
                        #{index + 1} contributor
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-sm">R{spender.totalSpent.toLocaleString()}</p>
                      <p className="text-xs text-muted-foreground">total spent</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}

