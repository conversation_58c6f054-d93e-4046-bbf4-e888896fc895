// components/admin/SalesChart.tsx

"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from 'recharts';
import { TrendingUp, TrendingDown, RefreshCw, AlertCircle, BarChart3, Activity, Download } from "lucide-react";
import { useGetSalesChartQuery, SalesChartDataPoint, SalesChartResponse } from "@/lib/redux/features/admin/adminDashboardApiSlice";
import { motion } from "framer-motion";
import { ChartErrorState } from "@/components/ui/error-components";

interface SalesChartProps {
  className?: string;
}

// Tooltip props type
interface TooltipProps {
  active?: boolean;
  payload?: Array<{
    value: number;
    payload: SalesChartDataPoint;
  }>;
  label?: string;
}

// Utility functions for error handling
const getDisplayMessage = (error: any): string => {
  if (!error) return "An error occurred";
  if (typeof error === 'string') return error;
  if (error.message) return error.message;
  if (error.data?.message) return error.data.message;
  return "Failed to load chart data";
};

const shouldShowRetry = (error: any): boolean => {
  // Always show retry for errors
  return !!error;
};

export function SalesChart({ className = "" }: SalesChartProps) {
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'year'>('week');
  const [chartType, setChartType] = useState<'line' | 'area'>('area');

  const {
    data,
    isLoading,
    error,
    refetch,
    isFetching
  } = useGetSalesChartQuery({ period: selectedPeriod });

  // Simple fallback data
  const fallbackData: SalesChartResponse = {
    success: false,
    data: [],
    summary: {
      totalSales: 0,
      totalOrders: 0,
      totalPaidOrders: 0,
      averageOrderValue: 0,
      salesTrend: 0,
      period: selectedPeriod,
      dateRange: {
        start: '',
        end: ''
      }
    },
    lastUpdated: new Date().toISOString()
  };

  // Use real data if available, otherwise use fallback data
  const chartData: SalesChartResponse = data || fallbackData;

  const periodOptions = [
    { value: 'week' as const, label: 'Week' },
    { value: 'month' as const, label: 'Month' },
    { value: 'year' as const, label: 'Year' }
  ];

  const formatCurrency = (value: number) => {
    if (value >= 1000000) {
      return `R${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `R${(value / 1000).toFixed(1)}K`;
    } else {
      return `R${value.toFixed(0)}`;
    }
  };

  const CustomTooltip = ({ active, payload, label }: TooltipProps) => {
    if (active && payload && payload.length > 0) {
      const data = payload[0];
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900">{label}</p>
          <p className="text-blue-600">
            Sales: <span className="font-semibold">{formatCurrency(data.value)}</span>
          </p>
          {data.payload.orders && (
            <p className="text-gray-600 text-sm">
              Orders: {data.payload.orders}
            </p>
          )}
        </div>
      );
    }
    return null;
  };

  const handleExport = () => {
    if (chartData?.data && chartData.data.length > 0) {
      try {
        const csvContent = [
          ['Date', 'Sales', 'Orders', 'Paid Orders'],
          ...chartData.data.map((item: SalesChartDataPoint) => [
            item.date || item.day,
            item.sales.toString(),
            item.orders.toString(),
            item.paidOrders.toString()
          ])
        ].map(row => row.join(',')).join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `sales-data-${selectedPeriod}-${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        window.URL.revokeObjectURL(url);
      } catch (error) {
        console.error('Failed to export data:', error);
      }
    }
  };

  // Only show error for critical failures, otherwise show chart with fallback data
  const showCriticalError = error && !data;

  // Safe accessors for chart data
  const hasValidData = chartData?.data && Array.isArray(chartData.data) && chartData.data.length > 0;
  const hasSummary = chartData?.summary && typeof chartData.summary === 'object';

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-blue-600" />
              Sales Performance
            </CardTitle>
            <CardDescription>
              {hasSummary ? (
                <span>
                  {formatCurrency(chartData.summary.totalSales || 0)} total sales • {chartData.summary.totalOrders || 0} orders
                  {error && (
                    <span className="text-orange-600 ml-2">• Using cached data</span>
                  )}
                </span>
              ) : (
                "Sales performance over time"
              )}
            </CardDescription>
          </div>

          {/* Controls - Mobile Optimized */}
          <div className="flex flex-col sm:flex-row gap-2">
            {/* Period Selector */}
            <div className="flex bg-gray-100 rounded-lg p-1">
              {periodOptions.map((option) => (
                <Button
                  key={option.value}
                  variant={selectedPeriod === option.value ? "default" : "ghost"}
                  size="sm"
                  className={`px-3 py-1 text-xs font-medium transition-all ${
                    selectedPeriod === option.value
                      ? "bg-white shadow-sm"
                      : "hover:bg-gray-200"
                  }`}
                  onClick={() => setSelectedPeriod(option.value)}
                >
                  {option.label}
                </Button>
              ))}
            </div>

            {/* Chart Type & Actions */}
            <div className="flex gap-1">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setChartType(chartType === 'line' ? 'area' : 'line')}
                className="px-3 py-1"
                title={`Switch to ${chartType === 'line' ? 'area' : 'line'} chart`}
              >
                <BarChart3 className="h-3 w-3" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleExport}
                disabled={!chartData?.data || chartData.data.length === 0}
                className="px-3 py-1"
                title="Export data as CSV"
              >
                <Download className="h-3 w-3" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => refetch()}
                disabled={isFetching}
                className="px-3 py-1"
              >
                <RefreshCw className={`h-3 w-3 ${isFetching ? "animate-spin" : ""}`} />
              </Button>
            </div>
          </div>
        </div>

        {/* Summary Stats */}
        {hasSummary && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex flex-wrap gap-4 mt-4 text-sm"
          >
            <div className="flex items-center gap-1">
              <span className="text-gray-600">Trend:</span>
              <div className="flex items-center gap-1">
                {(chartData.summary.salesTrend || 0) >= 0 ? (
                  <TrendingUp className="h-3 w-3 text-green-600" />
                ) : (
                  <TrendingDown className="h-3 w-3 text-red-600" />
                )}
                <span className={`font-medium ${
                  (chartData.summary.salesTrend || 0) >= 0 ? "text-green-600" : "text-red-600"
                }`}>
                  {(chartData.summary.salesTrend || 0) >= 0 ? "+" : ""}{(chartData.summary.salesTrend || 0).toFixed(1)}%
                </span>
              </div>
            </div>
            <div className="text-gray-600">
              Avg Order: <span className="font-medium text-gray-900">
                {formatCurrency(chartData.summary.averageOrderValue || 0)}
              </span>
            </div>
            <div className="text-gray-600">
              Paid Orders: <span className="font-medium text-gray-900">
                {chartData.summary.totalPaidOrders || 0}
              </span>
            </div>
          </motion.div>
        )}
      </CardHeader>

      <CardContent>
        {isLoading ? (
          <div className="space-y-3">
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-64 w-full" />
          </div>
        ) : showCriticalError ? (
          <ChartErrorState
            errorState="server-error"
            title="Chart Unavailable"
            message={getDisplayMessage(error)}
            onRetry={() => refetch()}
            isRetrying={isFetching}
            showRetry={shouldShowRetry(error)}
          />
        ) : hasValidData ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            <div className="w-full h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
              {chartType === 'area' ? (
                <AreaChart data={chartData.data}>
                  <defs>
                    <linearGradient id="salesGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.3}/>
                      <stop offset="95%" stopColor="#3B82F6" stopOpacity={0}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                  <XAxis
                    dataKey="day"
                    stroke="#6B7280"
                    fontSize={12}
                    tickLine={false}
                  />
                  <YAxis
                    stroke="#6B7280"
                    fontSize={12}
                    tickLine={false}
                    tickFormatter={formatCurrency}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Area
                    type="monotone"
                    dataKey="sales"
                    stroke="#3B82F6"
                    strokeWidth={2}
                    fill="url(#salesGradient)"
                    dot={{ fill: '#3B82F6', strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6, stroke: '#3B82F6', strokeWidth: 2 }}
                  />
                </AreaChart>
              ) : (
                <LineChart data={chartData.data}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                  <XAxis
                    dataKey="day"
                    stroke="#6B7280"
                    fontSize={12}
                    tickLine={false}
                  />
                  <YAxis
                    stroke="#6B7280"
                    fontSize={12}
                    tickLine={false}
                    tickFormatter={formatCurrency}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Line
                    type="monotone"
                    dataKey="sales"
                    stroke="#3B82F6"
                    strokeWidth={3}
                    dot={{ fill: '#3B82F6', strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6, stroke: '#3B82F6', strokeWidth: 2 }}
                  />
                </LineChart>
              )}
              </ResponsiveContainer>
            </div>
          </motion.div>
        ) : (
          <ChartErrorState
            errorState="no-data"
            title="No Sales Data"
            message="No sales data available for the selected period"
            onRetry={() => refetch()}
            isRetrying={isFetching}
            showRetry={error ? shouldShowRetry(error) : false}
          />
        )}
      </CardContent>
    </Card>
  );
}

