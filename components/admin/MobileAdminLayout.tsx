// components/admin/MobileAdminLayout.tsx

"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  ChevronDown, 
  ChevronUp, 
  TrendingUp, 
  TrendingDown,
  BarChart3,
  Users,
  ShoppingCart,
  DollarSign,
  Activity,
  RefreshCw
} from "lucide-react";

interface CollapsibleSectionProps {
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  children: React.ReactNode;
  defaultExpanded?: boolean;
  badge?: string | number;
}

function CollapsibleSection({ 
  title, 
  icon: Icon, 
  children, 
  defaultExpanded = false,
  badge 
}: CollapsibleSectionProps) {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  return (
    <Card className="border-gray-200 overflow-hidden">
      <CardHeader 
        className="pb-3 cursor-pointer hover:bg-gray-50 transition-colors"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-blue-50">
              <Icon className="h-4 w-4 text-blue-600" />
            </div>
            <CardTitle className="text-base font-semibold text-gray-900">
              {title}
            </CardTitle>
            {badge && (
              <Badge variant="secondary" className="text-xs">
                {badge}
              </Badge>
            )}
          </div>
          <motion.div
            animate={{ rotate: isExpanded ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronDown className="h-4 w-4 text-gray-500" />
          </motion.div>
        </div>
      </CardHeader>
      
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
          >
            <CardContent className="pt-0">
              {children}
            </CardContent>
          </motion.div>
        )}
      </AnimatePresence>
    </Card>
  );
}

interface QuickStatProps {
  title: string;
  value: string;
  trend?: number;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
}

function QuickStat({ title, value, trend, icon: Icon, color }: QuickStatProps) {
  return (
    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
      <div className="flex items-center gap-3">
        <div className={`p-2 rounded-lg ${color}`}>
          <Icon className="h-4 w-4 text-white" />
        </div>
        <div>
          <p className="text-sm font-medium text-gray-900">{value}</p>
          <p className="text-xs text-gray-600">{title}</p>
        </div>
      </div>
      {trend !== undefined && (
        <div className="flex items-center gap-1">
          {trend >= 0 ? (
            <TrendingUp className="h-3 w-3 text-green-600" />
          ) : (
            <TrendingDown className="h-3 w-3 text-red-600" />
          )}
          <span className={`text-xs font-medium ${
            trend >= 0 ? "text-green-600" : "text-red-600"
          }`}>
            {trend >= 0 ? "+" : ""}{trend.toFixed(1)}%
          </span>
        </div>
      )}
    </div>
  );
}

interface MobileAdminLayoutProps {
  children: React.ReactNode;
  stats?: {
    revenue: { value: string; trend: number };
    orders: { value: string; trend: number };
    customers: { value: string; trend: number };
    groups: { value: string; trend: number };
  };
  onRefresh?: () => void;
  isRefreshing?: boolean;
}

export function MobileAdminLayout({ 
  children, 
  stats,
  onRefresh,
  isRefreshing = false
}: MobileAdminLayoutProps) {
  return (
    <div className="space-y-4">
      {/* Header with Refresh */}
      <div className="flex items-center justify-between">
        <h1 className="text-xl font-bold text-gray-900">Dashboard</h1>
        {onRefresh && (
          <Button
            variant="outline"
            size="sm"
            onClick={onRefresh}
            disabled={isRefreshing}
            className="px-3 py-1"
          >
            <RefreshCw className={`h-3 w-3 ${isRefreshing ? "animate-spin" : ""}`} />
          </Button>
        )}
      </div>

      {/* Quick Stats - Always Visible */}
      {stats && (
        <CollapsibleSection
          title="Overview"
          icon={BarChart3}
          defaultExpanded={true}
          badge="Live"
        >
          <div className="grid grid-cols-1 gap-3">
            <QuickStat
              title="Revenue"
              value={stats.revenue.value}
              trend={stats.revenue.trend}
              icon={DollarSign}
              color="bg-green-500"
            />
            <QuickStat
              title="Orders"
              value={stats.orders.value}
              trend={stats.orders.trend}
              icon={ShoppingCart}
              color="bg-blue-500"
            />
            <QuickStat
              title="Customers"
              value={stats.customers.value}
              trend={stats.customers.trend}
              icon={Users}
              color="bg-purple-500"
            />
            <QuickStat
              title="Groups"
              value={stats.groups.value}
              trend={stats.groups.trend}
              icon={Activity}
              color="bg-orange-500"
            />
          </div>
        </CollapsibleSection>
      )}

      {/* Main Content */}
      <div className="space-y-4">
        {children}
      </div>
    </div>
  );
}

// Hook for mobile detection
export function useIsMobile() {
  const [isMobile, setIsMobile] = useState(false);

  useState(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  });

  return isMobile;
}

// Responsive wrapper component
interface ResponsiveAdminWrapperProps {
  children: React.ReactNode;
  mobileContent?: React.ReactNode;
  stats?: any;
  onRefresh?: () => void;
  isRefreshing?: boolean;
}

export function ResponsiveAdminWrapper({ 
  children, 
  mobileContent, 
  stats,
  onRefresh,
  isRefreshing 
}: ResponsiveAdminWrapperProps) {
  const isMobile = useIsMobile();

  if (isMobile && mobileContent) {
    return (
      <MobileAdminLayout 
        stats={stats} 
        onRefresh={onRefresh} 
        isRefreshing={isRefreshing}
      >
        {mobileContent}
      </MobileAdminLayout>
    );
  }

  return <>{children}</>;
}
