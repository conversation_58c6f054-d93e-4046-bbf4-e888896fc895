"use client";

import React, { useState, useMemo } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ArrowUpDown, Search, AlertCircle, Loader2, Mail, Phone, Calendar, ShoppingBag, DollarSign, Eye, MessageCircle, Filter, X } from 'lucide-react';
import { useGroupMembership } from '@/lib/redux/hooks/useGroupMembership';
import MemberDetailsModal from '@/components/groups/MemberDetailsModal';

// Define GroupMember interface locally for now
interface GroupMember {
  userId: string;
  name: string;
  email: string;
  phone: string;
  totalOrders: string;
  totalSpent: string;
  joinedAt: string;
  isOnline?: boolean;
  lastSeen?: string;
}

interface GroupMembersTableProps {
  groupId: string;
}

export default function GroupMembersTable({ groupId }: GroupMembersTableProps) {
  // Get group data from existing hook
  const { userGroups, isLoading } = useGroupMembership();
  const currentGroup = userGroups.find(group => group._id === groupId);

  // Transform group members to match our interface
  const members: GroupMember[] = currentGroup?.members?.map((member: any) => ({
    userId: member._id || member.userId || 'unknown',
    name: member.name || 'Unknown Member',
    email: member.email || 'No email provided',
    phone: member.phone || 'No phone provided',
    totalOrders: '0', // TODO: Calculate from actual orders
    totalSpent: 'R0.00', // TODO: Calculate from actual orders
    joinedAt: member.createdAt || new Date().toISOString(),
    isOnline: Math.random() > 0.7, // Mock online status
    lastSeen: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
  })) || [];

  const error = null; // No error for now
  const refetch = () => {}; // No refetch needed

  // State variables
  const [sortColumn, setSortColumn] = useState("");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [spendingFilter, setSpendingFilter] = useState<string>("all");
  const [activityFilter, setActivityFilter] = useState<string>("all");
  const [selectedMember, setSelectedMember] = useState<GroupMember | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const sortMembers = (column: string) => {
    if (column === sortColumn) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortColumn(column);
      setSortDirection("asc");
    }
  };

  // Memoized filtered and sorted members for better performance
  const sortedMembers = useMemo(() => {
    // Filter members based on search term and filters
    let filteredMembers = members.filter(member => {
      // Search filter
      const matchesSearch = member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        member.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        member.phone.toLowerCase().includes(searchTerm.toLowerCase());

      // Status filter
      const matchesStatus = statusFilter === "all" ||
        (statusFilter === "online" && member.isOnline) ||
        (statusFilter === "offline" && !member.isOnline);

      // Spending filter
      const totalSpent = parseFloat(member.totalSpent?.replace(/[^0-9.-]+/g, "") || "0");
      const matchesSpending = spendingFilter === "all" ||
        (spendingFilter === "high" && totalSpent > 5000) ||
        (spendingFilter === "medium" && totalSpent > 1000 && totalSpent <= 5000) ||
        (spendingFilter === "low" && totalSpent <= 1000);

      // Activity filter
      const totalOrders = parseInt(member.totalOrders || "0");
      const matchesActivity = activityFilter === "all" ||
        (activityFilter === "active" && totalOrders > 5) ||
        (activityFilter === "moderate" && totalOrders > 0 && totalOrders <= 5) ||
        (activityFilter === "inactive" && totalOrders === 0);

      return matchesSearch && matchesStatus && matchesSpending && matchesActivity;
    });

    // Sort members based on selected column
    return [...filteredMembers].sort((a, b) => {
      if (!sortColumn) return 0;

      const aVal = a[sortColumn as keyof typeof a] || '';
      const bVal = b[sortColumn as keyof typeof b] || '';

      if (sortColumn === "totalSpent") {
        // Remove currency symbol for numeric comparison
        const aNum = parseFloat(aVal.toString().replace(/[^0-9.-]+/g, "") || "0");
        const bNum = parseFloat(bVal.toString().replace(/[^0-9.-]+/g, "") || "0");
        return sortDirection === "asc" ? aNum - bNum : bNum - aNum;
      }

      if (sortColumn === "totalOrders") {
        const aNum = parseInt(aVal.toString() || "0");
        const bNum = parseInt(bVal.toString() || "0");
        return sortDirection === "asc" ? aNum - bNum : bNum - aNum;
      }

      // String comparison for other columns
      const compare = String(aVal).localeCompare(String(bVal));
      return sortDirection === "asc" ? compare : -compare;
    });
  }, [members, searchTerm, sortColumn, sortDirection, statusFilter, spendingFilter, activityFilter]);

  // Clear all filters
  const clearFilters = () => {
    setSearchTerm("");
    setStatusFilter("all");
    setSpendingFilter("all");
    setActivityFilter("all");
  };

  // Check if any filters are active
  const hasActiveFilters = searchTerm || statusFilter !== "all" || spendingFilter !== "all" || activityFilter !== "all";

  // Handle member selection
  const handleViewMember = (member: GroupMember) => {
    setSelectedMember(member);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedMember(null);
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
        <div>
          <h2 className="text-2xl font-semibold tracking-tight">Group Members</h2>
          <p className="text-sm text-muted-foreground">
            {sortedMembers.length} of {members.length} members
            {hasActiveFilters && " (filtered)"}
          </p>
        </div>

        {/* Search and Actions */}
        <div className="flex items-center space-x-2">
          <Input
            placeholder="Search members..."
            className="max-w-sm"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <Button variant="outline" size="icon">
            <Search className="h-4 w-4" />
          </Button>
          {error && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => refetch()}
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                'Retry'
              )}
            </Button>
          )}
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-wrap items-center gap-4 p-4 bg-muted/50 rounded-lg">
        <div className="flex items-center space-x-2">
          <Filter className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium">Filters:</span>
        </div>

        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-32">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="online">Online</SelectItem>
            <SelectItem value="offline">Offline</SelectItem>
          </SelectContent>
        </Select>

        <Select value={spendingFilter} onValueChange={setSpendingFilter}>
          <SelectTrigger className="w-36">
            <SelectValue placeholder="Spending" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Spending</SelectItem>
            <SelectItem value="high">High (&gt;R5,000)</SelectItem>
            <SelectItem value="medium">Medium (R1,000-R5,000)</SelectItem>
            <SelectItem value="low">Low (&lt;R1,000)</SelectItem>
          </SelectContent>
        </Select>

        <Select value={activityFilter} onValueChange={setActivityFilter}>
          <SelectTrigger className="w-32">
            <SelectValue placeholder="Activity" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Activity</SelectItem>
            <SelectItem value="active">Active (&gt;5 orders)</SelectItem>
            <SelectItem value="moderate">Moderate (1-5 orders)</SelectItem>
            <SelectItem value="inactive">Inactive (0 orders)</SelectItem>
          </SelectContent>
        </Select>

        {hasActiveFilters && (
          <Button variant="ghost" size="sm" onClick={clearFilters}>
            <X className="h-4 w-4 mr-1" />
            Clear Filters
          </Button>
        )}
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to load group members. Please try again.
          </AlertDescription>
        </Alert>
      )}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>
                <Button variant="ghost" onClick={() => sortMembers("name")}>
                  Name
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead>
                <Button variant="ghost" onClick={() => sortMembers("email")}>
                  Email
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead>
                <Button variant="ghost" onClick={() => sortMembers("phone")}>
                  Phone
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead>
                <Button variant="ghost" onClick={() => sortMembers("totalOrders")}>
                  Orders
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead>
                <Button variant="ghost" onClick={() => sortMembers("totalSpent")}>
                  Total Spent
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead>
                <Button variant="ghost" onClick={() => sortMembers("joinedAt")}>
                  Joined
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-6">
                  <div className="flex items-center justify-center space-x-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span>Loading members...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : error ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-6">
                  <div className="flex flex-col items-center space-y-2">
                    <AlertCircle className="h-8 w-8 text-muted-foreground" />
                    <span className="text-muted-foreground">Failed to load members</span>
                    <Button variant="outline" size="sm" onClick={() => refetch()}>
                      Try Again
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ) : sortedMembers.length > 0 ? (
              sortedMembers.map((member) => {
                const isOnline = member.isOnline;
                const totalSpentNum = parseFloat(member.totalSpent?.replace(/[^0-9.-]+/g, "") || "0");
                const totalOrdersNum = parseInt(member.totalOrders || "0");

                return (
                  <TableRow key={member.userId} className="hover:bg-muted/50">
                    {/* Name with Avatar */}
                    <TableCell className="font-medium">
                      <div className="flex items-center space-x-3">
                        <div className="relative">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${member.name}`} />
                            <AvatarFallback className="text-xs">
                              {member.name?.split(' ').map(n => n[0]).join('').toUpperCase() || 'NA'}
                            </AvatarFallback>
                          </Avatar>
                          {isOnline && (
                            <div className="absolute -bottom-0.5 -right-0.5 h-3 w-3 rounded-full bg-green-500 border-2 border-white" />
                          )}
                        </div>
                        <div>
                          <p className="font-medium">{member.name || 'N/A'}</p>
                          {isOnline && (
                            <Badge variant="secondary" className="text-xs bg-green-100 text-green-700">
                              Online
                            </Badge>
                          )}
                        </div>
                      </div>
                    </TableCell>

                    {/* Email */}
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{member.email || 'N/A'}</span>
                      </div>
                    </TableCell>

                    {/* Phone */}
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{member.phone || 'N/A'}</span>
                      </div>
                    </TableCell>

                    {/* Orders with Badge */}
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <ShoppingBag className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">{member.totalOrders || '0'}</span>
                        {totalOrdersNum > 10 && (
                          <Badge variant="secondary" className="text-xs">
                            Active
                          </Badge>
                        )}
                      </div>
                    </TableCell>

                    {/* Total Spent with Color Coding */}
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                        <span className={`font-medium ${
                          totalSpentNum > 5000 ? 'text-green-600' :
                          totalSpentNum > 1000 ? 'text-blue-600' :
                          'text-muted-foreground'
                        }`}>
                          {member.totalSpent || 'R0.00'}
                        </span>
                        {totalSpentNum > 5000 && (
                          <Badge className="text-xs bg-yellow-100 text-yellow-800">
                            Top Spender
                          </Badge>
                        )}
                      </div>
                    </TableCell>

                    {/* Joined Date */}
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">
                          {member.joinedAt ? new Date(member.joinedAt).toLocaleDateString() : 'N/A'}
                        </span>
                      </div>
                    </TableCell>

                    {/* Actions */}
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleViewMember(member)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <MessageCircle className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewMember(member)}
                        >
                          View Profile
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })
            ) : (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-6">
                  <div className="flex flex-col items-center space-y-2">
                    <span className="text-muted-foreground">
                      {searchTerm ? 'No members match your search' : 'No members found'}
                    </span>
                    {searchTerm && (
                      <Button variant="outline" size="sm" onClick={() => setSearchTerm('')}>
                        Clear Search
                      </Button>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Member Details Modal */}
      <MemberDetailsModal
        member={selectedMember}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        isAdmin={true} // You can make this dynamic based on user role
      />
    </div>
  );
}