import React, { useState } from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { MoreHorizontal, ArrowUpDown } from 'lucide-react';
import { useGetGroupOrdersQuery } from '@/lib/redux/features/cart/cartApiSlice';
import { Skeleton } from '@/components/ui/skeleton';

interface GroupOrdersReduxTableProps {
  groupId: string;
}

interface UserContribution {
  userId: string;
  userName: string;
  totalSpent: number;
}

interface GroupOrder {
  _id: string;
  groupId: string;
  totalOrderValue: number;
  status: string;
  paymentStatus: string;
  orderPlacedAt: string | Date;
  userContributions: UserContribution[];
}

const GroupOrdersReduxTable: React.FC<GroupOrdersReduxTableProps> = ({ groupId }) => {
  const [currentPage, setCurrentPage] = useState(0);
  const [pageSize, setPageSize] = useState(20);
  const [sortColumn, setSortColumn] = useState<string>("");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const [searchTerm, setSearchTerm] = useState<string>("");

  const { data: orders, isLoading, error } = useGetGroupOrdersQuery({
    groupId,
    limit: pageSize,
    skip: currentPage * pageSize,
    populate: true // Need product details for the table
  });

  const handleSort = (column: string) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortColumn(column);
      setSortDirection("asc");
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <Skeleton className="h-8 w-40" />
          <Skeleton className="h-10 w-64" />
        </div>
        <div className="rounded-md border">
          <div className="h-[400px] relative">
            <Skeleton className="absolute inset-0" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return <div className="text-red-500">Error loading orders</div>;
  }

  if (!orders || orders.length === 0) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium">No orders found</h3>
        <p className="text-muted-foreground">Start shopping to create orders</p>
      </div>
    );
  }

  const filteredOrders = orders.filter((order: GroupOrder) =>
    order._id.toLowerCase().includes(searchTerm.toLowerCase()) ||
    order.userContributions.some((contribution: UserContribution) =>
      contribution.userName.toLowerCase().includes(searchTerm.toLowerCase())
    )
  );

  const sortedOrders = [...filteredOrders].sort((a, b) => {
    let aValue: string | number;
    let bValue: string | number;

    switch (sortColumn) {
      case 'id':
        aValue = a._id;
        bValue = b._id;
        break;
      case 'orderPlacedAt':
        aValue = new Date(a.orderPlacedAt).getTime();
        bValue = new Date(b.orderPlacedAt).getTime();
        break;
      case 'totalOrderValue':
        aValue = a.totalOrderValue;
        bValue = b.totalOrderValue;
        break;
      case 'status':
        aValue = a.status;
        bValue = b.status;
        break;
      case 'paymentStatus':
        aValue = a.paymentStatus;
        bValue = b.paymentStatus;
        break;
      default:
        aValue = '';
        bValue = '';
    }

    return sortDirection === 'asc' ? (aValue > bValue ? 1 : -1) : (aValue < bValue ? 1 : -1);
  });

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold tracking-tight">Group Orders</h2>
        <div className="flex items-center space-x-2">
          <Input
            placeholder="Search orders..."
            className="max-w-sm"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <Button variant="outline" size="icon">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </div>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[100px]">Order ID</TableHead>
              <TableHead>Customer</TableHead>
              <TableHead>
                <Button variant="ghost" onClick={() => handleSort('orderPlacedAt')}>Date<ArrowUpDown className="ml-2 h-4 w-4" /></Button>
              </TableHead>
              <TableHead>
                <Button variant="ghost" onClick={() => handleSort('totalOrderValue')}>Total<ArrowUpDown className="ml-2 h-4 w-4" /></Button>
              </TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Payment</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              // Skeleton loading rows
              Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={index}>
                  <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                  <TableCell className="text-right"><Skeleton className="h-4 w-8 ml-auto" /></TableCell>
                </TableRow>
              ))
            ) : error ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                  Error loading orders. Please try again.
                </TableCell>
              </TableRow>
            ) : sortedOrders.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                  No orders found.
                </TableCell>
              </TableRow>
            ) : sortedOrders.map((order) => (
              <TableRow key={order._id}>
                <TableCell className="font-medium">{order._id.substring(0, 8)}...</TableCell>
                <TableCell>{order.userContributions.map(contribution => contribution.userName).join(', ')}</TableCell>
                <TableCell>{new Date(order.orderPlacedAt).toLocaleDateString()}</TableCell>
                <TableCell>R {order.totalOrderValue.toFixed(2)}</TableCell>
                <TableCell>{order.status}</TableCell>
                <TableCell>{order.paymentStatus}</TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem>View details</DropdownMenuItem>
                      <DropdownMenuItem>Update status</DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem>Delete order</DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination Controls */}
      <div className="flex items-center justify-between px-2 py-4">
        <div className="flex items-center space-x-2">
          <p className="text-sm text-muted-foreground">
            Showing {currentPage * pageSize + 1} to {Math.min((currentPage + 1) * pageSize, (orders?.length || 0) + currentPage * pageSize)} of results
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
            disabled={currentPage === 0 || isLoading}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(currentPage + 1)}
            disabled={!orders || orders.length < pageSize || isLoading}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
};

export default GroupOrdersReduxTable;
