// components/admin/tables/ProductCategoriesTable.tsx
"use client"

import React, { useState, useMemo, useCallback } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Skeleton } from '@/components/ui/skeleton'
import { toast } from 'sonner'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import {
  Edit2Icon,
  TrashIcon,
  EyeIcon,
  LoaderIcon,
  Search,
  Filter,
  ArrowUpDown,
  MoreHorizontal,
  FolderOpen,
  AlertTriangle,
  CheckCircle2,
  XCircle,
  RefreshCw,
  Download,
  Archive,
  Plus,
  TrendingUp,
  TrendingDown,
  Trash2
} from 'lucide-react'
import { useGetCategoriesQuery, useUpdateCategoryMutation, useDeleteCategoryMutation } from '@/lib/redux/features/categories/categoriesApiSlice'
import { motion, AnimatePresence } from "framer-motion"

// Extended interface for ProductCategory
export interface ExtendedProductCategory {
  _id: string
  name: string
  description?: string
  is_active: boolean
  product_count?: number
  parent_category?: string
  createdAt: Date
  updatedAt: Date
}

// Types for sorting and filtering
type SortField = 'name' | 'product_count' | 'is_active' | 'updatedAt';
type SortDirection = 'asc' | 'desc';

interface TableFilters {
  search: string;
  status: 'all' | 'active' | 'inactive';
  productCount: 'all' | 'empty' | 'low' | 'medium' | 'high';
}

export function ProductCategoriesTable() {
  const { data: categoriesResponse, isLoading, isError, refetch } = useGetCategoriesQuery()
  const [updateCategoryMutation] = useUpdateCategoryMutation()
  const [deleteCategoryMutation] = useDeleteCategoryMutation()

  // Extract categories with fallback - API returns array directly, not wrapped in object
  const categories = categoriesResponse || [];

  // State management
  const [editingCategory, setEditingCategory] = useState<{
    id: string,
    name: string,
    description?: string
  } | null>(null)

  const [deletingCategory, setDeletingCategory] = useState<{
    id: string,
    name: string
  } | null>(null)

  const [selectedCategories, setSelectedCategories] = useState<Set<string>>(new Set())
  const [sortField, setSortField] = useState<SortField>('updatedAt')
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc')
  const [filters, setFilters] = useState<TableFilters>({
    search: '',
    status: 'all',
    productCount: 'all'
  })
  const [showFilters, setShowFilters] = useState(false)

  const [selectedCategory, setSelectedCategory] = useState<ExtendedProductCategory | null>(null)

  // New state for loading actions
  const [isUpdating, setIsUpdating] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)

  // Helper functions
  const formatDate = useCallback((date?: Date | string) => {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }, [])

  const getProductCountStatus = useCallback((count: number) => {
    if (count === 0) return { status: 'empty', label: 'Empty', color: 'secondary', className: 'bg-gray-100 text-gray-800' };
    if (count <= 5) return { status: 'low', label: 'Low', color: 'outline', className: 'bg-yellow-100 text-yellow-800 border-yellow-300' };
    if (count <= 20) return { status: 'medium', label: 'Medium', color: 'default', className: 'bg-blue-100 text-blue-800' };
    return { status: 'high', label: 'High', color: 'default', className: 'bg-green-100 text-green-800' };
  }, [])

  // Filtering and sorting logic
  const filteredAndSortedCategories = useMemo(() => {
    let filtered = categories.filter(category => {
      // Search filter
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        const matchesSearch =
          category.name.toLowerCase().includes(searchLower) ||
          category.description?.toLowerCase().includes(searchLower);
        if (!matchesSearch) return false;
      }

      // Status filter
      if (filters.status !== 'all') {
        if (filters.status === 'active' && !category.is_active) return false;
        if (filters.status === 'inactive' && category.is_active) return false;
      }

      // Product count filter
      if (filters.productCount !== 'all') {
        const count = category.product_count || 0;
        const status = getProductCountStatus(count).status;
        if (status !== filters.productCount) return false;
      }

      return true;
    });

    // Sorting
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortField) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'product_count':
          aValue = a.product_count || 0;
          bValue = b.product_count || 0;
          break;
        case 'is_active':
          aValue = a.is_active ? 1 : 0;
          bValue = b.is_active ? 1 : 0;
          break;
        case 'updatedAt':
          aValue = a.updatedAt ? new Date(a.updatedAt).getTime() : 0;
          bValue = b.updatedAt ? new Date(b.updatedAt).getTime() : 0;
          break;
        default:
          return 0;
      }

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [categories, filters, sortField, sortDirection, getProductCountStatus])

  // Event handlers
  const handleSort = useCallback((field: SortField) => {
    if (sortField === field) {
      setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  }, [sortField]);

  const handleSelectAll = useCallback((checked: boolean) => {
    if (checked) {
      setSelectedCategories(new Set(filteredAndSortedCategories.map(c => c._id)));
    } else {
      setSelectedCategories(new Set());
    }
  }, [filteredAndSortedCategories]);

  const handleSelectCategory = useCallback((categoryId: string, checked: boolean) => {
    setSelectedCategories(prev => {
      const newSet = new Set(prev);
      if (checked) {
        newSet.add(categoryId);
      } else {
        newSet.delete(categoryId);
      }
      return newSet;
    });
  }, []);

  const clearFilters = useCallback(() => {
    setFilters({
      search: '',
      status: 'all',
      productCount: 'all'
    });
  }, []);

  const handleEditSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!editingCategory) return

    try {
      setIsUpdating(true)
      await updateCategoryMutation({
        id: editingCategory.id,
        updateData: {
          name: editingCategory.name,
          description: editingCategory.description
        }
      })

      toast.success('Category Updated', {
        description: `Category "${editingCategory.name}" has been successfully updated.`
      })

      setEditingCategory(null)
    } catch (error) {
      toast.error('Failed to Update Category', {
        description: error instanceof Error ? error.message : 'An unknown error occurred'
      })
    } finally {
      setIsUpdating(false)
    }
  }

  const handleDeleteConfirmation = async () => {
    if (!deletingCategory) return

    try {
      setIsDeleting(true)
      // Use deleteCategoryMutation
      await deleteCategoryMutation(deletingCategory.id)

      toast.success('Category Archived', {
        description: `Category "${deletingCategory.name}" has been successfully archived.`
      })

      setDeletingCategory(null)
    } catch (error) {
      toast.error('Failed to Archive Category', {
        description: error instanceof Error ? error.message : 'An unknown error occurred'
      })
    } finally {
      setIsDeleting(false)
    }
  }

  // Loading state
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FolderOpen className="h-5 w-5" />
            Categories
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <Skeleton className="h-12 w-12 rounded" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-[250px]" />
                  <Skeleton className="h-4 w-[200px]" />
                </div>
                <Skeleton className="h-8 w-[100px]" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  // Error state
  if (isError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FolderOpen className="h-5 w-5" />
            Categories
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert className="border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              Failed to load categories. Please try again.
              <Button
                variant="outline"
                size="sm"
                onClick={() => refetch()}
                className="ml-2"
              >
                <RefreshCw className="h-3 w-3 mr-1" />
                Retry
              </Button>
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <FolderOpen className="h-5 w-5 text-[#2A7C6C]" />
            Categories ({filteredAndSortedCategories.length})
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className="h-8"
            >
              <Filter className="h-3 w-3 mr-1" />
              Filters
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => refetch()}
              className="h-8"
            >
              <RefreshCw className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Filters Section */}
        <AnimatePresence>
          {showFilters && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="border rounded-lg p-4 bg-gray-50"
            >
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Search */}
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-1 block">Search</label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search categories..."
                      value={filters.search}
                      onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                      className="pl-10"
                    />
                  </div>
                </div>

                {/* Status Filter */}
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-1 block">Status</label>
                  <Select
                    value={filters.status}
                    onValueChange={(value: any) => setFilters(prev => ({ ...prev, status: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Product Count Filter */}
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-1 block">Product Count</label>
                  <Select
                    value={filters.productCount}
                    onValueChange={(value: any) => setFilters(prev => ({ ...prev, productCount: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All Counts" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Counts</SelectItem>
                      <SelectItem value="empty">Empty (0)</SelectItem>
                      <SelectItem value="low">Low (1-5)</SelectItem>
                      <SelectItem value="medium">Medium (6-20)</SelectItem>
                      <SelectItem value="high">High (20+)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex justify-between items-center mt-4 pt-4 border-t">
                <div className="text-sm text-gray-600">
                  Showing {filteredAndSortedCategories.length} of {categories.length} categories
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearFilters}
                  className="h-8"
                >
                  Clear Filters
                </Button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Bulk Actions */}
        {selectedCategories.size > 0 && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg"
          >
            <span className="text-sm font-medium text-blue-900">
              {selectedCategories.size} categor{selectedCategories.size !== 1 ? 'ies' : 'y'} selected
            </span>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => toast.info('Export feature coming soon')}
                className="h-8"
              >
                <Download className="h-3 w-3 mr-1" />
                Export
              </Button>
              <Button
                variant="destructive"
                size="sm"
                onClick={() => toast.info('Bulk delete feature coming soon')}
                className="h-8"
              >
                <Trash2 className="h-3 w-3 mr-1" />
                Delete
              </Button>
            </div>
          </motion.div>
        )}

      {/* Edit Category Dialog */}
      <Dialog
        open={!!editingCategory}
        onOpenChange={() => setEditingCategory(null)}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Category</DialogTitle>
            <DialogDescription>
              Update the details of the selected category.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleEditSubmit} className="space-y-4">
            <div>
              <Label htmlFor="categoryName">Category Name</Label>
              <Input
                id="categoryName"
                value={editingCategory?.name || ''}
                onChange={(e) => setEditingCategory(prev =>
                  prev ? { ...prev, name: e.target.value } : null
                )}
                required
              />
            </div>
            <div>
              <Label htmlFor="categoryDescription">Description (Optional)</Label>
              <Input
                id="categoryDescription"
                value={editingCategory?.description || ''}
                onChange={(e) => setEditingCategory(prev =>
                  prev ? { ...prev, description: e.target.value } : null
                )}
              />
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setEditingCategory(null)}
                disabled={isUpdating}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isUpdating}
              >
                {isUpdating ? (
                  <>
                    <LoaderIcon className="h-4 w-4 mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  'Save Changes'
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Category Confirmation Dialog */}
      <Dialog
        open={!!deletingCategory}
        onOpenChange={() => setDeletingCategory(null)}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Category</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the category &quot;{deletingCategory?.name}&quot;?
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeletingCategory(null)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteConfirmation}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <LoaderIcon className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Confirm Delete
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View Category Dialog */}
      <Dialog
        open={!!selectedCategory}
        onOpenChange={() => setSelectedCategory(null)}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{selectedCategory?.name}</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <p><strong>Description:</strong> {selectedCategory?.description}</p>
            <p><strong>Product Count:</strong> {selectedCategory?.product_count ?? 0}</p>
            <p><strong>Status:</strong> {selectedCategory?.is_active ? 'Active' : 'Inactive'}</p>
            <p><strong>Parent Category:</strong> {selectedCategory?.parent_category || 'None'}</p>
            <p><strong>Created At:</strong> {selectedCategory && formatDate(selectedCategory.createdAt)}</p>
            <p><strong>Updated At:</strong> {selectedCategory && formatDate(selectedCategory.updatedAt)}</p>
          </div>
        </DialogContent>
      </Dialog>

        {/* Table */}
        <div className="border rounded-lg overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="w-12">
                  <Checkbox
                    checked={selectedCategories.size === filteredAndSortedCategories.length && filteredAndSortedCategories.length > 0}
                    onCheckedChange={handleSelectAll}
                  />
                </TableHead>
                <TableHead>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleSort('name')}
                    className="h-8 p-0 font-medium hover:bg-transparent"
                  >
                    Name
                    <ArrowUpDown className="ml-2 h-3 w-3" />
                  </Button>
                </TableHead>
                <TableHead>Description</TableHead>
                <TableHead>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleSort('product_count')}
                    className="h-8 p-0 font-medium hover:bg-transparent"
                  >
                    Products
                    <ArrowUpDown className="ml-2 h-3 w-3" />
                  </Button>
                </TableHead>
                <TableHead>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleSort('is_active')}
                    className="h-8 p-0 font-medium hover:bg-transparent"
                  >
                    Status
                    <ArrowUpDown className="ml-2 h-3 w-3" />
                  </Button>
                </TableHead>
                <TableHead>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleSort('updatedAt')}
                    className="h-8 p-0 font-medium hover:bg-transparent"
                  >
                    Updated
                    <ArrowUpDown className="ml-2 h-3 w-3" />
                  </Button>
                </TableHead>
                <TableHead className="w-32">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredAndSortedCategories.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-12">
                    <div className="flex flex-col items-center gap-2">
                      <FolderOpen className="h-12 w-12 text-gray-400" />
                      <h3 className="text-lg font-medium text-gray-900">No categories found</h3>
                      <p className="text-sm text-gray-600">
                        {filters.search || filters.status !== 'all' || filters.productCount !== 'all'
                          ? 'Try adjusting your filters to see more results.'
                          : 'Get started by adding your first category.'
                        }
                      </p>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                filteredAndSortedCategories.map((category) => {
                  const productCountStatus = getProductCountStatus(category.product_count || 0);
                  const isSelected = selectedCategories.has(category._id);

                  return (
                    <TableRow
                      key={category._id}
                      className={`hover:bg-gray-50 transition-colors ${isSelected ? 'bg-blue-50' : ''}`}
                    >
                      <TableCell>
                        <Checkbox
                          checked={isSelected}
                          onCheckedChange={(checked) => handleSelectCategory(category._id, checked as boolean)}
                        />
                      </TableCell>

                      <TableCell>
                        <div className="font-medium text-gray-900">
                          {category.name}
                        </div>
                      </TableCell>

                      <TableCell>
                        <div className="text-sm text-gray-600 max-w-xs truncate">
                          {category.description || 'No description'}
                        </div>
                      </TableCell>

                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Badge
                            variant={productCountStatus.color as "default" | "secondary" | "destructive" | "outline"}
                            className={`font-normal ${productCountStatus.className}`}
                          >
                            {category.product_count || 0}
                          </Badge>
                          <span className="text-xs text-gray-500">
                            {productCountStatus.label}
                          </span>
                        </div>
                      </TableCell>

                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Badge
                            variant={category.is_active ? 'default' : 'secondary'}
                            className={`font-normal ${category.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}
                          >
                            {category.is_active ? (
                              <>
                                <CheckCircle2 className="h-3 w-3 mr-1" />
                                Active
                              </>
                            ) : (
                              <>
                                <XCircle className="h-3 w-3 mr-1" />
                                Inactive
                              </>
                            )}
                          </Badge>
                        </div>
                      </TableCell>

                      <TableCell>
                        <div className="text-sm text-gray-600">
                          {formatDate(category.updatedAt)}
                        </div>
                      </TableCell>

                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setSelectedCategory(category as unknown as ExtendedProductCategory)}
                            className="h-8 w-8 p-0"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setEditingCategory({
                              id: category._id,
                              name: category.name,
                              description: category.description
                            })}
                            className="h-8 w-8 p-0"
                          >
                            <Edit2Icon className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setDeletingCategory({
                              id: category._id,
                              name: category.name
                            })}
                            className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
