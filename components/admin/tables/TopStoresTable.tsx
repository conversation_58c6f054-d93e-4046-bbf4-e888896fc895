// components/admin/tables/TopStoresTable.tsx

"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Store,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  AlertCircle,
  MapPin,
  Users,
  ShoppingCart,
  Filter,
  MoreHorizontal,
  Download
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useGetTopGroupsQuery } from "@/lib/redux/features/admin/adminDashboardApiSlice";
import { motion } from "framer-motion";
import { TableErrorState } from "@/components/ui/error-components";

interface TopStoresTableProps {
  className?: string;
}

// Utility functions for error handling
const getDisplayMessage = (error: any): string => {
  if (!error) return "An error occurred";
  if (typeof error === 'string') return error;
  if (error.message) return error.message;
  if (error.data?.message) return error.data.message;
  return "Failed to load groups data";
};

const shouldShowRetry = (error: any): boolean => {
  // Always show retry for errors
  return !!error;
};

export function TopStoresTable({ className = "" }: TopStoresTableProps) {
  const [selectedPeriod, setSelectedPeriod] = useState("30");
  const [sortBy, setSortBy] = useState<'revenue' | 'orders' | 'members'>('revenue');
  const [limit, setLimit] = useState(10);

  const {
    data,
    isLoading,
    error,
    refetch,
    isFetching
  } = useGetTopGroupsQuery({
    period: selectedPeriod,
    sortBy,
    limit
  });

  // Simple fallback data
  const fallbackData = {
    groups: [],
    summary: {
      totalGroups: 0,
      totalRevenue: 0,
      totalOrders: 0,
      totalMembers: 0,
      averageGroupSize: 0
    }
  };

  // Use real data if available, otherwise use fallback data
  const groupsData = data?.data || data || fallbackData;

  const periodOptions = [
    { value: "7", label: "7D" },
    { value: "30", label: "30D" },
    { value: "90", label: "90D" }
  ];

  const sortOptions = [
    { value: 'revenue' as const, label: 'Revenue' },
    { value: 'orders' as const, label: 'Orders' },
    { value: 'members' as const, label: 'Members' }
  ];

  const getStatusBadge = (group: any) => {
    const memberCount = group.totalMembers;
    if (memberCount >= 20) return { variant: "default" as const, label: "Active" };
    if (memberCount >= 10) return { variant: "secondary" as const, label: "Growing" };
    return { variant: "outline" as const, label: "New" };
  };

  const formatCurrency = (amount: number) => {
    if (amount >= 1000000) {
      return `R${(amount / 1000000).toFixed(1)}M`;
    } else if (amount >= 1000) {
      return `R${(amount / 1000).toFixed(1)}K`;
    } else {
      return `R${amount.toFixed(2)}`;
    }
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(word => word[0]).join('').toUpperCase().slice(0, 2);
  };

  const handleExport = () => {
    if (data?.groups) {
      const csvContent = [
        ['Group Name', 'Location', 'Members', 'Revenue', 'Orders', 'Revenue per Member'],
        ...data.groups.map(group => [
          group.name,
          group.location,
          group.totalMembers.toString(),
          group.totalRevenue.toString(),
          group.totalOrders.toString(),
          group.revenuePerMember.toString()
        ])
      ].map(row => row.join(',')).join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `top-groups-${selectedPeriod}d-${new Date().toISOString().split('T')[0]}.csv`;
      a.click();
      window.URL.revokeObjectURL(url);
    }
  };

  // Only show critical errors, otherwise show table with fallback data
  const showCriticalError = error && !data;

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Store className="h-5 w-5 text-purple-600" />
              Top Performing Groups
            </CardTitle>
            <CardDescription>
              {groupsData?.summary ? (
                <span>
                  {groupsData.summary.totalGroups} groups • {formatCurrency(groupsData.summary.totalRevenue)} total revenue
                  {error && (
                    <span className="text-orange-600 ml-2">• Using cached data</span>
                  )}
                </span>
              ) : (
                "Groups with highest performance this period"
              )}
            </CardDescription>
          </div>

          {/* Controls - Mobile Optimized */}
          <div className="flex flex-col sm:flex-row gap-2">
            {/* Period Selector */}
            <div className="flex bg-gray-100 rounded-lg p-1">
              {periodOptions.map((option) => (
                <Button
                  key={option.value}
                  variant={selectedPeriod === option.value ? "default" : "ghost"}
                  size="sm"
                  className={`px-3 py-1 text-xs font-medium transition-all ${
                    selectedPeriod === option.value
                      ? "bg-white shadow-sm"
                      : "hover:bg-gray-200"
                  }`}
                  onClick={() => setSelectedPeriod(option.value)}
                >
                  {option.label}
                </Button>
              ))}
            </div>

            {/* Sort & Actions */}
            <div className="flex gap-1">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="px-3 py-1">
                    <Filter className="h-3 w-3 mr-1" />
                    <span className="hidden sm:inline">
                      {sortOptions.find(opt => opt.value === sortBy)?.label}
                    </span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Sort by</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {sortOptions.map((option) => (
                    <DropdownMenuItem
                      key={option.value}
                      onClick={() => setSortBy(option.value)}
                    >
                      {option.label}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              <Button
                variant="outline"
                size="sm"
                onClick={handleExport}
                disabled={!data?.groups}
                className="px-3 py-1"
                title="Export data as CSV"
              >
                <Download className="h-3 w-3" />
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={() => refetch()}
                disabled={isFetching}
                className="px-3 py-1"
              >
                <RefreshCw className={`h-3 w-3 ${isFetching ? "animate-spin" : ""}`} />
              </Button>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, index) => (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-3 w-24" />
                  </div>
                </div>
                <div className="text-right space-y-2">
                  <Skeleton className="h-4 w-20" />
                  <Skeleton className="h-3 w-16" />
                </div>
                <div className="flex items-center space-x-2">
                  <Skeleton className="h-6 w-16" />
                  <Skeleton className="h-4 w-12" />
                </div>
              </div>
            ))}
          </div>
        ) : showCriticalError ? (
          <TableErrorState
            errorState="server-error"
            title="Unable to Load Groups"
            message={getDisplayMessage(error)}
            onRetry={() => refetch()}
            isRetrying={isFetching}
            showRetry={shouldShowRetry(error)}
          />
        ) : groupsData?.groups && groupsData.groups.length > 0 ? (
          <div className="space-y-3">
            {groupsData.groups.map((group, index) => {
              const statusBadge = getStatusBadge(group);
              const revenuePerMember = group.revenuePerMember;
              const isPositiveGrowth = revenuePerMember > 0;

              return (
                <motion.div
                  key={group.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow duration-200"
                >
                  {/* Group Info */}
                  <div className="flex items-center space-x-4 flex-1 min-w-0">
                    <div className="relative">
                      <Avatar className="h-10 w-10">
                        <AvatarFallback className="bg-gradient-to-br from-purple-500 to-blue-500 text-white font-semibold">
                          {getInitials(group.name)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-white rounded-full flex items-center justify-center">
                        <Users className="h-2 w-2 text-gray-600" />
                      </div>
                    </div>
                    <div className="min-w-0 flex-1">
                      <p className="font-medium text-gray-900 truncate">{group.name}</p>
                      <div className="flex items-center gap-1 text-sm text-gray-500">
                        <MapPin className="h-3 w-3" />
                        <span className="truncate">{group.location}</span>
                      </div>
                      <div className="flex items-center gap-3 mt-1 text-xs text-gray-500">
                        <span>{group.totalMembers} members</span>
                        <span>•</span>
                        <span>{group.activeCustomers} active</span>
                      </div>
                    </div>
                  </div>

                  {/* Performance Metrics - Mobile Responsive */}
                  <div className="hidden sm:block text-right mr-4">
                    <p className="font-semibold text-gray-900">{group.formattedRevenue}</p>
                    <div className="flex items-center gap-1 text-sm text-gray-600">
                      <ShoppingCart className="h-3 w-3" />
                      <span>{group.totalOrders} orders</span>
                    </div>
                    <p className="text-xs text-gray-500">
                      {group.formattedRevenuePerMember}/member
                    </p>
                  </div>

                  {/* Mobile Performance - Compact */}
                  <div className="sm:hidden text-right mr-2">
                    <p className="font-semibold text-sm text-gray-900">{formatCurrency(group.totalRevenue)}</p>
                    <p className="text-xs text-gray-500">{group.totalOrders} orders</p>
                  </div>

                  {/* Status & Actions */}
                  <div className="flex items-center space-x-2">
                    <Badge variant={statusBadge.variant} className="text-xs">
                      {statusBadge.label}
                    </Badge>

                    <div className="hidden sm:flex items-center gap-1">
                      {isPositiveGrowth ? (
                        <TrendingUp className="h-3 w-3 text-green-600" />
                      ) : (
                        <TrendingDown className="h-3 w-3 text-red-600" />
                      )}
                      <span className={`text-xs font-medium ${
                        isPositiveGrowth ? "text-green-600" : "text-red-600"
                      }`}>
                        {formatCurrency(revenuePerMember)}
                      </span>
                    </div>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem>View Details</DropdownMenuItem>
                        <DropdownMenuItem>View Members</DropdownMenuItem>
                        <DropdownMenuItem>Contact Admin</DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </motion.div>
              );
            })}
          </div>
        ) : (
          <TableErrorState
            errorState="no-data"
            title="No Groups Found"
            message="No groups match your current filters for the selected period."
            onRetry={() => refetch()}
            isRetrying={isFetching}
            showRetry={error ? shouldShowRetry(error) : false}
          />
        )}

        {/* Summary Footer */}
        {groupsData?.summary && (
          <div className="mt-6 pt-4 border-t border-gray-200">
            <div className="flex flex-wrap gap-4 text-sm text-gray-600">
              <span>
                Average Group Size: <span className="font-medium text-gray-900">
                  {groupsData.summary.averageGroupSize} members
                </span>
              </span>
              <span className="hidden sm:inline">•</span>
              <span>
                Total Orders: <span className="font-medium text-gray-900">
                  {groupsData.summary.totalOrders.toLocaleString()}
                </span>
              </span>
              <span className="hidden sm:inline">•</span>
              <span>
                Period: <span className="font-medium text-gray-900">
                  Last {selectedPeriod} days
                </span>
              </span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

