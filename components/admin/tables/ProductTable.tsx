// components/admin/tables/ProductTable.tsx
"use client";

import { useState, useMemo, useCallback } from "react";
import Image from "next/image";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import {
  Eye,
  Edit,
  Trash2,
  Search,
  Filter,
  ArrowUpDown,
  MoreHorizontal,
  Package,
  AlertTriangle,
  CheckCircle2,
  XCircle,
  RefreshCw,
  Download,
  Upload,
  Archive,
  Star,
  TrendingUp,
  TrendingDown
} from "lucide-react";
import { useGetAllProductsQuery } from "@/lib/redux/features/products/productsApiSlice";
import { useGetCategoriesQuery } from "@/lib/redux/features/categories/categoriesApiSlice";
import { Product } from "@/types/product";
import { EditProductModal } from "@/components/admin/forms/EditProductModal";
import { DeleteProductModal } from "@/components/admin/forms/DeleteProductModal";
import { motion, AnimatePresence } from "framer-motion";
import { toast } from "sonner";

// Types for sorting and filtering
type SortField = 'name' | 'price' | 'stock' | 'createdAt' | 'category';
type SortDirection = 'asc' | 'desc';

interface TableFilters {
  search: string;
  category: string;
  stockStatus: 'all' | 'in-stock' | 'low-stock' | 'out-of-stock';
  priceRange: 'all' | '0-50' | '50-200' | '200-500' | '500+';
}

export function ProductsTable() {
  const { data: productsResponse, isLoading, isError, refetch } = useGetAllProductsQuery();
  const { data: categoriesResponse } = useGetCategoriesQuery();

  // Extract data with fallbacks - API returns arrays directly, not wrapped in objects
  const products = productsResponse || [];
  const categories = categoriesResponse || [];

  // State management
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [productToDelete, setProductToDelete] = useState<Product | null>(null);
  const [selectedProducts, setSelectedProducts] = useState<Set<string>>(new Set());
  const [sortField, setSortField] = useState<SortField>('createdAt');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');
  const [filters, setFilters] = useState<TableFilters>({
    search: '',
    category: 'all',
    stockStatus: 'all',
    priceRange: 'all'
  });
  const [showFilters, setShowFilters] = useState(false);

  // Helper functions
  const getCategoryName = useCallback((category: Product["category"]) => {
    if (typeof category === "string") {
      const foundCategory = categories.find((cat) => cat._id === category);
      return foundCategory ? foundCategory.name : "Unknown Category";
    } else {
      return category?.name || "Unknown Category";
    }
  }, [categories]);

  const formatDate = useCallback((date: Date | string) => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  }, []);

  const formatCurrency = useCallback((amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 2
    }).format(amount);
  }, []);

  const getStockStatus = useCallback((stock: number) => {
    if (stock === 0) return { status: 'out-of-stock', label: 'Out of Stock', color: 'destructive', className: 'bg-red-100 text-red-800' };
    if (stock <= 10) return { status: 'low-stock', label: 'Low Stock', color: 'outline', className: 'bg-yellow-100 text-yellow-800 border-yellow-300' };
    return { status: 'in-stock', label: 'In Stock', color: 'default', className: 'bg-green-100 text-green-800' };
  }, []);

  // Filtering and sorting logic
  const filteredAndSortedProducts = useMemo(() => {
    let filtered = products.filter(product => {
      // Search filter
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        const matchesSearch =
          product.name.toLowerCase().includes(searchLower) ||
          product.description?.toLowerCase().includes(searchLower) ||
          getCategoryName(product.category).toLowerCase().includes(searchLower);
        if (!matchesSearch) return false;
      }

      // Category filter
      if (filters.category !== 'all') {
        const categoryId = typeof product.category === 'string' ? product.category : product.category._id;
        if (categoryId !== filters.category) return false;
      }

      // Stock status filter
      if (filters.stockStatus !== 'all') {
        const stockStatus = getStockStatus(product.stock).status;
        if (stockStatus !== filters.stockStatus) return false;
      }

      // Price range filter
      if (filters.priceRange !== 'all') {
        const price = product.price;
        switch (filters.priceRange) {
          case '0-50': return price >= 0 && price <= 50;
          case '50-200': return price > 50 && price <= 200;
          case '200-500': return price > 200 && price <= 500;
          case '500+': return price > 500;
          default: return true;
        }
      }

      return true;
    });

    // Sorting
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortField) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'price':
          aValue = a.price;
          bValue = b.price;
          break;
        case 'stock':
          aValue = a.stock;
          bValue = b.stock;
          break;
        case 'createdAt':
          aValue = a.createdAt ? new Date(a.createdAt).getTime() : 0;
          bValue = b.createdAt ? new Date(b.createdAt).getTime() : 0;
          break;
        case 'category':
          aValue = getCategoryName(a.category).toLowerCase();
          bValue = getCategoryName(b.category).toLowerCase();
          break;
        default:
          return 0;
      }

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [products, filters, sortField, sortDirection, getCategoryName, getStockStatus]);

  // Event handlers
  const handleSort = useCallback((field: SortField) => {
    if (sortField === field) {
      setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  }, [sortField]);

  const handleSelectAll = useCallback((checked: boolean) => {
    if (checked) {
      setSelectedProducts(new Set(filteredAndSortedProducts.map(p => p._id)));
    } else {
      setSelectedProducts(new Set());
    }
  }, [filteredAndSortedProducts]);

  const handleSelectProduct = useCallback((productId: string, checked: boolean) => {
    setSelectedProducts(prev => {
      const newSet = new Set(prev);
      if (checked) {
        newSet.add(productId);
      } else {
        newSet.delete(productId);
      }
      return newSet;
    });
  }, []);

  const handleBulkAction = useCallback((action: string) => {
    if (selectedProducts.size === 0) {
      toast.error('No products selected');
      return;
    }

    switch (action) {
      case 'delete':
        toast.info(`Delete ${selectedProducts.size} products - Feature coming soon`);
        break;
      case 'archive':
        toast.info(`Archive ${selectedProducts.size} products - Feature coming soon`);
        break;
      case 'export':
        toast.info(`Export ${selectedProducts.size} products - Feature coming soon`);
        break;
      default:
        break;
    }
  }, [selectedProducts]);

  const clearFilters = useCallback(() => {
    setFilters({
      search: '',
      category: 'all',
      stockStatus: 'all',
      priceRange: 'all'
    });
  }, []);

  // Loading state
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Products
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <Skeleton className="h-12 w-12 rounded" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-[250px]" />
                  <Skeleton className="h-4 w-[200px]" />
                </div>
                <Skeleton className="h-8 w-[100px]" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  // Error state
  if (isError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Products
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert className="border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              Failed to load products. Please try again.
              <Button
                variant="outline"
                size="sm"
                onClick={() => refetch()}
                className="ml-2"
              >
                <RefreshCw className="h-3 w-3 mr-1" />
                Retry
              </Button>
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5 text-[#2A7C6C]" />
            Products ({filteredAndSortedProducts.length})
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className="h-8"
            >
              <Filter className="h-3 w-3 mr-1" />
              Filters
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => refetch()}
              className="h-8"
            >
              <RefreshCw className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Filters Section */}
        <AnimatePresence>
          {showFilters && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="border rounded-lg p-4 bg-gray-50"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Search */}
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-1 block">Search</label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search products..."
                      value={filters.search}
                      onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                      className="pl-10"
                    />
                  </div>
                </div>

                {/* Category Filter */}
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-1 block">Category</label>
                  <Select
                    value={filters.category}
                    onValueChange={(value) => setFilters(prev => ({ ...prev, category: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All Categories" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      {categories.map((category) => (
                        <SelectItem key={category._id} value={category._id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Stock Status Filter */}
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-1 block">Stock Status</label>
                  <Select
                    value={filters.stockStatus}
                    onValueChange={(value: any) => setFilters(prev => ({ ...prev, stockStatus: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All Stock" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Stock</SelectItem>
                      <SelectItem value="in-stock">In Stock</SelectItem>
                      <SelectItem value="low-stock">Low Stock</SelectItem>
                      <SelectItem value="out-of-stock">Out of Stock</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Price Range Filter */}
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-1 block">Price Range</label>
                  <Select
                    value={filters.priceRange}
                    onValueChange={(value: any) => setFilters(prev => ({ ...prev, priceRange: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All Prices" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Prices</SelectItem>
                      <SelectItem value="0-50">R0 - R50</SelectItem>
                      <SelectItem value="50-200">R50 - R200</SelectItem>
                      <SelectItem value="200-500">R200 - R500</SelectItem>
                      <SelectItem value="500+">R500+</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex justify-between items-center mt-4 pt-4 border-t">
                <div className="text-sm text-gray-600">
                  Showing {filteredAndSortedProducts.length} of {products.length} products
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearFilters}
                  className="h-8"
                >
                  Clear Filters
                </Button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Bulk Actions */}
        {selectedProducts.size > 0 && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg"
          >
            <span className="text-sm font-medium text-blue-900">
              {selectedProducts.size} product{selectedProducts.size !== 1 ? 's' : ''} selected
            </span>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBulkAction('export')}
                className="h-8"
              >
                <Download className="h-3 w-3 mr-1" />
                Export
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBulkAction('archive')}
                className="h-8"
              >
                <Archive className="h-3 w-3 mr-1" />
                Archive
              </Button>
              <Button
                variant="destructive"
                size="sm"
                onClick={() => handleBulkAction('delete')}
                className="h-8"
              >
                <Trash2 className="h-3 w-3 mr-1" />
                Delete
              </Button>
            </div>
          </motion.div>
        )}

        {/* Table */}
        <div className="border rounded-lg overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="w-12">
                  <Checkbox
                    checked={selectedProducts.size === filteredAndSortedProducts.length && filteredAndSortedProducts.length > 0}
                    onCheckedChange={handleSelectAll}
                  />
                </TableHead>
                <TableHead className="w-20">Image</TableHead>
                <TableHead>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleSort('name')}
                    className="h-8 p-0 font-medium hover:bg-transparent"
                  >
                    Name
                    <ArrowUpDown className="ml-2 h-3 w-3" />
                  </Button>
                </TableHead>
                <TableHead>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleSort('category')}
                    className="h-8 p-0 font-medium hover:bg-transparent"
                  >
                    Category
                    <ArrowUpDown className="ml-2 h-3 w-3" />
                  </Button>
                </TableHead>
                <TableHead>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleSort('price')}
                    className="h-8 p-0 font-medium hover:bg-transparent"
                  >
                    Price
                    <ArrowUpDown className="ml-2 h-3 w-3" />
                  </Button>
                </TableHead>
                <TableHead>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleSort('stock')}
                    className="h-8 p-0 font-medium hover:bg-transparent"
                  >
                    Stock
                    <ArrowUpDown className="ml-2 h-3 w-3" />
                  </Button>
                </TableHead>
                <TableHead>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleSort('createdAt')}
                    className="h-8 p-0 font-medium hover:bg-transparent"
                  >
                    Created
                    <ArrowUpDown className="ml-2 h-3 w-3" />
                  </Button>
                </TableHead>
                <TableHead className="w-32">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredAndSortedProducts.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-12">
                    <div className="flex flex-col items-center gap-2">
                      <Package className="h-12 w-12 text-gray-400" />
                      <h3 className="text-lg font-medium text-gray-900">No products found</h3>
                      <p className="text-sm text-gray-600">
                        {filters.search || filters.category !== 'all' || filters.stockStatus !== 'all' || filters.priceRange !== 'all'
                          ? 'Try adjusting your filters to see more results.'
                          : 'Get started by adding your first product.'
                        }
                      </p>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                filteredAndSortedProducts.map((product) => {
                  const stockStatus = getStockStatus(product.stock);
                  const isSelected = selectedProducts.has(product._id);

                  return (
                    <TableRow
                      key={product._id}
                      className={`hover:bg-gray-50 transition-colors ${isSelected ? 'bg-blue-50' : ''}`}
                    >
                      <TableCell>
                        <Checkbox
                          checked={isSelected}
                          onCheckedChange={(checked) => handleSelectProduct(product._id, checked as boolean)}
                        />
                      </TableCell>

                      <TableCell>
                        <div className="relative h-12 w-12 overflow-hidden rounded-lg border">
                          <Image
                            src={`/api/images/${product.image}`}
                            alt={product.name}
                            fill
                            className="object-cover"
                            onError={(e) => {
                              const imgElement = e.target as HTMLImageElement;
                              imgElement.src = '/placeholder-product.png';
                            }}
                          />
                        </div>
                      </TableCell>

                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium text-gray-900 line-clamp-1">
                            {product.name}
                          </div>
                          {product.description && (
                            <div className="text-sm text-gray-600 line-clamp-1">
                              {product.description}
                            </div>
                          )}
                        </div>
                      </TableCell>

                      <TableCell>
                        <Badge variant="outline" className="font-normal">
                          {getCategoryName(product.category)}
                        </Badge>
                      </TableCell>

                      <TableCell>
                        <div className="font-medium text-gray-900">
                          {formatCurrency(product.price)}
                        </div>
                      </TableCell>

                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Badge
                            variant={stockStatus.color as "default" | "secondary" | "destructive" | "outline"}
                            className={`font-normal ${stockStatus.className}`}
                          >
                            {product.stock}
                          </Badge>
                          {stockStatus.status === 'low-stock' && (
                            <AlertTriangle className="h-4 w-4 text-yellow-500" />
                          )}
                          {stockStatus.status === 'out-of-stock' && (
                            <XCircle className="h-4 w-4 text-red-500" />
                          )}
                        </div>
                      </TableCell>

                      <TableCell>
                        <div className="text-sm text-gray-600">
                          {formatDate(product.createdAt)}
                        </div>
                      </TableCell>

                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => setSelectedProduct(product)}
                                className="h-8 w-8 p-0"
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                            </DialogTrigger>
                            <DialogContent className="max-w-2xl">
                              <DialogHeader>
                                <DialogTitle className="flex items-center gap-2">
                                  <Package className="h-5 w-5" />
                                  {selectedProduct?.name}
                                </DialogTitle>
                              </DialogHeader>
                              {selectedProduct && (
                                <div className="grid gap-6 py-4">
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div className="aspect-square relative rounded-lg overflow-hidden border">
                                      <Image
                                        src={`/api/images/${selectedProduct.image}`}
                                        alt={selectedProduct.name}
                                        fill
                                        className="object-cover"
                                      />
                                    </div>
                                    <div className="space-y-4">
                                      <div>
                                        <h3 className="font-medium text-gray-900 mb-1">Description</h3>
                                        <p className="text-sm text-gray-600">
                                          {selectedProduct.description || 'No description available'}
                                        </p>
                                      </div>
                                      <div className="grid grid-cols-2 gap-4">
                                        <div>
                                          <h4 className="font-medium text-gray-900 mb-1">Price</h4>
                                          <p className="text-lg font-bold text-[#2A7C6C]">
                                            {formatCurrency(selectedProduct.price)}
                                          </p>
                                        </div>
                                        <div>
                                          <h4 className="font-medium text-gray-900 mb-1">Stock</h4>
                                          <div className="flex items-center gap-2">
                                            <Badge
                                              variant={getStockStatus(selectedProduct.stock).color as "default" | "secondary" | "destructive" | "outline"}
                                              className={`font-normal ${getStockStatus(selectedProduct.stock).className}`}
                                            >
                                              {selectedProduct.stock} units
                                            </Badge>
                                          </div>
                                        </div>
                                      </div>
                                      <div>
                                        <h4 className="font-medium text-gray-900 mb-1">Category</h4>
                                        <Badge variant="outline">
                                          {getCategoryName(selectedProduct.category)}
                                        </Badge>
                                      </div>
                                      <div>
                                        <h4 className="font-medium text-gray-900 mb-1">Created</h4>
                                        <p className="text-sm text-gray-600">
                                          {formatDate(selectedProduct.createdAt)}
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              )}
                            </DialogContent>
                          </Dialog>

                          <EditProductModal product={product} />
                          {productToDelete && productToDelete._id === product._id && (
                            <DeleteProductModal
                              product={product}
                              onClose={() => setProductToDelete(null)}
                            />
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setProductToDelete(product)}
                            className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
