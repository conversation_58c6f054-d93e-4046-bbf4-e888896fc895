// components/admin/stats/CategoryStats.tsx

"use client"

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { 
  FolderOpen, 
  CheckCircle2, 
  BarChart3, 
  TrendingUp, 
  TrendingDown,
  Minus,
  RefreshCw,
  AlertCircle,
  XCircle
} from 'lucide-react'
import { 
  useRealTimeProductAnalytics, 
  formatCategoryStats,
  getAnalyticsError,
  isAnalyticsLoading,
  hasAnalyticsData,
  useRefreshProductAnalyticsMutation
} from "@/lib/redux/features/products/productAnalyticsApiSlice"
import { motion } from "framer-motion"
import { useState } from "react"

const iconMap = {
  <PERSON>older<PERSON><PERSON>,
  CheckCircle2,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Minus
};

const colorMap = {
  blue: "text-blue-600 bg-blue-50",
  green: "text-green-600 bg-green-50", 
  purple: "text-purple-600 bg-purple-50",
  orange: "text-orange-600 bg-orange-50",
  red: "text-red-600 bg-red-50"
};

export function CategoryStats() {
  const [period, setPeriod] = useState(30);
  const { 
    data: response, 
    isLoading, 
    isError, 
    error,
    refetch 
  } = useRealTimeProductAnalytics({ period });
  
  const [refreshAnalytics, { isLoading: isRefreshing }] = useRefreshProductAnalyticsMutation();

  const handleRefresh = async () => {
    try {
      await refreshAnalytics({ period }).unwrap();
    } catch (error) {
      console.error('Failed to refresh analytics:', error);
    }
  };

  const handlePeriodChange = (newPeriod: number) => {
    setPeriod(newPeriod);
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-20 mb-2" />
              <Skeleton className="h-3 w-32" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  // Error state
  if (isError || !response?.success) {
    return (
      <Alert className="border-red-200 bg-red-50">
        <AlertCircle className="h-4 w-4 text-red-600" />
        <AlertDescription className="text-red-800">
          {getAnalyticsError(error)} 
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => refetch()}
            className="ml-2"
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            Retry
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  const analytics = response.analytics;
  const stats = formatCategoryStats(analytics);
  const hasData = hasAnalyticsData(analytics);

  return (
    <div className="space-y-4">
      {/* Period selector and refresh */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-gray-700">Period:</span>
          {[7, 30, 90].map((days) => (
            <Button
              key={days}
              variant={period === days ? "default" : "outline"}
              size="sm"
              onClick={() => handlePeriodChange(days)}
              className="h-8"
            >
              {days}d
            </Button>
          ))}
        </div>
        
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-xs">
            Updated: {new Date(analytics.lastUpdated).toLocaleTimeString()}
          </Badge>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="h-8"
          >
            <RefreshCw className={`h-3 w-3 mr-1 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Stats grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat, index) => {
          const IconComponent = iconMap[stat.icon as keyof typeof iconMap] || FolderOpen;
          const colorClass = colorMap[stat.color as keyof typeof colorMap] || colorMap.blue;
          
          return (
            <motion.div
              key={stat.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="relative overflow-hidden hover:shadow-md transition-shadow">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-gray-700">
                    {stat.title}
                  </CardTitle>
                  <div className={`p-2 rounded-lg ${colorClass}`}>
                    <IconComponent className="h-4 w-4" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-gray-900 mb-1">
                    {stat.value}
                  </div>
                  <div className="flex items-center gap-1">
                    {stat.trend === "up" && <TrendingUp className="h-3 w-3 text-green-600" />}
                    {stat.trend === "down" && <TrendingDown className="h-3 w-3 text-red-600" />}
                    {stat.trend === "neutral" && <Minus className="h-3 w-3 text-gray-400" />}
                    <p className="text-xs text-gray-600">
                      {stat.description}
                    </p>
                  </div>
                </CardContent>
                
                {/* Status indicator */}
                <div className="absolute top-2 right-2">
                  {hasData ? (
                    <CheckCircle2 className="h-3 w-3 text-green-500" />
                  ) : (
                    <XCircle className="h-3 w-3 text-gray-400" />
                  )}
                </div>
              </Card>
            </motion.div>
          );
        })}
      </div>

      {/* Category Performance Table */}
      {analytics.categoryPerformance && analytics.categoryPerformance.length > 0 && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-[#2A7C6C]" />
              Category Performance
            </CardTitle>
            <p className="text-sm text-gray-600">Product distribution across categories</p>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.categoryPerformance.slice(0, 8).map((category, index) => (
                <div key={category._id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-[#2A7C6C] text-white rounded-full flex items-center justify-center text-sm font-medium">
                      {index + 1}
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{category.name}</p>
                      <p className="text-sm text-gray-600">{category.activeProducts} active products</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-lg font-bold text-[#2A7C6C]">{category.productCount}</p>
                    <p className="text-xs text-gray-600">total products</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* No data message */}
      {!hasData && (
        <Alert className="border-yellow-200 bg-yellow-50">
          <AlertCircle className="h-4 w-4 text-yellow-600" />
          <AlertDescription className="text-yellow-800">
            No category data available. Data will appear once categories are created and products are added.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
