// components/admin/modals/GroupRequestApprovalModal.tsx

"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X, CheckCircle, XCircle, AlertTriangle, Loader2, Users } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  useApproveGroupRequestMutation, 
  useRejectGroupRequestMutation,
  type GroupRequest 
} from "@/lib/redux/features/groupRequests/groupRequestsApiSlice";

interface GroupRequestApprovalModalProps {
  isOpen: boolean;
  onClose: () => void;
  request: GroupRequest;
  action: 'approve' | 'reject';
  onComplete: () => void;
}

const modalVariants = {
  hidden: { 
    opacity: 0,
    scale: 0.95,
    y: 20
  },
  visible: { 
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      type: "spring",
      damping: 25,
      stiffness: 300,
      duration: 0.4
    }
  },
  exit: { 
    opacity: 0,
    scale: 0.95,
    y: 20,
    transition: {
      duration: 0.2
    }
  }
};

const overlayVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
  exit: { opacity: 0 }
};

export function GroupRequestApprovalModal({
  isOpen,
  onClose,
  request,
  action,
  onComplete
}: GroupRequestApprovalModalProps) {
  const [reviewNotes, setReviewNotes] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");

  const [approveRequest] = useApproveGroupRequestMutation();
  const [rejectRequest] = useRejectGroupRequestMutation();

  const handleSubmit = async () => {
    if (action === 'reject' && !reviewNotes.trim()) {
      setError("Review notes are required when rejecting a request");
      return;
    }

    setIsSubmitting(true);
    setError("");

    try {
      if (action === 'approve') {
        await approveRequest({
          id: request._id,
          reviewNotes: reviewNotes.trim() || undefined
        }).unwrap();
      } else {
        await rejectRequest({
          id: request._id,
          reviewNotes: reviewNotes.trim()
        }).unwrap();
      }

      onComplete();
      onClose();
    } catch (err: any) {
      console.error(`Error ${action}ing request:`, err);
      setError(err.data?.error || `Failed to ${action} request. Please try again.`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setReviewNotes("");
      setError("");
      onClose();
    }
  };

  if (!isOpen) return null;

  const isApproval = action === 'approve';

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        {/* Overlay */}
        <motion.div
          variants={overlayVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          onClick={handleClose}
        />

        {/* Modal */}
        <motion.div
          variants={modalVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          className="relative w-full max-w-lg bg-white rounded-2xl shadow-2xl overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className={`px-6 py-4 border-b ${isApproval ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className={`w-10 h-10 rounded-xl flex items-center justify-center ${
                  isApproval ? 'bg-green-100' : 'bg-red-100'
                }`}>
                  {isApproval ? (
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-600" />
                  )}
                </div>
                <div>
                  <h3 
                    className={`text-lg font-semibold ${isApproval ? 'text-green-900' : 'text-red-900'}`}
                    style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
                  >
                    {isApproval ? 'Approve' : 'Reject'} Group Request
                  </h3>
                  <p className={`text-sm ${isApproval ? 'text-green-700' : 'text-red-700'}`}>
                    {isApproval ? 'Create a new group and approve this request' : 'Decline this group creation request'}
                  </p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleClose}
                disabled={isSubmitting}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>
          </div>

          {/* Content */}
          <div className="px-6 py-4">
            {/* Request Details */}
            <div className="mb-6 p-4 bg-gray-50 border border-gray-200 rounded-xl">
              <h4 className="text-sm font-medium text-gray-800 mb-3">Request Details</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Requested Group:</span>
                  <span className="font-medium text-gray-900">{request.requestedGroupName}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Requester:</span>
                  <span className="font-medium text-gray-900">{request.userName}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Email:</span>
                  <span className="font-medium text-gray-900">{request.userEmail}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Location:</span>
                  <span className="font-medium text-gray-900 text-right max-w-[200px] truncate" title={request.fullLocationPath}>
                    {request.fullLocationPath}
                  </span>
                </div>
                {request.groupDescription && (
                  <div className="pt-2 border-t border-gray-200">
                    <span className="text-gray-600">Description:</span>
                    <p className="text-gray-900 mt-1">{request.groupDescription}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Warning for Approval */}
            {isApproval && (
              <Alert className="mb-4">
                <Users className="h-4 w-4" />
                <AlertDescription>
                  Approving this request will create a new StokvelGroup with "{request.requestedGroupName}" 
                  as the name and {request.userName} as the group admin.
                </AlertDescription>
              </Alert>
            )}

            {/* Warning for Rejection */}
            {!isApproval && (
              <Alert variant="destructive" className="mb-4">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  Rejecting this request will permanently decline the group creation. 
                  The user will be notified of the rejection.
                </AlertDescription>
              </Alert>
            )}

            {/* Review Notes */}
            <div className="space-y-2">
              <Label htmlFor="reviewNotes" className="text-sm font-medium text-gray-700">
                {isApproval ? 'Approval Notes (Optional)' : 'Rejection Reason *'}
              </Label>
              <Textarea
                id="reviewNotes"
                value={reviewNotes}
                onChange={(e) => setReviewNotes(e.target.value)}
                placeholder={
                  isApproval 
                    ? "Add any notes about the approval (optional)..."
                    : "Please provide a reason for rejecting this request..."
                }
                className={error && !reviewNotes.trim() ? "border-red-300 focus:border-red-500" : ""}
                disabled={isSubmitting}
                rows={4}
                maxLength={1000}
              />
              <p className="text-xs text-gray-500">
                {reviewNotes.length}/1000 characters
              </p>
            </div>

            {/* Error Message */}
            {error && (
              <Alert variant="destructive" className="mt-4">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {/* Action Buttons */}
            <div className="flex gap-3 mt-6">
              <Button
                variant="outline"
                onClick={handleClose}
                disabled={isSubmitting}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={isSubmitting || (!isApproval && !reviewNotes.trim())}
                className={`flex-1 ${
                  isApproval 
                    ? 'bg-green-600 hover:bg-green-700' 
                    : 'bg-red-600 hover:bg-red-700'
                } text-white`}
              >
                {isSubmitting ? (
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    {isApproval ? 'Approving...' : 'Rejecting...'}
                  </div>
                ) : (
                  <>
                    {isApproval ? (
                      <CheckCircle className="h-4 w-4 mr-2" />
                    ) : (
                      <XCircle className="h-4 w-4 mr-2" />
                    )}
                    {isApproval ? 'Approve & Create Group' : 'Reject Request'}
                  </>
                )}
              </Button>
            </div>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
}
