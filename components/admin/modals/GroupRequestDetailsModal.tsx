// components/admin/modals/GroupRequestDetailsModal.tsx

"use client";

import { motion, AnimatePresence } from "framer-motion";
import { 
  X, 
  User, 
  MapPin, 
  Calendar, 
  Clock, 
  CheckCircle, 
  XCircle, 
  FileText,
  Mail,
  Phone,
  Users
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import type { GroupRequest } from "@/lib/redux/features/groupRequests/groupRequestsApiSlice";

interface GroupRequestDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  request: GroupRequest;
}

const modalVariants = {
  hidden: { 
    opacity: 0,
    scale: 0.95,
    y: 20
  },
  visible: { 
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      type: "spring",
      damping: 25,
      stiffness: 300,
      duration: 0.4
    }
  },
  exit: { 
    opacity: 0,
    scale: 0.95,
    y: 20,
    transition: {
      duration: 0.2
    }
  }
};

const overlayVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
  exit: { opacity: 0 }
};

export function GroupRequestDetailsModal({
  isOpen,
  onClose,
  request
}: GroupRequestDetailsModalProps) {
  if (!isOpen) return null;

  const getStatusBadge = (status: string) => {
    const variants = {
      pending: { bg: "bg-yellow-100", text: "text-yellow-800", border: "border-yellow-200", icon: Clock },
      approved: { bg: "bg-green-100", text: "text-green-800", border: "border-green-200", icon: CheckCircle },
      rejected: { bg: "bg-red-100", text: "text-red-800", border: "border-red-200", icon: XCircle },
    };

    const config = variants[status as keyof typeof variants];
    const Icon = config.icon;

    return (
      <Badge variant="outline" className={`${config.bg} ${config.text} ${config.border}`}>
        <span className="flex items-center gap-1">
          <Icon className="h-3 w-3" />
          {status.charAt(0).toUpperCase() + status.slice(1)}
        </span>
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        {/* Overlay */}
        <motion.div
          variants={overlayVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          onClick={onClose}
        />

        {/* Modal */}
        <motion.div
          variants={modalVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          className="relative w-full max-w-2xl bg-white rounded-2xl shadow-2xl overflow-hidden max-h-[90vh] overflow-y-auto"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                  <FileText className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <h3 
                    className="text-lg font-semibold text-gray-900"
                    style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
                  >
                    Group Request Details
                  </h3>
                  <p className="text-sm text-gray-600">
                    Request ID: {request._id}
                  </p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>
          </div>

          {/* Content */}
          <div className="px-6 py-6 space-y-6">
            {/* Status and Dates */}
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Status</h4>
                {getStatusBadge(request.status)}
              </div>
              <div className="text-right">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Request Date</h4>
                <div className="flex items-center gap-1 text-sm text-gray-600">
                  <Calendar className="h-3 w-3" />
                  {formatDate(request.requestDate)}
                </div>
              </div>
            </div>

            <Separator />

            {/* Group Information */}
            <div>
              <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Users className="h-5 w-5" />
                Requested Group
              </h4>
              <div className="bg-blue-50 border border-blue-200 rounded-xl p-4 space-y-3">
                <div>
                  <label className="text-sm font-medium text-blue-800">Group Name</label>
                  <p className="text-blue-900 font-semibold">{request.requestedGroupName}</p>
                </div>
                {request.groupDescription && (
                  <div>
                    <label className="text-sm font-medium text-blue-800">Description</label>
                    <p className="text-blue-900">{request.groupDescription}</p>
                  </div>
                )}
              </div>
            </div>

            <Separator />

            {/* Requester Information */}
            <div>
              <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <User className="h-5 w-5" />
                Requester Information
              </h4>
              <div className="bg-gray-50 border border-gray-200 rounded-xl p-4 space-y-3">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Full Name</label>
                    <p className="text-gray-900 font-medium">{request.userName}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Email Address</label>
                    <div className="flex items-center gap-1">
                      <Mail className="h-3 w-3 text-gray-500" />
                      <p className="text-gray-900">{request.userEmail}</p>
                    </div>
                  </div>
                </div>
                {request.userPhone && (
                  <div>
                    <label className="text-sm font-medium text-gray-600">Phone Number</label>
                    <div className="flex items-center gap-1">
                      <Phone className="h-3 w-3 text-gray-500" />
                      <p className="text-gray-900">{request.userPhone}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <Separator />

            {/* Location Information */}
            <div>
              <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Location Details
              </h4>
              <div className="bg-green-50 border border-green-200 rounded-xl p-4 space-y-3">
                <div>
                  <label className="text-sm font-medium text-green-800">Full Location Path</label>
                  <p className="text-green-900 font-medium">{request.fullLocationPath}</p>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <label className="text-green-700 font-medium">Province</label>
                    <p className="text-green-900">{request.provinceName}</p>
                  </div>
                  <div>
                    <label className="text-green-700 font-medium">City</label>
                    <p className="text-green-900">{request.cityName}</p>
                  </div>
                  <div>
                    <label className="text-green-700 font-medium">Township</label>
                    <p className="text-green-900">{request.townshipName}</p>
                  </div>
                  <div>
                    <label className="text-green-700 font-medium">Location</label>
                    <p className="text-green-900">{request.locationName}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Review Information (if reviewed) */}
            {(request.status === 'approved' || request.status === 'rejected') && (
              <>
                <Separator />
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">Review Information</h4>
                  <div className={`border rounded-xl p-4 space-y-3 ${
                    request.status === 'approved' 
                      ? 'bg-green-50 border-green-200' 
                      : 'bg-red-50 border-red-200'
                  }`}>
                    {request.reviewDate && (
                      <div>
                        <label className={`text-sm font-medium ${
                          request.status === 'approved' ? 'text-green-800' : 'text-red-800'
                        }`}>
                          Review Date
                        </label>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          <p className={request.status === 'approved' ? 'text-green-900' : 'text-red-900'}>
                            {formatDate(request.reviewDate)}
                          </p>
                        </div>
                      </div>
                    )}
                    {request.reviewer && (
                      <div>
                        <label className={`text-sm font-medium ${
                          request.status === 'approved' ? 'text-green-800' : 'text-red-800'
                        }`}>
                          Reviewed By
                        </label>
                        <p className={request.status === 'approved' ? 'text-green-900' : 'text-red-900'}>
                          {request.reviewer.name} ({request.reviewer.email})
                        </p>
                      </div>
                    )}
                    {request.reviewNotes && (
                      <div>
                        <label className={`text-sm font-medium ${
                          request.status === 'approved' ? 'text-green-800' : 'text-red-800'
                        }`}>
                          Review Notes
                        </label>
                        <p className={request.status === 'approved' ? 'text-green-900' : 'text-red-900'}>
                          {request.reviewNotes}
                        </p>
                      </div>
                    )}
                    {request.createdGroup && (
                      <div>
                        <label className="text-sm font-medium text-green-800">Created Group</label>
                        <p className="text-green-900 font-medium">{request.createdGroup.name}</p>
                      </div>
                    )}
                  </div>
                </div>
              </>
            )}
          </div>

          {/* Footer */}
          <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
            <div className="flex justify-end">
              <Button onClick={onClose} variant="outline">
                Close
              </Button>
            </div>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
}
