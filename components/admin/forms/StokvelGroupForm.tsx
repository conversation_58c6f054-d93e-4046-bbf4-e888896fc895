
// components/admin/forms/StokvelGroupForm.tsx

"use client"

import React, { useEffect, useState } from "react"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { useGroups } from "@/lib/redux/hooks/useGroups"
import { useAuth } from "@/context/AuthContext"
import { AlertCircle, CheckCircle2, MapPin } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { createStokvelGroup as createGroup } from "@/lib/frontendGroupUtilities"
import { useLocations } from "@/lib/redux/hooks/useLocations"
import {
  EnhancedCitySelect,
  EnhancedTownshipSelect,
  EnhancedLocationSelect,
  EnhancedDropdownSelect
} from "@/components/ui/enhanced-location-selects"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

const stokvelGroupSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters."),
  description: z.string().min(10, "Description must be at least 10 characters."),
  admin: z.string().min(1, "Admin user ID is required."),
  locationId: z.string().min(1, "Location is required."), // NEW: Use locationId instead of geolocation
  hasDelivery: z.boolean(),
})

type StokvelGroupFormType = z.infer<typeof stokvelGroupSchema>

export function StokvelGroupForm({ onSuccess }: { onSuccess?: () => void }) {
  const { user } = useAuth()
  // Extract only what we need from useGroups
  const { isLoading } = useGroups()
  const [submitting, setSubmitting] = useState(false)
  const [success, setSuccess] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Location selection hooks
  const {
    selectionData,
    handleProvinceChange,
    handleCityChange,
    handleTownshipChange,
    handleLocationChange,
    isSelectionComplete,
  } = useLocations()

  const form = useForm<StokvelGroupFormType>({
    resolver: zodResolver(stokvelGroupSchema),
    defaultValues: {
      name: "",
      description: "",
      admin: "",
      locationId: "", // NEW: Use locationId instead of geolocation
      hasDelivery: false,
    },
  })

  useEffect(() => {
    if (user?._id) {
      form.setValue("admin", user._id)
    }
  }, [user?._id, form])

  // Update form when location selection changes
  useEffect(() => {
    if (selectionData.selectedLocationId) {
      form.setValue("locationId", selectionData.selectedLocationId)
    }
  }, [selectionData.selectedLocationId, form])

  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => {
        onSuccess?.()
      }, 3000)
      return () => clearTimeout(timer)
    }
  }, [success, onSuccess])

  const onSubmit = async (data: StokvelGroupFormType) => {
    setSubmitting(true)
    setError(null)
    try {
      console.log("Submitting form data:", data)
      await createGroup({
        name: data.name,
        description: data.description,
        admin: data.admin,
        locationId: data.locationId, // NEW: Use locationId instead of geolocation
        members: [],
        hasDelivery: data.hasDelivery,
        totalSales: 0,
        avgOrderValue: 0,
        activeOrders: 0,
      })
      console.log("Group created successfully")
      setSuccess(true)
      form.reset()
    } catch (err) {
      console.error("Failed to create StokvelGroup", err)
      setError("Failed to create group. Please try again.")
      setSuccess(false)
    } finally {
      setSubmitting(false)
    }
  }

  if (success) {
    return (
      <Alert className="bg-green-50 border-green-200">
        <CheckCircle2 className="h-4 w-4 text-green-600" />
        <AlertTitle className="text-green-800">Success</AlertTitle>
        <AlertDescription className="text-green-700">
          Your new Stokvel Group was created successfully. This window will close in 3 seconds...
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Group Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter group name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea placeholder="Enter a detailed description of your Stokvel group" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="admin"
          render={({ field }) => (
            <FormItem className="hidden">
              <FormControl>
                <Input type="hidden" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormItem>
          <FormLabel>Admin (You)</FormLabel>
          <FormControl>
            <Input placeholder="Admin Name" readOnly value={user?.name || ""} />
          </FormControl>
        </FormItem>

        {/* NEW: Hierarchical Location Selection */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Location Selection
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Province Selection */}
            <div className="space-y-2">
              <FormLabel>Province</FormLabel>
              <EnhancedDropdownSelect
                options={selectionData.availableProvinces.map(province => ({
                  value: province._id,
                  label: `${province.name} (${province.code})`,
                  description: province.name
                }))}
                value={selectionData.selectedProvinceId}
                onValueChange={handleProvinceChange}
                placeholder="Select a province"
                isLoading={selectionData.isLoading.provinces}
                searchable={true}
                emptyMessage="No provinces available"
              />
            </div>

            {/* City Selection */}
            {selectionData.selectedProvinceId && (
              <div className="space-y-2">
                <FormLabel>City</FormLabel>
                <EnhancedCitySelect
                  cities={selectionData.availableCities}
                  value={selectionData.selectedCityId}
                  onValueChange={handleCityChange}
                  placeholder="Select a city"
                  isLoading={selectionData.isLoading.cities}
                />
              </div>
            )}

            {/* Township Selection */}
            {selectionData.selectedCityId && (
              <div className="space-y-2">
                <FormLabel>Township</FormLabel>
                <EnhancedTownshipSelect
                  townships={selectionData.availableTownships}
                  value={selectionData.selectedTownshipId}
                  onValueChange={handleTownshipChange}
                  placeholder="Select a township"
                  isLoading={selectionData.isLoading.townships}
                />
              </div>
            )}

            {/* Location Selection */}
            {selectionData.selectedTownshipId && (
              <div className="space-y-2">
                <FormLabel>Location</FormLabel>
                <EnhancedLocationSelect
                  locations={selectionData.availableLocations}
                  value={selectionData.selectedLocationId}
                  onValueChange={handleLocationChange}
                  placeholder="Select a location"
                  isLoading={selectionData.isLoading.locations}
                />
              </div>
            )}

            {/* Hidden field for form validation */}
            <FormField
              control={form.control}
              name="locationId"
              render={({ field }) => (
                <FormItem className="hidden">
                  <FormControl>
                    <Input type="hidden" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Selection Status */}
            {!isSelectionComplete && selectionData.selectedProvinceId && (
              <div className="text-sm text-muted-foreground">
                Please complete the location selection to continue.
              </div>
            )}

            {isSelectionComplete && (
              <div className="text-sm text-green-600 font-medium">
                ✓ Location selected successfully
              </div>
            )}
          </CardContent>
        </Card>

        <Separator className="my-4" />

        <FormField
          control={form.control}
          name="hasDelivery"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">Delivery Available?</FormLabel>
                <FormDescription>Enable if your group offers delivery services.</FormDescription>
              </div>
              <FormControl>
                <Switch checked={field.value} onCheckedChange={field.onChange} />
              </FormControl>
            </FormItem>
          )}
        />

        <div className="flex justify-end">
          <Button
            type="submit"
            disabled={isLoading || submitting || !isSelectionComplete}
            className="bg-[#2A7C6C] hover:bg-[#236358] text-white"
          >
            {submitting ? "Creating..." : "Create Group"}
          </Button>
        </div>
      </form>
    </Form>
  )
}

