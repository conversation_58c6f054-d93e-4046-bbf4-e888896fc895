// components/admin/forms/LocationCreationModal.tsx
"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { MapPin, Plus, ChevronRight, Building, Home, Globe, Navigation, CheckCircle2, AlertCircle, Loader2, Check, X, RotateCcw } from "lucide-react";
import { useLocations } from "@/lib/redux/hooks/useLocations";
import { useAuthStatus } from "@/hooks/useAuthStatus";
import { useToast } from "@/components/ui/use-toast";
import { EnhancedProvinceSelect } from "@/components/ui/enhanced-province-select";
import type { CreateLocationData } from "@/types/locations";

interface LocationCreationModalProps {
  onSuccess?: () => void;
}

export function LocationCreationModal({ onSuccess }: LocationCreationModalProps) {
  const [open, setOpen] = useState(false);


  const [currentStep, setCurrentStep] = useState(1);
  const [locationForm, setLocationForm] = useState<CreateLocationData>({
    name: "",
    townshipId: "",
    description: ""
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [submissionState, setSubmissionState] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [submissionResult, setSubmissionResult] = useState<{
    type: 'success' | 'error';
    message: string;
    location?: any;
  } | null>(null);



  const { user, isLoading: authLoading } = useAuthStatus();
  const { toast } = useToast();
  
  const {
    selectionData,
    handleProvinceChange,
    handleCityChange,
    handleTownshipChange,
    isSelectionComplete,
    createLocation,
    provinces,
    availableCities,
    availableTownships,
    isLoading
  } = useLocations();

  // Reset form when modal closes
  useEffect(() => {
    if (!open) {
      setCurrentStep(1);
      setLocationForm({ name: "", townshipId: "", description: "" });
      setIsSubmitting(false);
      setSuggestions([]);
      setShowSuggestions(false);
      setSubmissionState('idle');
      setSubmissionResult(null);
    }
  }, [open]);

  // Update townshipId when selection changes
  useEffect(() => {
    if (selectionData.selectedTownshipId) {
      setLocationForm(prev => ({
        ...prev,
        townshipId: selectionData.selectedTownshipId
      }));
    }
  }, [selectionData.selectedTownshipId]);

  const handleSubmit = async (e?: React.FormEvent | React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    // Validate township ID format
    if (locationForm.townshipId && !/^[0-9a-fA-F]{24}$/.test(locationForm.townshipId)) {
      toast({
        title: "Validation Error",
        description: "Invalid township ID format. Please reselect the township.",
        variant: "destructive",
      });
      return;
    }

    if (!locationForm.name.trim() || !locationForm.townshipId) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    // Additional validation
    if (locationForm.name.trim().length < 2) {
      toast({
        title: "Validation Error",
        description: "Location name must be at least 2 characters long.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    setSubmissionState('loading');
    setSubmissionResult(null);

    try {
      // Use direct API call for better error handling
      const response = await fetch(`/api/locations/locations/${locationForm.townshipId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: locationForm.name,
          description: locationForm.description
        })
      });

      const data = await response.json();

      if (!response.ok) {
        // Handle duplicate error with suggestions
        if (response.status === 409 && data.suggestions) {
          setSuggestions(data.suggestions);
          setShowSuggestions(true);
          setSubmissionState('error');
          setSubmissionResult({
            type: 'error',
            message: `${data.error}. Please choose a different name or select from suggestions below.`
          });
          return;
        }

        setSubmissionState('error');
        setSubmissionResult({
          type: 'error',
          message: data.error || `API call failed with status ${response.status}`
        });
        return;
      }

      // Success state
      setSubmissionState('success');
      setSubmissionResult({
        type: 'success',
        message: "Location (Group) created successfully! Users can now join this location-based group.",
        location: data.location
      });

      // Reset suggestions on success
      setSuggestions([]);
      setShowSuggestions(false);

      // Call onSuccess callback
      onSuccess?.();

    } catch (error: any) {
      // Handle network or other errors
      let errorMessage = "Failed to create location. Please check your connection and try again.";
      if (error?.message) {
        errorMessage = error.message;
      }

      setSubmissionState('error');
      setSubmissionResult({
        type: 'error',
        message: errorMessage
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const canProceedToStep2 = selectionData.selectedProvinceId && selectionData.selectedCityId;
  const canProceedToStep3 = canProceedToStep2 && selectionData.selectedTownshipId;
  const canSubmit = canProceedToStep3 && locationForm.name.trim();



  const selectedProvince = provinces.find(p => p._id === selectionData.selectedProvinceId);
  const selectedCity = availableCities.find(c => c._id === selectionData.selectedCityId);
  const selectedTownship = availableTownships.find(t => t._id === selectionData.selectedTownshipId);

  // Handle retry after error
  const handleRetry = () => {
    setSubmissionState('idle');
    setSubmissionResult(null);
    setSuggestions([]);
    setShowSuggestions(false);
  };

  // Handle close after success
  const handleCloseAfterSuccess = () => {
    setOpen(false);
  };

  // Handle try again with different name
  const handleTryAgain = () => {
    setSubmissionState('idle');
    setSubmissionResult(null);
    // Keep suggestions visible for user to choose from
  };

  if (authLoading) {
    return <Button disabled>Loading...</Button>;
  }

  return (
    <Dialog
      open={open}
      onOpenChange={(newOpen) => {
        console.log('Dialog onOpenChange called', { newOpen, isSubmitting });
        // Prevent closing during submission
        if (isSubmitting && !newOpen) {
          console.log('Preventing dialog close during submission');
          return;
        }
        setOpen(newOpen);
      }}
    >
      <DialogTrigger asChild>
        <Button className="bg-[#2A7C6C] hover:bg-[#236358]">
          <Plus className="h-4 w-4 mr-2" />
          Create Location Group
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5 text-[#2A7C6C]" />
            Create New Location Group
          </DialogTitle>
          <p className="text-sm text-muted-foreground">
            Create a location-based group where customers from the same area can shop together.
          </p>
        </DialogHeader>

        {!user ? (
          <div className="text-center py-8">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-500">You must be logged in to create a location group.</p>
          </div>
        ) : submissionState === 'loading' ? (
          // Loading State
          <div className="text-center py-12">
            <Loader2 className="h-16 w-16 text-[#2A7C6C] mx-auto mb-6 animate-spin" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Creating Location Group...</h3>
            <p className="text-gray-600">Please wait while we set up your location-based group.</p>
          </div>
        ) : submissionState === 'success' ? (
          // Success State
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Check className="h-8 w-8 text-green-600" />
            </div>
            <h3 className="text-xl font-semibold text-green-900 mb-2">Location Group Created!</h3>
            <p className="text-gray-600 mb-6">{submissionResult?.message}</p>
            {submissionResult?.location && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                <h4 className="font-medium text-green-900 mb-2">Group Details:</h4>
                <p className="text-sm text-green-800">
                  <strong>Name:</strong> {submissionResult.location.name}
                </p>
                <p className="text-sm text-green-800">
                  <strong>Location:</strong> {selectedTownship?.name}, {selectedCity?.name}, {selectedProvince?.name}
                </p>
              </div>
            )}
            <Button onClick={handleCloseAfterSuccess} className="bg-[#2A7C6C] hover:bg-[#236358]">
              <Check className="h-4 w-4 mr-2" />
              Done
            </Button>
          </div>
        ) : submissionState === 'error' ? (
          // Error State
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <X className="h-8 w-8 text-red-600" />
            </div>
            <h3 className="text-xl font-semibold text-red-900 mb-2">Creation Failed</h3>
            <p className="text-gray-600 mb-6">{submissionResult?.message}</p>

            {/* Show suggestions if available */}
            {showSuggestions && suggestions.length > 0 && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                <h4 className="font-medium text-yellow-900 mb-3">Try these alternative names:</h4>
                <div className="flex flex-wrap gap-2 justify-center">
                  {suggestions.map((suggestion, index) => (
                    <button
                      key={index}
                      type="button"
                      onClick={() => {
                        setLocationForm(prev => ({
                          ...prev,
                          name: suggestion
                        }));
                        handleTryAgain();
                      }}
                      className="px-3 py-1 text-sm bg-yellow-100 hover:bg-yellow-200 text-yellow-800 rounded-full transition-colors"
                    >
                      {suggestion}
                    </button>
                  ))}
                </div>
              </div>
            )}

            <div className="flex gap-3 justify-center">
              <Button onClick={handleTryAgain} variant="outline">
                <RotateCcw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
              <Button onClick={() => setOpen(false)} variant="outline">
                Cancel
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Progress Steps */}
            <div className="flex items-center justify-between">
              {[1, 2, 3].map((step) => (
                <div key={step} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    currentStep >= step
                      ? 'bg-[#2A7C6C] text-white'
                      : 'bg-gray-200 text-gray-600'
                  }`}>
                    {step}
                  </div>
                  {step < 3 && (
                    <ChevronRight className={`h-4 w-4 mx-2 ${
                      currentStep > step ? 'text-[#2A7C6C]' : 'text-gray-400'
                    }`} />
                  )}
                </div>
              ))}
            </div>

            <div className="space-y-6">
              {/* Step 1: Province & City Selection */}
              {currentStep >= 1 && (
                <Card className="border-l-4 border-l-blue-500">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Globe className="h-5 w-5 text-blue-500" />
                      Step 1: Select Province & City
                    </CardTitle>
                    <CardDescription>
                      Choose the province and city where the group will be located.
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <Label>Province</Label>
                        <EnhancedProvinceSelect
                          provinces={provinces}
                          value={selectionData.selectedProvinceId}
                          onValueChange={handleProvinceChange}
                          placeholder="Choose a province"
                          isLoading={isLoading.provinces}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>City</Label>
                        <Select 
                          value={selectionData.selectedCityId} 
                          onValueChange={handleCityChange}
                          disabled={!selectionData.selectedProvinceId || isLoading.cities}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Choose a city" />
                          </SelectTrigger>
                          <SelectContent className="bg-white border border-gray-200 shadow-lg rounded-lg max-h-60 overflow-y-auto">
                            {availableCities.map((city) => (
                              <SelectItem
                                key={city._id}
                                value={city._id}
                                className="bg-white hover:bg-gray-50 focus:bg-gray-50 text-gray-900 cursor-pointer px-3 py-2 text-sm"
                              >
                                {city.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    {canProceedToStep2 && (
                      <div className="flex items-center gap-2 text-sm text-green-600">
                        <CheckCircle2 className="h-4 w-4" />
                        Province and city selected: {selectedProvince?.name}, {selectedCity?.name}
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Step 2: Township Selection */}
              {currentStep >= 2 && canProceedToStep2 && (
                <Card className="border-l-4 border-l-purple-500">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Building className="h-5 w-5 text-purple-500" />
                      Step 2: Select Township
                    </CardTitle>
                    <CardDescription>
                      Choose the specific township within {selectedCity?.name}.
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label>Township</Label>
                      <Select 
                        value={selectionData.selectedTownshipId} 
                        onValueChange={handleTownshipChange}
                        disabled={!selectionData.selectedCityId || isLoading.townships}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Choose a township" />
                        </SelectTrigger>
                        <SelectContent>
                          {availableTownships.map((township) => (
                            <SelectItem key={township._id} value={township._id}>
                              {township.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    {canProceedToStep3 && (
                      <div className="flex items-center gap-2 text-sm text-green-600">
                        <CheckCircle2 className="h-4 w-4" />
                        Township selected: {selectedTownship?.name}
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Step 3: Location Details */}
              {currentStep >= 3 && canProceedToStep3 && (
                <Card className="border-l-4 border-l-green-500">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Navigation className="h-5 w-5 text-green-500" />
                      Step 3: Location Details
                    </CardTitle>
                    <CardDescription>
                      Provide details for the specific location within {selectedTownship?.name}.
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="location-name">Location Name *</Label>
                      <Input
                        id="location-name"
                        placeholder="e.g., Orlando East, Tafelsig, etc."
                        value={locationForm.name}
                        onChange={(e) => {
                          setLocationForm(prev => ({
                            ...prev,
                            name: e.target.value
                          }));
                          // Hide suggestions when user starts typing
                          if (showSuggestions) {
                            setShowSuggestions(false);
                          }
                        }}
                        required
                      />

                      {/* Suggestions for duplicate names */}
                      {showSuggestions && suggestions.length > 0 && (
                        <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                          <p className="text-sm font-medium text-yellow-800 mb-2">
                            Suggested alternative names:
                          </p>
                          <div className="flex flex-wrap gap-2">
                            {suggestions.map((suggestion, index) => (
                              <button
                                key={index}
                                type="button"
                                onClick={() => {
                                  setLocationForm(prev => ({
                                    ...prev,
                                    name: suggestion
                                  }));
                                  setShowSuggestions(false);
                                  setSuggestions([]);
                                }}
                                className="px-3 py-1 text-sm bg-yellow-100 hover:bg-yellow-200 text-yellow-800 rounded-full transition-colors"
                              >
                                {suggestion}
                              </button>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="location-description">Description (Optional)</Label>
                      <Textarea
                        id="location-description"
                        placeholder="Brief description of the location and group shopping area..."
                        value={locationForm.description}
                        onChange={(e) => setLocationForm(prev => ({ 
                          ...prev, 
                          description: e.target.value 
                        }))}
                        rows={3}
                      />
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Navigation Buttons - Only show during form steps */}
              {submissionState === 'idle' && (
                <div className="flex justify-between pt-4">
                  <div className="flex gap-2">
                    {currentStep > 1 && (
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setCurrentStep(prev => prev - 1)}
                      >
                        Previous
                      </Button>
                    )}
                  </div>

                  <div className="flex gap-2">
                    {currentStep < 3 ? (
                      <Button
                        type="button"
                        onClick={() => setCurrentStep(prev => prev + 1)}
                        disabled={
                          (currentStep === 1 && !canProceedToStep2) ||
                          (currentStep === 2 && !canProceedToStep3)
                        }
                        className="bg-[#2A7C6C] hover:bg-[#236358]"
                      >
                        Next
                      </Button>
                    ) : (
                      <>
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => setOpen(false)}
                        >
                          Cancel
                        </Button>
                        <Button
                          type="button"
                          onClick={handleSubmit}
                          disabled={!canSubmit || isSubmitting || currentStep !== 3}
                          className="bg-[#2A7C6C] hover:bg-[#236358]"
                        >
                          {isSubmitting ? "Creating..." : "Create Location Group"}
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Info Box - Only show during form steps */}
            {submissionState === 'idle' && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <MapPin className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div className="text-sm">
                    <p className="font-medium text-blue-900 mb-1">About Location Groups</p>
                    <p className="text-blue-700">
                      Each location represents a shopping group where customers from the same area can join together
                      for bulk purchasing benefits. Once created, customers can discover and join this location-based group.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
