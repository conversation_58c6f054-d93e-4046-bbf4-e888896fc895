// components/admin/forms/AddProductModal.tsx

"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { AddProductForm } from "./AddProductForm";
import { Plus, Package } from "lucide-react";

interface AddProductModalProps {
  onSuccess?: () => void;
  trigger?: React.ReactNode;
}

export function AddProductModal({ onSuccess, trigger }: AddProductModalProps) {
  const [open, setOpen] = useState(false);

  const handleSuccess = () => {
    // Close modal after successful creation
    setTimeout(() => {
      setOpen(false);
      onSuccess?.();
    }, 2000);
  };

  const defaultTrigger = (
    <Button className="bg-[#2A7C6C] hover:bg-[#236358] text-white">
      <Plus className="h-4 w-4 mr-2" />
      Add Product
    </Button>
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl font-semibold">
            <Package className="h-5 w-5 text-[#2A7C6C]" />
            Add New Product
          </DialogTitle>
          <p className="text-sm text-gray-600 mt-1">
            Create a new product for your store. Fill in all the required information below.
          </p>
        </DialogHeader>

        <div className="mt-4">
          <AddProductForm onSuccess={handleSuccess} />
        </div>
      </DialogContent>
    </Dialog>
  );
}

