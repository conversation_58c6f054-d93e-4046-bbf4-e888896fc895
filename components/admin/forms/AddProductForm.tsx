// components/admin/forms/AddProductForm.tsx

"use client";


import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useGetCategoriesQuery } from "@/lib/redux/features/categories/categoriesApiSlice";
import { useCreateProductMutation } from "@/lib/redux/features/products/productsApiSlice";
import { CheckCircle2, AlertCircle, Upload, X, Loader2 } from "lucide-react";
import { motion } from "framer-motion";

/**
 * 1) Conditionally define the "image" field so it doesn't reference
 *    File in SSR.
 *    - On the server (build time), use z.any()
 *    - In the browser, use z.instanceof(File)
 */
const fileField = typeof window === "undefined"
  ? z.any()
  : z.instanceof(File)
      .refine((file) => file.size <= 5_000_000, "Max image size is 5MB.")
      .refine(
        (file) => ["image/jpeg", "image/png", "image/webp"].includes(file.type),
        "Only .jpg, .png, and .webp formats are supported."
      );

/**
 * 2) Build the rest of the product schema with the conditional fileField
 */
const productSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters."),
  description: z.string().min(10, "Description must be at least 10 characters."),
  price: z.number().positive("Price must be a positive number."),
  category: z.string().nonempty("Category is required."),
  stock: z.number().int().nonnegative("Stock must be a non-negative integer."),
  image: fileField,
});

type ProductFormType = z.infer<typeof productSchema>;

export function AddProductForm({ onSuccess }: { onSuccess?: () => void }) {
  const form = useForm<ProductFormType>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      name: "",
      description: "",
      price: 0,
      category: "",
      stock: 0,
    },
  });

  const { data: categoriesResponse, isLoading: categoriesLoading, error: categoriesError } = useGetCategoriesQuery();
  const [createProduct, { isLoading: isCreating, error: createError }] = useCreateProductMutation();

  const [submissionState, setSubmissionState] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [filePreview, setFilePreview] = useState<string | null>(null);

  // Handle file selection
  const handleFileChange = (file: File | undefined) => {
    if (file) {
      setSelectedFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setFilePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      setSelectedFile(null);
      setFilePreview(null);
    }
  };

  // Remove selected file
  const removeFile = () => {
    setSelectedFile(null);
    setFilePreview(null);
    form.setValue('image', undefined as any);
  };

  const onSubmit = async (data: ProductFormType) => {
    try {
      setSubmissionState('loading');

      // Validate file is selected
      if (!selectedFile) {
        form.setError('image', { message: 'Product image is required' });
        setSubmissionState('error');
        return;
      }

      // Build FormData to upload file + fields
      const formData = new FormData();
      formData.append("name", data.name.trim());
      formData.append("description", data.description.trim());
      formData.append("price", data.price.toString());
      formData.append("category", data.category);
      formData.append("stock", data.stock.toString());
      formData.append("image", selectedFile);

      await createProduct(formData).unwrap();

      setSubmissionState('success');
      form.reset();
      setSelectedFile(null);
      setFilePreview(null);

      // Call success callback after a short delay
      setTimeout(() => {
        onSuccess?.();
      }, 2000);

    } catch (error: any) {
      console.error("Failed to create Product", error);
      setSubmissionState('error');

      // Handle specific error messages
      if (error?.data?.error) {
        form.setError('root', { message: error.data.error });
      } else if (error?.message) {
        form.setError('root', { message: error.message });
      } else {
        form.setError('root', { message: 'Failed to create product. Please try again.' });
      }
    }
  };

  // Loading state
  if (submissionState === 'loading') {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <Loader2 className="h-12 w-12 text-[#2A7C6C] mb-4 animate-spin" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Creating Product...</h3>
        <p className="text-sm text-gray-600">Please wait while we save your product.</p>
      </div>
    );
  }

  // Success state
  if (submissionState === 'success') {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="flex flex-col items-center justify-center py-12 text-center"
      >
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
          <CheckCircle2 className="h-8 w-8 text-green-600" />
        </div>
        <h3 className="text-lg font-semibold text-green-900 mb-2">Product Created Successfully!</h3>
        <p className="text-sm text-gray-600 mb-4">
          Your new product has been added to the catalog and is now available for customers.
        </p>
        <Badge variant="outline" className="text-green-700 border-green-200">
          This window will close automatically
        </Badge>
      </motion.div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Error Alert */}
      {submissionState === 'error' && (
        <Alert className="border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            {form.formState.errors.root?.message || 'Failed to create product. Please check your inputs and try again.'}
          </AlertDescription>
        </Alert>
      )}

      {/* Categories Loading Error */}
      {categoriesError && (
        <Alert className="border-yellow-200 bg-yellow-50">
          <AlertCircle className="h-4 w-4 text-yellow-600" />
          <AlertDescription className="text-yellow-800">
            Failed to load categories. Some features may not work properly.
          </AlertDescription>
        </Alert>
      )}

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Name */}
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium text-gray-700">Product Name *</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter product name (e.g., Organic Apples)"
                    className="focus:ring-2 focus:ring-[#2A7C6C] focus:border-transparent"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Description */}
          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium text-gray-700">Description *</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe your product in detail (minimum 10 characters)"
                    className="focus:ring-2 focus:ring-[#2A7C6C] focus:border-transparent min-h-[100px]"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Price and Stock Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Price */}
            <FormField
              control={form.control}
              name="price"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium text-gray-700">Price (ZAR) *</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">R</span>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="0.00"
                        className="pl-8 focus:ring-2 focus:ring-[#2A7C6C] focus:border-transparent"
                        {...field}
                        onChange={(e) => field.onChange(Number.parseFloat(e.target.value) || 0)}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Stock */}
            <FormField
              control={form.control}
              name="stock"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium text-gray-700">Stock Quantity *</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="0"
                      placeholder="0"
                      className="focus:ring-2 focus:ring-[#2A7C6C] focus:border-transparent"
                      {...field}
                      onChange={(e) => field.onChange(Number.parseInt(e.target.value, 10) || 0)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Category */}
          <FormField
            control={form.control}
            name="category"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium text-gray-700">Category *</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value} disabled={categoriesLoading}>
                  <FormControl>
                    <SelectTrigger className="focus:ring-2 focus:ring-[#2A7C6C] focus:border-transparent">
                      <SelectValue placeholder={categoriesLoading ? "Loading categories..." : "Select a category"} />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent className="bg-white border border-gray-200 shadow-lg rounded-lg max-h-60 overflow-y-auto">
                    {categoriesResponse?.categories?.map((category) => (
                      <SelectItem
                        key={category._id}
                        value={category._id}
                        className="bg-white hover:bg-gray-50 focus:bg-gray-50 text-gray-900 cursor-pointer px-3 py-2 text-sm"
                      >
                        {category.name}
                      </SelectItem>
                    )) || []}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Image Upload */}
          <FormField
            control={form.control}
            name="image"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium text-gray-700">Product Image *</FormLabel>
                <FormControl>
                  <div className="space-y-4">
                    {/* File Input */}
                    <div className="flex items-center justify-center w-full">
                      <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors">
                        <div className="flex flex-col items-center justify-center pt-5 pb-6">
                          <Upload className="w-8 h-8 mb-2 text-gray-400" />
                          <p className="mb-2 text-sm text-gray-500">
                            <span className="font-semibold">Click to upload</span> or drag and drop
                          </p>
                          <p className="text-xs text-gray-500">PNG, JPG or WEBP (MAX. 5MB)</p>
                        </div>
                        <input
                          type="file"
                          className="hidden"
                          accept="image/jpeg,image/png,image/webp"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            field.onChange(file);
                            handleFileChange(file);
                          }}
                        />
                      </label>
                    </div>

                    {/* File Preview */}
                    {filePreview && selectedFile && (
                      <div className="relative">
                        <div className="flex items-center gap-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                          <img
                            src={filePreview}
                            alt="Preview"
                            className="w-12 h-12 object-cover rounded"
                          />
                          <div className="flex-1">
                            <p className="text-sm font-medium text-green-900">{selectedFile.name}</p>
                            <p className="text-xs text-green-700">
                              {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                            </p>
                          </div>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={removeFile}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Submit Button */}
          <div className="flex justify-end pt-4 border-t">
            <Button
              type="submit"
              disabled={isCreating || categoriesLoading || submissionState === 'loading'}
              className="bg-[#2A7C6C] hover:bg-[#236358] px-8"
            >
              {isCreating || submissionState === 'loading' ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Creating Product...
                </>
              ) : (
                'Create Product'
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
