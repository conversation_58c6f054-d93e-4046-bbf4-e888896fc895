// components/admin/DashboardStats.tsx

"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { ConnectionStatus } from "@/components/ui/error-components";
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  ShoppingCart,
  Users,
  BarChart3,
  RefreshCw,
  AlertCircle,
  Minus
} from "lucide-react";
import { useGetDashboardStatsQuery, type DashboardStats } from "@/lib/redux/features/admin/adminDashboardApiSlice";
import { motion } from "framer-motion";

interface DashboardStatsProps {
  period?: string;
  className?: string;
}

export function DashboardStats({ period = "30", className = "" }: DashboardStatsProps) {
  const [selectedPeriod, setSelectedPeriod] = useState(period);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const {
    data,
    isLoading,
    error,
    refetch,
    isFetching
  } = useGetDashboardStatsQuery({ period: selectedPeriod });



  // Simple fallback data
  const fallbackStats: DashboardStats = {
    revenue: { value: 0, trend: 0, description: "No data available", formatted: "R0" },
    orders: { value: 0, trend: 0, description: "No orders found", formatted: "0" },
    customers: { value: 0, trend: 0, description: "No customers found", formatted: "0%" },
    averageSale: { value: 0, trend: 0, description: "No sales data", formatted: "R0" },
    totalGroups: 0,
    totalProducts: 0,
    period: parseInt(selectedPeriod),
    lastUpdated: new Date().toISOString()
  };

  // Use real data if available, otherwise use fallback data
  const stats = data?.stats || fallbackStats;

  const periodOptions = [
    { value: "7", label: "7D" },
    { value: "30", label: "30D" },
    { value: "90", label: "90D" },
    { value: "365", label: "1Y" }
  ];

  const getTrendIcon = (trend: number) => {
    if (trend > 0) return TrendingUp;
    if (trend < 0) return TrendingDown;
    return Minus;
  };

  const getTrendColor = (trend: number) => {
    if (trend > 0) return "text-green-600";
    if (trend < 0) return "text-red-600";
    return "text-gray-600";
  };

  const formatTrend = (trend: number) => {
    const sign = trend >= 0 ? "+" : "";
    return `${sign}${trend.toFixed(1)}%`;
  };

  // Show error alert for any errors
  const showErrorAlert = !!error;

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Error Alert */}
      {showErrorAlert && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to load dashboard statistics. Using cached data.
            <Button
              variant="link"
              className="p-0 h-auto ml-2"
              onClick={() => refetch()}
            >
              Try again
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Period Selector - Mobile Optimized */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
        <div className="flex items-center gap-2">
          <h3 className="text-lg font-semibold text-gray-900">Dashboard Overview</h3>
          {error && (
            <Badge variant="outline" className="text-xs text-orange-600 border-orange-200">
              Using fallback data
            </Badge>
          )}

        </div>
        <div className="flex items-center gap-2">
          <div className="flex bg-gray-100 rounded-lg p-1">
            {periodOptions.map((option) => (
              <Button
                key={option.value}
                variant={selectedPeriod === option.value ? "default" : "ghost"}
                size="sm"
                className={`px-3 py-1 text-xs font-medium transition-all ${
                  selectedPeriod === option.value
                    ? "bg-white shadow-sm"
                    : "hover:bg-gray-200"
                }`}
                onClick={() => setSelectedPeriod(option.value)}
              >
                {option.label}
              </Button>
            ))}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => refetch()}
            disabled={isFetching}
            className="px-3 py-1"
          >
            <RefreshCw className={`h-3 w-3 ${isFetching ? "animate-spin" : ""}`} />
          </Button>
        </div>
      </div>

      {/* Stats Grid - Mobile First */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
        {isLoading ? (
          // Loading Skeletons
          Array.from({ length: 4 }).map((_, index) => (
            <Card key={index} className="border-gray-200">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-4 w-4 rounded" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-24 mb-2" />
                <Skeleton className="h-3 w-32" />
              </CardContent>
            </Card>
          ))
        ) : stats ? (
          // Real Data
          [
            {
              title: "Total Revenue",
              value: stats.revenue.formatted,
              change: formatTrend(stats.revenue.trend),
              trend: stats.revenue.trend,
              icon: DollarSign,
              description: stats.revenue.description,
              color: "text-green-600"
            },
            {
              title: "Orders",
              value: stats.orders.formatted,
              change: formatTrend(stats.orders.trend),
              trend: stats.orders.trend,
              icon: ShoppingCart,
              description: stats.orders.description,
              color: "text-blue-600"
            },
            {
              title: "Customer Growth",
              value: stats.customers.formatted,
              change: formatTrend(stats.customers.trend),
              trend: stats.customers.trend,
              icon: Users,
              description: stats.customers.description,
              color: "text-purple-600"
            },
            {
              title: "Average Sale",
              value: stats.averageSale.formatted,
              change: formatTrend(stats.averageSale.trend),
              trend: stats.averageSale.trend,
              icon: BarChart3,
              description: stats.averageSale.description,
              color: "text-orange-600"
            }
          ].map((stat, index) => {
            const Icon = stat.icon;
            const TrendIcon = getTrendIcon(stat.trend);
            const trendColor = getTrendColor(stat.trend);

            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="border-gray-200 hover:shadow-md transition-shadow duration-200">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium text-gray-700">
                      {stat.title}
                    </CardTitle>
                    <div className={`p-2 rounded-lg bg-gray-50 ${stat.color}`}>
                      <Icon className="h-4 w-4" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl sm:text-3xl font-bold text-gray-900 mb-1">
                      {stat.value}
                    </div>
                    <div className="flex items-center space-x-1 text-xs text-gray-600">
                      <TrendIcon className={`h-3 w-3 ${trendColor}`} />
                      <span className={`font-medium ${trendColor}`}>
                        {stat.change}
                      </span>
                      <span className="hidden sm:inline">{stat.description}</span>
                      <span className="sm:hidden">vs last period</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })
        ) : null}
      </div>

      {/* Additional Info - Mobile Optimized */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 text-xs text-gray-500">
        <div className="flex flex-col sm:flex-row gap-2">
          <span>Total Groups: {stats.totalGroups}</span>
          <span className="hidden sm:inline">•</span>
          <span>Total Products: {stats.totalProducts}</span>
        </div>
        <div className="flex items-center gap-2 text-xs text-gray-500">
          <div className={`w-2 h-2 rounded-full ${!error ? 'bg-green-500' : 'bg-red-500'}`}></div>
          <span>{!error ? 'Connected' : 'Using fallback data'}</span>
          {isClient && stats.lastUpdated && (
            <span>• Updated {new Date(stats.lastUpdated).toLocaleTimeString()}</span>
          )}
        </div>
      </div>
    </div>
  );
}

