"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { ShoppingBag, TrendingUp, Users, DollarSign, RefreshCw, AlertTriangle } from 'lucide-react'
import { Button } from "@/components/ui/button"
import { useGetOrderOverviewQuery } from "@/lib/redux/features/orders/ordersApiSlice"
import { Skeleton } from "@/components/ui/skeleton"

// Helper function to format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-ZA', {
    style: 'currency',
    currency: 'ZAR',
    minimumFractionDigits: 2,
  }).format(amount);
};

// Helper function to format growth percentage
const formatGrowth = (growth: number) => {
  const sign = growth >= 0 ? '+' : '';
  return `${sign}${growth.toFixed(1)}%`;
};

// Helper function to get trend color
const getTrendColor = (growth: number) => {
  return growth >= 0 ? 'text-green-500' : 'text-red-500';
};

export function OrdersOverview() {
  const { data: overview, isLoading, isError, error, refetch } = useGetOrderOverviewQuery();

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-20 mb-2" />
              <Skeleton className="h-3 w-32" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (isError) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="col-span-full">
          <CardContent className="flex items-center justify-center py-8">
            <div className="text-center space-y-4">
              <AlertTriangle className="h-12 w-12 text-red-500 mx-auto" />
              <div>
                <h3 className="text-lg font-semibold text-red-700">Failed to Load Order Overview</h3>
                <p className="text-sm text-muted-foreground mt-1">
                  {error && 'data' in error ? (error.data as any)?.message || 'An error occurred' : 'Network error'}
                </p>
              </div>
              <Button onClick={() => refetch()} variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!overview) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="col-span-full">
          <CardContent className="flex items-center justify-center py-8">
            <p className="text-muted-foreground">No order data available</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const overviewItems = [
    {
      title: "Total Orders",
      value: overview.totalOrders.toLocaleString(),
      icon: ShoppingBag,
      trend: formatGrowth(overview.ordersGrowth),
      trendLabel: "from last month",
      growth: overview.ordersGrowth,
    },
    {
      title: "Group Orders",
      value: overview.groupOrders.toLocaleString(),
      icon: Users,
      trend: formatGrowth(overview.groupOrdersGrowth),
      trendLabel: "from last month",
      growth: overview.groupOrdersGrowth,
    },
    {
      title: "Average Order Value",
      value: formatCurrency(overview.averageOrderValue),
      icon: TrendingUp,
      trend: formatGrowth(overview.avgOrderGrowth),
      trendLabel: "from last month",
      growth: overview.avgOrderGrowth,
    },
    {
      title: "Total Revenue",
      value: formatCurrency(overview.totalRevenue),
      icon: DollarSign,
      trend: formatGrowth(overview.revenueGrowth),
      trendLabel: "from last month",
      growth: overview.revenueGrowth,
    },
  ];

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {overviewItems.map((item, index) => (
        <Card key={index}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {item.title}
            </CardTitle>
            <item.icon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{item.value}</div>
            <p className="text-xs text-muted-foreground">
              <span className={getTrendColor(item.growth)}>{item.trend}</span> {item.trendLabel}
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

