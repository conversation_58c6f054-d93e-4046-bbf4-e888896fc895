"use client";

import { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  ShoppingCart,
  DollarSign,
  Activity,
  AlertTriangle,
  Shield,
  Key,
  Ban,
  CheckCircle,
  Edit,
  Save,
  X,
  Clock,
  TrendingUp,
  Users,
  Target
} from 'lucide-react';
import { UserAnalytics } from '@/lib/redux/features/users/usersApiSlice';
import { toast } from 'sonner';

interface UserDetailModalProps {
  user: UserAnalytics | null;
  isOpen: boolean;
  onClose: () => void;
  onUserUpdate?: (userId: string, updates: any) => void;
  onPasswordReset?: (userId: string) => void;
  onAccountSuspension?: (userId: string, reason: string) => void;
  onAccountReactivation?: (userId: string) => void;
}

// Helper functions
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-ZA', {
    style: 'currency',
    currency: 'ZAR',
    minimumFractionDigits: 2,
  }).format(amount);
};

const formatDate = (date: string | Date) => {
  if (!date) return 'Never';
  return new Intl.DateTimeFormat('en-ZA', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(new Date(date));
};

const formatPercentage = (value: number) => {
  return `${(value * 100).toFixed(1)}%`;
};

const getStatusColor = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'active': return 'bg-green-100 text-green-800 border-green-200';
    case 'inactive': return 'bg-gray-100 text-gray-800 border-gray-200';
    case 'suspended': return 'bg-red-100 text-red-800 border-red-200';
    case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    default: return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const getRiskColor = (riskScore: number) => {
  if (riskScore >= 0.8) return 'text-red-600';
  if (riskScore >= 0.6) return 'text-orange-600';
  if (riskScore >= 0.4) return 'text-yellow-600';
  return 'text-green-600';
};

export function UserDetailModal({
  user,
  isOpen,
  onClose,
  onUserUpdate,
  onPasswordReset,
  onAccountSuspension,
  onAccountReactivation
}: UserDetailModalProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editedUser, setEditedUser] = useState<Partial<UserAnalytics>>({});
  const [suspensionReason, setSuspensionReason] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  if (!user) return null;

  const handleEdit = () => {
    setIsEditing(true);
    setEditedUser({
      name: user.name,
      email: user.email,
      role: user.role,
      status: user.status,
    });
  };

  const handleSave = async () => {
    if (!onUserUpdate) return;
    
    setIsLoading(true);
    try {
      await onUserUpdate(user.userId, editedUser);
      setIsEditing(false);
      toast.success('User updated successfully');
    } catch (error) {
      toast.error('Failed to update user');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditedUser({});
  };

  const handlePasswordReset = async () => {
    if (!onPasswordReset) return;
    
    setIsLoading(true);
    try {
      await onPasswordReset(user.userId);
      toast.success('Password reset email sent');
    } catch (error) {
      toast.error('Failed to reset password');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSuspension = async () => {
    if (!onAccountSuspension || !suspensionReason.trim()) return;
    
    setIsLoading(true);
    try {
      await onAccountSuspension(user.userId, suspensionReason);
      setSuspensionReason('');
      toast.success('Account suspended successfully');
    } catch (error) {
      toast.error('Failed to suspend account');
    } finally {
      setIsLoading(false);
    }
  };

  const handleReactivation = async () => {
    if (!onAccountReactivation) return;
    
    setIsLoading(true);
    try {
      await onAccountReactivation(user.userId);
      toast.success('Account reactivated successfully');
    } catch (error) {
      toast.error('Failed to reactivate account');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                <User className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <DialogTitle className="text-xl font-semibold">
                  {isEditing ? (
                    <Input
                      value={editedUser.name || ''}
                      onChange={(e) => setEditedUser({ ...editedUser, name: e.target.value })}
                      className="text-xl font-semibold"
                    />
                  ) : (
                    user.name || 'Unknown User'
                  )}
                </DialogTitle>
                <div className="flex items-center space-x-2 mt-1">
                  <Badge className={getStatusColor(user.status || 'inactive')}>
                    {(user.status || 'inactive').charAt(0).toUpperCase() + (user.status || 'inactive').slice(1)}
                  </Badge>
                  {user.role && user.role !== 'user' && (
                    <Badge variant="outline">
                      {user.role}
                    </Badge>
                  )}
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              {isEditing ? (
                <>
                  <Button onClick={handleSave} disabled={isLoading} size="sm">
                    <Save className="h-4 w-4 mr-2" />
                    Save
                  </Button>
                  <Button onClick={handleCancel} variant="outline" size="sm">
                    <X className="h-4 w-4 mr-2" />
                    Cancel
                  </Button>
                </>
              ) : (
                <Button onClick={handleEdit} variant="outline" size="sm">
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Button>
              )}
            </div>
          </div>
        </DialogHeader>

        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="activity">Activity</TabsTrigger>
            <TabsTrigger value="orders">Orders</TabsTrigger>
            <TabsTrigger value="admin">Admin Actions</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Personal Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <User className="h-5 w-5" />
                    <span>Personal Information</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label>Email</Label>
                    {isEditing ? (
                      <Input
                        type="email"
                        value={editedUser.email || ''}
                        onChange={(e) => setEditedUser({ ...editedUser, email: e.target.value })}
                      />
                    ) : (
                      <div className="flex items-center space-x-2">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <span>{user.email || 'No email'}</span>
                      </div>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Role</Label>
                    {isEditing ? (
                      <Select
                        value={editedUser.role || ''}
                        onValueChange={(value) => setEditedUser({ ...editedUser, role: value })}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select role" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="user">User</SelectItem>
                          <SelectItem value="admin">Admin</SelectItem>
                          <SelectItem value="moderator">Moderator</SelectItem>
                        </SelectContent>
                      </Select>
                    ) : (
                      <div className="flex items-center space-x-2">
                        <Shield className="h-4 w-4 text-muted-foreground" />
                        <span>{user.role || 'User'}</span>
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label>Registration Date</Label>
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span>{formatDate(user.registrationDate)}</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Last Login</Label>
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span>{formatDate(user.lastLoginDate || '')}</span>
                    </div>
                  </div>

                  {user.groupName && (
                    <div className="space-y-2">
                      <Label>Group</Label>
                      <div className="flex items-center space-x-2">
                        <Users className="h-4 w-4 text-muted-foreground" />
                        <span>{user.groupName}</span>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Analytics Summary */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <TrendingUp className="h-5 w-5" />
                    <span>Analytics Summary</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 border rounded-lg">
                      <ShoppingCart className="h-6 w-6 mx-auto mb-2 text-blue-500" />
                      <div className="text-2xl font-bold">{user.totalOrders || 0}</div>
                      <div className="text-sm text-muted-foreground">Total Orders</div>
                    </div>
                    
                    <div className="text-center p-3 border rounded-lg">
                      <DollarSign className="h-6 w-6 mx-auto mb-2 text-green-500" />
                      <div className="text-2xl font-bold">{formatCurrency(user.totalSpent || 0)}</div>
                      <div className="text-sm text-muted-foreground">Total Spent</div>
                    </div>
                    
                    <div className="text-center p-3 border rounded-lg">
                      <Activity className="h-6 w-6 mx-auto mb-2 text-purple-500" />
                      <div className="text-2xl font-bold">{user.activityScore?.toFixed(1) || '0.0'}</div>
                      <div className="text-sm text-muted-foreground">Activity Score</div>
                    </div>
                    
                    <div className="text-center p-3 border rounded-lg">
                      <AlertTriangle className={`h-6 w-6 mx-auto mb-2 ${getRiskColor(user.riskScore || 0)}`} />
                      <div className={`text-2xl font-bold ${getRiskColor(user.riskScore || 0)}`}>
                        {formatPercentage(user.riskScore || 0)}
                      </div>
                      <div className="text-sm text-muted-foreground">Risk Score</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Additional Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Lifetime Value</p>
                      <p className="text-2xl font-bold">{formatCurrency(user.lifetimeValue || 0)}</p>
                    </div>
                    <DollarSign className="h-8 w-8 text-green-500" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Avg Order Value</p>
                      <p className="text-2xl font-bold">{formatCurrency(user.averageOrderValue || 0)}</p>
                    </div>
                    <ShoppingCart className="h-8 w-8 text-blue-500" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Churn Probability</p>
                      <p className={`text-2xl font-bold ${getRiskColor(user.churnProbability || 0)}`}>
                        {formatPercentage(user.churnProbability || 0)}
                      </p>
                    </div>
                    <AlertTriangle className={`h-8 w-8 ${getRiskColor(user.churnProbability || 0)}`} />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Behavioral Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Target className="h-5 w-5" />
                  <span>Behavioral Profile</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium">Engagement Level</Label>
                    <Badge className="mt-1">
                      {(user.engagementLevel || 'inactive').charAt(0).toUpperCase() + (user.engagementLevel || 'inactive').slice(1)}
                    </Badge>
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium">Behavior Segment</Label>
                    <Badge variant="outline" className="mt-1">
                      {user.behaviorSegment || 'Unassigned'}
                    </Badge>
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium">Acquisition Channel</Label>
                    <span className="text-sm">{user.acquisitionChannel || 'Unknown'}</span>
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium">Groups Joined</Label>
                    <span className="text-sm">{user.groupsJoined || 0}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Activity Tab */}
          <TabsContent value="activity" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center space-x-3 p-3 border rounded-lg">
                    <Calendar className="h-5 w-5 text-blue-500" />
                    <div>
                      <p className="font-medium">Account Created</p>
                      <p className="text-sm text-muted-foreground">{formatDate(user.registrationDate)}</p>
                    </div>
                  </div>
                  
                  {user.lastLoginDate && (
                    <div className="flex items-center space-x-3 p-3 border rounded-lg">
                      <Activity className="h-5 w-5 text-green-500" />
                      <div>
                        <p className="font-medium">Last Login</p>
                        <p className="text-sm text-muted-foreground">{formatDate(user.lastLoginDate)}</p>
                      </div>
                    </div>
                  )}
                  
                  {user.lastOrderDate && (
                    <div className="flex items-center space-x-3 p-3 border rounded-lg">
                      <ShoppingCart className="h-5 w-5 text-purple-500" />
                      <div>
                        <p className="font-medium">Last Order</p>
                        <p className="text-sm text-muted-foreground">{formatDate(user.lastOrderDate)}</p>
                      </div>
                    </div>
                  )}
                  
                  {user.firstOrderDate && (
                    <div className="flex items-center space-x-3 p-3 border rounded-lg">
                      <ShoppingCart className="h-5 w-5 text-orange-500" />
                      <div>
                        <p className="font-medium">First Order</p>
                        <p className="text-sm text-muted-foreground">{formatDate(user.firstOrderDate)}</p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Orders Tab */}
          <TabsContent value="orders" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Order History</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  <ShoppingCart className="h-12 w-12 mx-auto mb-4" />
                  <p>Order history will be displayed here</p>
                  <p className="text-sm">Integration with order management system required</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Admin Actions Tab */}
          <TabsContent value="admin" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Account Management */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Shield className="h-5 w-5" />
                    <span>Account Management</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="outline" className="w-full">
                        <Key className="h-4 w-4 mr-2" />
                        Reset Password
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Reset User Password</AlertDialogTitle>
                        <AlertDialogDescription>
                          This will send a password reset email to {user.email}. The user will be able to set a new password using the link in the email.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={handlePasswordReset} disabled={isLoading}>
                          Send Reset Email
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>

                  {user.status !== 'suspended' ? (
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="destructive" className="w-full">
                          <Ban className="h-4 w-4 mr-2" />
                          Suspend Account
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Suspend User Account</AlertDialogTitle>
                          <AlertDialogDescription>
                            This will suspend the user's account and prevent them from logging in. Please provide a reason for the suspension.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <div className="py-4">
                          <Label htmlFor="suspension-reason">Suspension Reason</Label>
                          <Textarea
                            id="suspension-reason"
                            placeholder="Enter reason for suspension..."
                            value={suspensionReason}
                            onChange={(e) => setSuspensionReason(e.target.value)}
                            className="mt-2"
                          />
                        </div>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction 
                            onClick={handleSuspension} 
                            disabled={isLoading || !suspensionReason.trim()}
                            className="bg-red-600 hover:bg-red-700"
                          >
                            Suspend Account
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  ) : (
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="default" className="w-full">
                          <CheckCircle className="h-4 w-4 mr-2" />
                          Reactivate Account
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Reactivate User Account</AlertDialogTitle>
                          <AlertDialogDescription>
                            This will reactivate the user's account and allow them to log in again.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction onClick={handleReactivation} disabled={isLoading}>
                            Reactivate Account
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  )}
                </CardContent>
              </Card>

              {/* Status Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Account Status</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Current Status</span>
                    <Badge className={getStatusColor(user.status || 'inactive')}>
                      {(user.status || 'inactive').charAt(0).toUpperCase() + (user.status || 'inactive').slice(1)}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Account Type</span>
                    <Badge variant="outline">
                      {user.role || 'User'}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Risk Level</span>
                    <Badge className={getRiskColor(user.riskScore || 0) === 'text-red-600' ? 'bg-red-100 text-red-800' : 
                                     getRiskColor(user.riskScore || 0) === 'text-orange-600' ? 'bg-orange-100 text-orange-800' :
                                     getRiskColor(user.riskScore || 0) === 'text-yellow-600' ? 'bg-yellow-100 text-yellow-800' :
                                     'bg-green-100 text-green-800'}>
                      {(user.riskScore || 0) >= 0.8 ? 'High' : 
                       (user.riskScore || 0) >= 0.6 ? 'Medium' : 
                       (user.riskScore || 0) >= 0.4 ? 'Low' : 'Very Low'}
                    </Badge>
                  </div>
                  
                  <Separator />
                  
                  <div className="text-sm text-muted-foreground">
                    <p><strong>User ID:</strong> {user.userId}</p>
                    <p><strong>Registration:</strong> {formatDate(user.registrationDate)}</p>
                    {user.lastLoginDate && (
                      <p><strong>Last Login:</strong> {formatDate(user.lastLoginDate)}</p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
