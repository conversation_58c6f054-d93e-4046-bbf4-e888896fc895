// components/admin/users/EnhancedUserDashboard.tsx
"use client"

import { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Users,
  TrendingUp,
  TrendingDown,
  Search,
  Filter,
  Download,
  UserPlus,
  AlertTriangle,
  Star,
  Activity,
  DollarSign,
  ShoppingCart,
  Calendar,
  Mail,
  Phone,
  MapPin,
  Eye,
  Edit,
  MoreHorizontal,
  RefreshCw,
  Target,
  Zap,
  BarChart3,
  PieChart,
  ArrowUpDown,
  Key,
  Ban,
  CheckCircle
} from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart as RechartsPieChart, Pie, Cell, BarChart, Bar } from 'recharts';
import {
  useGetUserAnalyticsQuery,
  useGetUserSegmentsQuery,
  useRefreshAnalyticsMutation,
  useExportUserAnalyticsMutation,
  useUpdateUserMutation,
  useResetUserPasswordMutation,
  useSuspendUserAccountMutation,
  useReactivateUserAccountMutation,
  UserAnalytics
} from '@/lib/redux/features/users/usersApiSlice';
import { toast } from 'sonner';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { UserDetailModal } from './UserDetailModal';

// Helper functions
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-ZA', {
    style: 'currency',
    currency: 'ZAR',
    minimumFractionDigits: 2,
  }).format(amount);
};

const formatDate = (date: string | Date) => {
  if (!date) return 'Never';
  return new Intl.DateTimeFormat('en-ZA', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }).format(new Date(date));
};

const formatPercentage = (value: number) => {
  return `${(value * 100).toFixed(1)}%`;
};

const getEngagementColor = (level: string) => {
  switch (level) {
    case 'high': return 'bg-green-100 text-green-800 border-green-200';
    case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'low': return 'bg-orange-100 text-orange-800 border-orange-200';
    case 'inactive': return 'bg-red-100 text-red-800 border-red-200';
    default: return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const getRiskColor = (riskScore: number) => {
  if (riskScore >= 0.8) return 'bg-red-100 text-red-800 border-red-200';
  if (riskScore >= 0.6) return 'bg-orange-100 text-orange-800 border-orange-200';
  if (riskScore >= 0.4) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
  return 'bg-green-100 text-green-800 border-green-200';
};

const getStatusColor = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'active': return 'bg-green-100 text-green-800 border-green-200';
    case 'inactive': return 'bg-gray-100 text-gray-800 border-gray-200';
    case 'suspended': return 'bg-red-100 text-red-800 border-red-200';
    case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    default: return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

export function EnhancedUserDashboard() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSegment, setSelectedSegment] = useState('all');
  const [selectedUser, setSelectedUser] = useState<UserAnalytics | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [sortBy, setSortBy] = useState('lifetimeValue');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [isUserDetailModalOpen, setIsUserDetailModalOpen] = useState(false);
  const [userDetailModalUser, setUserDetailModalUser] = useState<UserAnalytics | null>(null);

  // Fetch data with RTK Query
  const {
    data: analyticsData,
    isLoading: analyticsLoading,
    isError: analyticsError,
    error: analyticsErrorDetails,
    refetch: refetchAnalytics
  } = useGetUserAnalyticsQuery({
    includeChurnPrediction: true,
    includeCLV: true
  });

  const {
    data: segmentsData,
    isLoading: segmentsLoading,
    isError: segmentsError,
    refetch: refetchSegments
  } = useGetUserSegmentsQuery();

  const [refreshAnalytics] = useRefreshAnalyticsMutation();
  const [exportAnalytics] = useExportUserAnalyticsMutation();
  const [updateUser] = useUpdateUserMutation();
  const [resetUserPassword] = useResetUserPasswordMutation();
  const [suspendUserAccount] = useSuspendUserAccountMutation();
  const [reactivateUserAccount] = useReactivateUserAccountMutation();

  const users = analyticsData?.data || [];
  const segments = segmentsData?.data || [];
  const summary = analyticsData?.summary || {
    totalUsers: 0,
    activeUsers: 0,
    highValueUsers: 0,
    atRiskUsers: 0,
    averageLifetimeValue: 0,
    averageActivityScore: 0,
    engagementDistribution: { high: 0, medium: 0, low: 0, inactive: 0 },
    totalRevenue: 0
  };

  const isLoading = analyticsLoading || segmentsLoading;
  const hasError = analyticsError || segmentsError;

  // Debug logging
  console.log('Enhanced User Dashboard Debug:', {
    analyticsData,
    segmentsData,
    users: users.length,
    segments: segments.length,
    isLoading,
    hasError,
    analyticsError,
    segmentsError
  });

  // Filter and sort users
  const filteredAndSortedUsers = useMemo(() => {
    let filtered = users.filter(user => {
      const matchesSearch = (user.name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                           (user.email || '').toLowerCase().includes(searchTerm.toLowerCase());
      const matchesSegment = selectedSegment === 'all' || (user.behaviorSegment || '') === selectedSegment;
      return matchesSearch && matchesSegment;
    });

    // Sort users
    filtered.sort((a, b) => {
      let aValue, bValue;

      switch (sortBy) {
        case 'lifetimeValue':
          aValue = a.lifetimeValue || 0;
          bValue = b.lifetimeValue || 0;
          break;
        case 'totalSpent':
          aValue = a.totalSpent || 0;
          bValue = b.totalSpent || 0;
          break;
        case 'totalOrders':
          aValue = a.totalOrders || 0;
          bValue = b.totalOrders || 0;
          break;
        case 'churnProbability':
          aValue = a.churnProbability || 0;
          bValue = b.churnProbability || 0;
          break;
        case 'activityScore':
          aValue = a.activityScore || 0;
          bValue = b.activityScore || 0;
          break;
        case 'registrationDate':
          aValue = a.registrationDate ? new Date(a.registrationDate).getTime() : 0;
          bValue = b.registrationDate ? new Date(b.registrationDate).getTime() : 0;
          break;
        case 'name':
          aValue = a.name || '';
          bValue = b.name || '';
          break;
        default:
          return 0;
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortOrder === 'asc'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
      }

      return 0;
    });

    return filtered;
  }, [users, searchTerm, selectedSegment, sortBy, sortOrder]);

  // Handle sorting
  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('desc');
    }
  };

  // Handle refresh
  const handleRefresh = async () => {
    try {
      await refreshAnalytics().unwrap();
      await refetchAnalytics();
      await refetchSegments();
      toast.success('Analytics refreshed successfully');
    } catch (error) {
      toast.error('Failed to refresh analytics');
    }
  };

  // Handle export
  const handleExport = async (format: string) => {
    try {
      await exportAnalytics({ format }).unwrap();
      toast.success(`Analytics exported as ${format.toUpperCase()}`);
    } catch (error) {
      toast.error('Failed to export analytics');
    }
  };

  // Handle user detail modal
  const handleViewUserDetails = (user: UserAnalytics) => {
    setUserDetailModalUser(user);
    setIsUserDetailModalOpen(true);
  };

  const handleCloseUserDetailModal = () => {
    setIsUserDetailModalOpen(false);
    setUserDetailModalUser(null);
  };

  // Handle user updates
  const handleUserUpdate = async (userId: string, updates: any) => {
    try {
      await updateUser({ userId, ...updates }).unwrap();
      await refetchAnalytics();
      toast.success('User updated successfully');
    } catch (error) {
      toast.error('Failed to update user');
      throw error;
    }
  };

  // Handle password reset
  const handlePasswordReset = async (userId: string) => {
    try {
      await resetUserPassword(userId).unwrap();
      toast.success('Password reset email sent');
    } catch (error) {
      toast.error('Failed to send password reset email');
      throw error;
    }
  };

  // Handle account suspension
  const handleAccountSuspension = async (userId: string, reason: string) => {
    try {
      await suspendUserAccount({ userId, reason }).unwrap();
      await refetchAnalytics();
      toast.success('Account suspended successfully');
    } catch (error) {
      toast.error('Failed to suspend account');
      throw error;
    }
  };

  // Handle account reactivation
  const handleAccountReactivation = async (userId: string) => {
    try {
      await reactivateUserAccount(userId).unwrap();
      await refetchAnalytics();
      toast.success('Account reactivated successfully');
    } catch (error) {
      toast.error('Failed to reactivate account');
      throw error;
    }
  };

  // Generate chart data for user segments
  const segmentChartData = useMemo(() => {
    if (!segments || segments.length === 0) return [];

    return segments.map(segment => ({
      name: segment.name || 'Unknown Segment',
      users: segment.userCount || 0,
      value: segment.averageValue || 0
    }));
  }, [segments]);

  // Generate engagement distribution data
  const engagementData = useMemo(() => {
    if (!summary?.engagementDistribution && users.length === 0) return [];

    // If we have users but no summary, calculate from users data
    if (!summary?.engagementDistribution && users.length > 0) {
      const distribution = users.reduce((acc, user) => {
        const level = user.engagementLevel || 'inactive';
        acc[level] = (acc[level] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      return [
        { name: 'High', value: distribution.high || 0, color: '#10b981' },
        { name: 'Medium', value: distribution.medium || 0, color: '#f59e0b' },
        { name: 'Low', value: distribution.low || 0, color: '#f97316' },
        { name: 'Inactive', value: distribution.inactive || 0, color: '#ef4444' }
      ];
    }

    return [
      { name: 'High', value: summary?.engagementDistribution?.high || 0, color: '#10b981' },
      { name: 'Medium', value: summary?.engagementDistribution?.medium || 0, color: '#f59e0b' },
      { name: 'Low', value: summary?.engagementDistribution?.low || 0, color: '#f97316' },
      { name: 'Inactive', value: summary?.engagementDistribution?.inactive || 0, color: '#ef4444' }
    ];
  }, [summary, users]);

  // Generate user activity trend data
  const activityTrendData = useMemo(() => {
    const last30Days = Array.from({ length: 30 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (29 - i));
      return {
        date: date.toISOString().split('T')[0],
        activeUsers: Math.floor(Math.random() * 100) + 50, // Mock data
        newUsers: Math.floor(Math.random() * 20) + 5,
      };
    });
    return last30Days;
  }, []);

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <Skeleton className="h-8 w-64 mb-2" />
            <Skeleton className="h-4 w-96" />
          </div>
          <div className="flex gap-2">
            <Skeleton className="h-10 w-24" />
            <Skeleton className="h-10 w-32" />
          </div>
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-4" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-20 mb-2" />
                <Skeleton className="h-3 w-32" />
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-64 w-full" />
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-64 w-full" />
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // Error state
  if (hasError) {
    return (
      <div className="flex items-center justify-center h-96">
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center py-8">
            <AlertTriangle className="h-12 w-12 text-red-500 mb-4" />
            <h3 className="text-lg font-semibold text-red-700 mb-2">Failed to Load User Data</h3>
            <p className="text-sm text-muted-foreground text-center mb-4">
              {analyticsErrorDetails && 'data' in analyticsErrorDetails
                ? (analyticsErrorDetails.data as any)?.message || 'An error occurred'
                : 'Network error'}
            </p>
            <Button onClick={() => { refetchAnalytics(); refetchSegments(); }} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Enhanced User Management</h1>
          <p className="text-muted-foreground">
            Advanced user analytics, segmentation, and customer lifecycle management
          </p>
        </div>

        <div className="flex items-center gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => handleExport('csv')}>
                Export as CSV
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleExport('xlsx')}>
                Export as Excel
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleExport('pdf')}>
                Export as PDF
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Button onClick={handleRefresh} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4" />
          </Button>

          <Button>
            <UserPlus className="h-4 w-4 mr-2" />
            Add User
          </Button>
        </div>
      </div>

      {/* Overview Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summary?.totalUsers?.toLocaleString() || '0'}</div>
            <p className="text-xs text-muted-foreground">
              All registered users
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summary?.activeUsers?.toLocaleString() || '0'}</div>
            <p className="text-xs text-muted-foreground">
              Engaged users
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">High Value</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summary?.highValueUsers?.toLocaleString() || '0'}</div>
            <p className="text-xs text-muted-foreground">
              Premium customers
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">At Risk</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{summary?.atRiskUsers?.toLocaleString() || '0'}</div>
            <p className="text-xs text-muted-foreground">
              Churn risk
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg LTV</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(summary?.averageLifetimeValue || 0)}</div>
            <p className="text-xs text-muted-foreground">
              Lifetime value
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(summary?.totalRevenue || 0)}</div>
            <p className="text-xs text-muted-foreground">
              All time revenue
            </p>
          </CardContent>
        </Card>
      </div>
      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">
            <BarChart3 className="h-4 w-4 mr-2" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="users">
            <Users className="h-4 w-4 mr-2" />
            User List
          </TabsTrigger>
          <TabsTrigger value="segments">
            <Target className="h-4 w-4 mr-2" />
            Segments
          </TabsTrigger>
          <TabsTrigger value="analytics">
            <Zap className="h-4 w-4 mr-2" />
            Analytics
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>User Segments</CardTitle>
                <CardDescription>Distribution of users across behavior segments</CardDescription>
              </CardHeader>
              <CardContent>
                {segmentChartData.length > 0 ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={segmentChartData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip formatter={(value, name) => [value, name === 'users' ? 'Users' : 'Avg Value']} />
                      <Bar dataKey="users" fill="#3b82f6" />
                    </BarChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="flex items-center justify-center h-[300px] text-muted-foreground">
                    No segment data available
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Engagement Levels</CardTitle>
                <CardDescription>User engagement distribution</CardDescription>
              </CardHeader>
              <CardContent>
                {engagementData.length > 0 ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <RechartsPieChart>
                      <Pie
                        data={engagementData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {engagementData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="flex items-center justify-center h-[300px] text-muted-foreground">
                    No engagement data available
                  </div>
                )}
              </CardContent>
            </Card>

            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>User Activity Trends</CardTitle>
                <CardDescription>Daily active and new user trends over the last 30 days</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={activityTrendData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="activeUsers" stroke="#3b82f6" strokeWidth={2} name="Active Users" />
                    <Line type="monotone" dataKey="newUsers" stroke="#10b981" strokeWidth={2} name="New Users" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Users Tab */}
        <TabsContent value="users" className="space-y-6">
          {/* Search and Filter */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search users by name or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={selectedSegment} onValueChange={setSelectedSegment}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by segment" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Segments</SelectItem>
                {segments.map(segment => (
                  <SelectItem key={segment.id} value={segment.name}>
                    {segment.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Users Table */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Users ({filteredAndSortedUsers.length})</CardTitle>
                  <CardDescription>Detailed user information and analytics</CardDescription>
                </div>
                <div className="text-sm text-muted-foreground">
                  Showing {Math.min(20, filteredAndSortedUsers.length)} of {filteredAndSortedUsers.length} users
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>
                      <Button variant="ghost" onClick={() => handleSort('name')} className="h-auto p-0">
                        User
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      </Button>
                    </TableHead>
                    <TableHead>
                      <Button variant="ghost" onClick={() => handleSort('registrationDate')} className="h-auto p-0">
                        Joined
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      </Button>
                    </TableHead>
                    <TableHead>Engagement</TableHead>
                    <TableHead>
                      <Button variant="ghost" onClick={() => handleSort('totalOrders')} className="h-auto p-0">
                        Orders
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      </Button>
                    </TableHead>
                    <TableHead>
                      <Button variant="ghost" onClick={() => handleSort('lifetimeValue')} className="h-auto p-0">
                        Lifetime Value
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      </Button>
                    </TableHead>
                    <TableHead>
                      <Button variant="ghost" onClick={() => handleSort('churnProbability')} className="h-auto p-0">
                        Risk Score
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      </Button>
                    </TableHead>
                    <TableHead>Segment</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAndSortedUsers.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8">
                        <div className="flex flex-col items-center space-y-2">
                          <Users className="h-8 w-8 text-muted-foreground" />
                          <p className="text-muted-foreground">No users found</p>
                          {(searchTerm || selectedSegment !== 'all') && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setSearchTerm("");
                                setSelectedSegment("all");
                              }}
                            >
                              Clear filters
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredAndSortedUsers.slice(0, 20).map((user) => (
                      <TableRow key={user.userId} className="hover:bg-muted/50">
                        <TableCell>
                          <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
                              {(user.name || 'U').charAt(0).toUpperCase()}
                            </div>
                            <div className="space-y-1">
                              <div className="font-medium text-gray-900">{user.name || 'Unknown User'}</div>
                              <div className="text-sm text-muted-foreground">{user.email || 'No email'}</div>
                              <div className="flex items-center space-x-2">
                                {user.role && user.role !== 'user' && user.role !== 'customer' && (
                                  <Badge variant="outline" className="text-xs">
                                    {user.role}
                                  </Badge>
                                )}
                                <Badge className={getStatusColor(user.status || 'inactive')} variant="outline">
                                  {(user.status || 'inactive').charAt(0).toUpperCase() + (user.status || 'inactive').slice(1)}
                                </Badge>
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm">
                              {formatDate(user.registrationDate)}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getEngagementColor(user.engagementLevel || 'inactive')}>
                            {(user.engagementLevel || 'inactive').charAt(0).toUpperCase() + (user.engagementLevel || 'inactive').slice(1)}
                          </Badge>
                          <div className="text-xs text-muted-foreground mt-1">
                            Score: {user.activityScore?.toFixed(1) || '0.0'}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
                            <span className="font-medium">{user.totalOrders || 0}</span>
                          </div>
                          <div className="text-xs text-muted-foreground">
                            Avg: {formatCurrency(user.averageOrderValue || 0)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="font-medium">
                            {formatCurrency(user.lifetimeValue || 0)}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            Total: {formatCurrency(user.totalSpent || 0)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <Badge className={getRiskColor(user.riskScore || 0)}>
                              {formatPercentage(user.riskScore || 0)}
                            </Badge>
                            <div className="text-xs text-muted-foreground">
                              Churn: {formatPercentage(user.churnProbability || 0)}
                            </div>
                            <div className="flex items-center space-x-1">
                              <div className={`w-2 h-2 rounded-full ${
                                (user.riskScore || 0) >= 0.8 ? 'bg-red-500' :
                                (user.riskScore || 0) >= 0.6 ? 'bg-orange-500' :
                                (user.riskScore || 0) >= 0.4 ? 'bg-yellow-500' : 'bg-green-500'
                              }`}></div>
                              <span className="text-xs text-muted-foreground">
                                {(user.riskScore || 0) >= 0.8 ? 'High Risk' :
                                 (user.riskScore || 0) >= 0.6 ? 'Medium Risk' :
                                 (user.riskScore || 0) >= 0.4 ? 'Low Risk' : 'Very Low Risk'}
                              </span>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{user.behaviorSegment || 'Unassigned'}</Badge>
                          {user.acquisitionChannel && (
                            <div className="text-xs text-muted-foreground mt-1">
                              via {user.acquisitionChannel}
                            </div>
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>User Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => handleViewUserDetails(user)}>
                                <Eye className="h-4 w-4 mr-2" />
                                View Full Details
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleViewUserDetails(user)}>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit User
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem onClick={() => handlePasswordReset(user.userId)}>
                                <Key className="h-4 w-4 mr-2" />
                                Reset Password
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Mail className="h-4 w-4 mr-2" />
                                Send Email
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              {user.status !== 'suspended' ? (
                                <DropdownMenuItem
                                  onClick={() => handleAccountSuspension(user.userId, 'Suspended by admin')}
                                  className="text-red-600"
                                >
                                  <Ban className="h-4 w-4 mr-2" />
                                  Suspend Account
                                </DropdownMenuItem>
                              ) : (
                                <DropdownMenuItem
                                  onClick={() => handleAccountReactivation(user.userId)}
                                  className="text-green-600"
                                >
                                  <CheckCircle className="h-4 w-4 mr-2" />
                                  Reactivate Account
                                </DropdownMenuItem>
                              )}
                              <DropdownMenuItem>
                                <Target className="h-4 w-4 mr-2" />
                                Add to Segment
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Segments Tab */}
        <TabsContent value="segments" className="space-y-6">
          <div className="flex justify-between items-center">
            <div>
              <h3 className="text-lg font-semibold">User Segments</h3>
              <p className="text-sm text-muted-foreground">
                Behavioral segments based on user activity and engagement patterns
              </p>
            </div>
            <Button>
              <Target className="h-4 w-4 mr-2" />
              Create Segment
            </Button>
          </div>

          <div className="grid gap-6">
            {segments.length === 0 ? (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <Target className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No Segments Found</h3>
                  <p className="text-sm text-muted-foreground text-center mb-4">
                    Create user segments to better understand and target your customer base
                  </p>
                  <Button>
                    <Target className="h-4 w-4 mr-2" />
                    Create Your First Segment
                  </Button>
                </CardContent>
              </Card>
            ) : (
              segments.map((segment) => (
                <Card key={segment.id}>
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="flex items-center gap-2">
                          {segment.name}
                          <Badge variant="outline" className="text-xs">
                            {segment.userCount} users
                          </Badge>
                        </CardTitle>
                        <CardDescription>{segment.description}</CardDescription>
                      </div>
                      <Badge variant={segment.growthRate > 0 ? 'default' : 'destructive'}>
                        {segment.growthRate > 0 ? '+' : ''}{segment.growthRate.toFixed(1)}%
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">{segment.userCount.toLocaleString()}</div>
                        <div className="text-sm text-muted-foreground">Users</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">{formatCurrency(segment.averageValue)}</div>
                        <div className="text-sm text-muted-foreground">Avg Value</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-600">{segment.characteristics.retentionRate.toFixed(1)}%</div>
                        <div className="text-sm text-muted-foreground">Retention</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-orange-600">{segment.characteristics.conversionRate.toFixed(1)}%</div>
                        <div className="text-sm text-muted-foreground">Conversion</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-indigo-600">{formatCurrency(segment.characteristics.averageLifetimeValue)}</div>
                        <div className="text-sm text-muted-foreground">Avg LTV</div>
                      </div>
                    </div>

                    <div className="mt-4 pt-4 border-t">
                      <div className="flex items-center justify-between">
                        <div className="text-sm text-muted-foreground">
                          Top Categories: {segment.characteristics.mostPopularCategories.slice(0, 3).join(', ')}
                        </div>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4 mr-2" />
                            View Users
                          </Button>
                          <Button variant="outline" size="sm">
                            <Edit className="h-4 w-4 mr-2" />
                            Edit Segment
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          <div className="grid gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Advanced Analytics</CardTitle>
                <CardDescription>
                  Deep insights into user behavior, churn prediction, and lifetime value analysis
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center p-6 border rounded-lg">
                    <Activity className="h-8 w-8 mx-auto mb-2 text-blue-500" />
                    <div className="text-2xl font-bold">{summary?.averageActivityScore?.toFixed(1) || '0.0'}</div>
                    <div className="text-sm text-muted-foreground">Avg Activity Score</div>
                  </div>
                  <div className="text-center p-6 border rounded-lg">
                    <AlertTriangle className="h-8 w-8 mx-auto mb-2 text-red-500" />
                    <div className="text-2xl font-bold">{summary?.atRiskUsers || 0}</div>
                    <div className="text-sm text-muted-foreground">High Churn Risk</div>
                  </div>
                  <div className="text-center p-6 border rounded-lg">
                    <Star className="h-8 w-8 mx-auto mb-2 text-yellow-500" />
                    <div className="text-2xl font-bold">{summary?.highValueUsers || 0}</div>
                    <div className="text-sm text-muted-foreground">High Value Users</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Churn Risk Distribution</CardTitle>
                  <CardDescription>Users categorized by churn probability</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Low Risk (0-30%)</span>
                      <Badge className="bg-green-100 text-green-800">
                        {users.filter(u => (u.churnProbability || 0) <= 0.3).length}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Medium Risk (30-60%)</span>
                      <Badge className="bg-yellow-100 text-yellow-800">
                        {users.filter(u => (u.churnProbability || 0) > 0.3 && (u.churnProbability || 0) <= 0.6).length}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">High Risk (60%+)</span>
                      <Badge className="bg-red-100 text-red-800">
                        {users.filter(u => (u.churnProbability || 0) > 0.6).length}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Value Distribution</CardTitle>
                  <CardDescription>Users categorized by lifetime value</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">High Value (R10k+)</span>
                      <Badge className="bg-green-100 text-green-800">
                        {users.filter(u => (u.lifetimeValue || 0) >= 10000).length}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Medium Value (R5k-R10k)</span>
                      <Badge className="bg-yellow-100 text-yellow-800">
                        {users.filter(u => (u.lifetimeValue || 0) >= 5000 && (u.lifetimeValue || 0) < 10000).length}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Low Value (&lt;R5k)</span>
                      <Badge className="bg-gray-100 text-gray-800">
                        {users.filter(u => (u.lifetimeValue || 0) < 5000).length}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      {/* User Detail Modal */}
      <UserDetailModal
        user={userDetailModalUser}
        isOpen={isUserDetailModalOpen}
        onClose={handleCloseUserDetailModal}
        onUserUpdate={handleUserUpdate}
        onPasswordReset={handlePasswordReset}
        onAccountSuspension={handleAccountSuspension}
        onAccountReactivation={handleAccountReactivation}
      />
    </div>
  );
}


