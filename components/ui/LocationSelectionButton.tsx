// components/ui/LocationSelectionButton.tsx

"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { MapPin, ChevronDown, Check } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { LocationSelectionModal, type LocationSelectionResult, type GroupRequestData } from "@/components/modals/LocationSelectionModal";

interface LocationSelectionButtonProps {
  value?: LocationSelectionResult | null;
  onLocationSelect: (result: LocationSelectionResult) => void;
  onGroupRequest?: (data: GroupRequestData & { locationSelection: LocationSelectionResult }) => Promise<void>;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  variant?: "default" | "outline" | "ghost";
  size?: "sm" | "md" | "lg";
  showFullPath?: boolean;
  required?: boolean;
  error?: string;
  userEmail?: string;
  userName?: string;
  userPhone?: string;
}

export function LocationSelectionButton({
  value,
  onLocationSelect,
  onGroupRequest,
  placeholder = "Select your location",
  disabled = false,
  className,
  variant = "outline",
  size = "md",
  showFullPath = true,
  required = false,
  error,
  userEmail,
  userName,
  userPhone
}: LocationSelectionButtonProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleOpenModal = () => {
    if (!disabled) {
      setIsModalOpen(true);
    }
  };

  const handleLocationSelect = (result: LocationSelectionResult) => {
    onLocationSelect(result);
    setIsModalOpen(false);
  };

  const getSizeClasses = () => {
    switch (size) {
      case "sm":
        return "h-10 px-3 text-sm";
      case "lg":
        return "h-14 px-6 text-base";
      default:
        return "h-12 px-4 text-sm";
    }
  };

  const getDisplayText = () => {
    if (!value) return placeholder;
    
    if (showFullPath) {
      return value.fullPath;
    } else {
      return value.locationName;
    }
  };

  const hasValue = !!value;

  return (
    <>
      <div className="space-y-2">
        <motion.div
          whileTap={{ scale: 0.98 }}
          className="relative"
        >
          <Button
            type="button"
            variant={variant}
            onClick={handleOpenModal}
            disabled={disabled}
            className={cn(
              "w-full justify-between text-left font-normal transition-all duration-200",
              getSizeClasses(),
              hasValue 
                ? "text-gray-900 border-gray-300 hover:border-gray-400" 
                : "text-gray-500 border-gray-200 hover:border-gray-300",
              error && "border-red-300 hover:border-red-400 focus:border-red-500 focus:ring-red-200",
              disabled && "opacity-50 cursor-not-allowed",
              className
            )}
          >
            <div className="flex items-center gap-3 flex-1 min-w-0">
              <MapPin className={cn(
                "h-4 w-4 shrink-0",
                hasValue ? "text-blue-500" : "text-gray-400"
              )} />
              
              <div className="flex-1 min-w-0">
                {hasValue ? (
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-gray-900 truncate">
                        {value.locationName}
                      </span>
                      <Check className="h-3 w-3 text-green-500 shrink-0" />
                    </div>
                    {showFullPath && (
                      <div className="text-xs text-gray-500 truncate">
                        {value.townshipName}, {value.cityName}, {value.provinceName}
                      </div>
                    )}
                  </div>
                ) : (
                  <span className="truncate">
                    {placeholder}
                    {required && <span className="text-red-500 ml-1">*</span>}
                  </span>
                )}
              </div>
            </div>

            <ChevronDown className={cn(
              "h-4 w-4 shrink-0 transition-transform duration-200",
              "text-gray-400"
            )} />
          </Button>

          {/* Selection indicator */}
          {hasValue && (
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="absolute -top-1 -right-1 h-3 w-3 bg-green-500 rounded-full border-2 border-white"
            />
          )}
        </motion.div>

        {/* Error message */}
        {error && (
          <motion.p
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-sm text-red-600 flex items-center gap-1"
          >
            <span className="h-1 w-1 bg-red-500 rounded-full" />
            {error}
          </motion.p>
        )}

        {/* Selected location details */}
        {hasValue && showFullPath && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            className="bg-blue-50/50 border border-blue-200/50 rounded-lg p-3"
          >
            <div className="flex items-start gap-3">
              <MapPin className="h-4 w-4 text-blue-500 mt-0.5 shrink-0" />
              <div className="space-y-1 flex-1 min-w-0">
                <h4 className="font-medium text-blue-900 text-sm">
                  Selected Location
                </h4>
                <div className="space-y-1 text-xs text-blue-700">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs border-blue-300 text-blue-700">
                      Location
                    </Badge>
                    <span className="truncate">{value.locationName}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs border-blue-300 text-blue-700">
                      Township
                    </Badge>
                    <span className="truncate">{value.townshipName}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs border-blue-300 text-blue-700">
                      City
                    </Badge>
                    <span className="truncate">{value.cityName}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs border-blue-300 text-blue-700">
                      Province
                    </Badge>
                    <span className="truncate">{value.provinceName}</span>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </div>

      {/* Location Selection Modal */}
      <LocationSelectionModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onLocationSelect={handleLocationSelect}
        onGroupRequest={onGroupRequest}
        initialSelection={value || undefined}
        title="Select Your Location"
        userEmail={userEmail}
        userName={userName}
        userPhone={userPhone}
      />
    </>
  );
}

// Simplified version for basic use cases
export function SimpleLocationButton({
  value,
  onLocationSelect,
  placeholder = "Choose location",
  disabled = false,
  className
}: {
  value?: LocationSelectionResult | null;
  onLocationSelect: (result: LocationSelectionResult) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}) {
  return (
    <LocationSelectionButton
      value={value}
      onLocationSelect={onLocationSelect}
      placeholder={placeholder}
      disabled={disabled}
      className={className}
      showFullPath={false}
      size="sm"
      variant="ghost"
    />
  );
}

// Compact version for forms
export function CompactLocationButton({
  value,
  onLocationSelect,
  placeholder = "Select location",
  error,
  required = false,
  className
}: {
  value?: LocationSelectionResult | null;
  onLocationSelect: (result: LocationSelectionResult) => void;
  placeholder?: string;
  error?: string;
  required?: boolean;
  className?: string;
}) {
  return (
    <LocationSelectionButton
      value={value}
      onLocationSelect={onLocationSelect}
      placeholder={placeholder}
      error={error}
      required={required}
      className={className}
      showFullPath={false}
      size="md"
      variant="outline"
    />
  );
}
