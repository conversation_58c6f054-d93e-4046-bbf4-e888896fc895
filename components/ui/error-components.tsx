// components/ui/error-components.tsx

"use client";

import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { 
  AlertCircle, 
  RefreshCw, 
  Wifi, 
  WifiOff, 
  Database, 
  Shield, 
  FileX, 
  Server,
  Users,
  BarChart3,
  ShoppingCart,
  TrendingUp,
  Inbox,
  Search,
  Settings
} from "lucide-react";
import { motion } from "framer-motion";

// Error state types
export type ErrorState = 
  | 'no-data' 
  | 'loading-error' 
  | 'network-error' 
  | 'auth-error' 
  | 'permission-error' 
  | 'not-found' 
  | 'server-error'
  | 'validation-error';

// Component context types
export type ComponentContext = 
  | 'dashboard' 
  | 'table' 
  | 'chart' 
  | 'form' 
  | 'modal' 
  | 'page' 
  | 'card';

interface BaseErrorProps {
  title?: string;
  message?: string;
  onRetry?: () => void;
  retryLabel?: string;
  isRetrying?: boolean;
  showRetry?: boolean;
  className?: string;
}

interface ErrorComponentProps extends BaseErrorProps {
  errorState: ErrorState;
  context: ComponentContext;
  icon?: React.ComponentType<{ className?: string }>;
  children?: React.ReactNode;
}

// Icon mapping for different error states
const ERROR_ICONS = {
  'no-data': Inbox,
  'loading-error': AlertCircle,
  'network-error': WifiOff,
  'auth-error': Shield,
  'permission-error': Shield,
  'not-found': FileX,
  'server-error': Server,
  'validation-error': AlertCircle
};

// Context-specific icons
const CONTEXT_ICONS = {
  dashboard: BarChart3,
  table: Users,
  chart: TrendingUp,
  form: Settings,
  modal: AlertCircle,
  page: FileX,
  card: Inbox
};

// Default messages for different error states
const ERROR_MESSAGES = {
  'no-data': {
    title: 'No Data Available',
    message: 'There\'s no data to display at the moment.'
  },
  'loading-error': {
    title: 'Loading Failed',
    message: 'We couldn\'t load the data. Please try again.'
  },
  'network-error': {
    title: 'Connection Problem',
    message: 'Please check your internet connection and try again.'
  },
  'auth-error': {
    title: 'Authentication Required',
    message: 'Please log in to access this information.'
  },
  'permission-error': {
    title: 'Access Denied',
    message: 'You don\'t have permission to view this content.'
  },
  'not-found': {
    title: 'Not Found',
    message: 'The requested information could not be found.'
  },
  'server-error': {
    title: 'Server Error',
    message: 'Something went wrong on our end. Please try again.'
  },
  'validation-error': {
    title: 'Invalid Data',
    message: 'Please check your input and try again.'
  }
};

// Main Error Component
export function ErrorComponent({
  errorState,
  context,
  title,
  message,
  onRetry,
  retryLabel = "Try Again",
  isRetrying = false,
  showRetry = true,
  icon,
  className = "",
  children
}: ErrorComponentProps) {
  const defaultMessages = ERROR_MESSAGES[errorState];
  const IconComponent = icon || ERROR_ICONS[errorState] || AlertCircle;
  
  const finalTitle = title || defaultMessages.title;
  const finalMessage = message || defaultMessages.message;

  // Different layouts based on context
  if (context === 'table') {
    return <TableErrorState {...{ errorState, title: finalTitle, message: finalMessage, onRetry, retryLabel, isRetrying, showRetry, className }} />;
  }

  if (context === 'chart') {
    return <ChartErrorState {...{ errorState, title: finalTitle, message: finalMessage, onRetry, retryLabel, isRetrying, showRetry, className }} />;
  }

  if (context === 'modal') {
    return <ModalErrorState {...{ errorState, title: finalTitle, message: finalMessage, onRetry, retryLabel, isRetrying, showRetry, className }} />;
  }

  if (context === 'card') {
    return <CardErrorState {...{ errorState, title: finalTitle, message: finalMessage, onRetry, retryLabel, isRetrying, showRetry, className }} />;
  }

  // Default layout for dashboard and page contexts
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`text-center py-8 ${className}`}
    >
      <div className="flex flex-col items-center space-y-4">
        <div className="p-4 rounded-full bg-gray-100">
          <IconComponent className="h-8 w-8 text-gray-400" />
        </div>
        <div className="space-y-2">
          <h3 className="text-lg font-medium text-gray-900">{finalTitle}</h3>
          <p className="text-gray-500 max-w-md">{finalMessage}</p>
        </div>
        {showRetry && onRetry && (
          <Button 
            onClick={onRetry} 
            disabled={isRetrying}
            variant="outline"
            className="mt-4"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRetrying ? 'animate-spin' : ''}`} />
            {isRetrying ? 'Retrying...' : retryLabel}
          </Button>
        )}
        {children}
      </div>
    </motion.div>
  );
}

// Table-specific error state
export function TableErrorState({
  errorState,
  title,
  message,
  onRetry,
  retryLabel = "Refresh",
  isRetrying = false,
  showRetry = true,
  className = ""
}: BaseErrorProps & { errorState: ErrorState }) {
  const IconComponent = ERROR_ICONS[errorState] || Users;

  return (
    <div className={`text-center py-12 ${className}`}>
      <div className="flex flex-col items-center space-y-4">
        <div className="p-3 rounded-full bg-gray-50">
          <IconComponent className="h-6 w-6 text-gray-400" />
        </div>
        <div className="space-y-1">
          <h4 className="text-sm font-medium text-gray-900">{title}</h4>
          <p className="text-sm text-gray-500">{message}</p>
        </div>
        {showRetry && onRetry && (
          <Button 
            onClick={onRetry} 
            disabled={isRetrying}
            variant="ghost"
            size="sm"
          >
            <RefreshCw className={`h-3 w-3 mr-2 ${isRetrying ? 'animate-spin' : ''}`} />
            {isRetrying ? 'Loading...' : retryLabel}
          </Button>
        )}
      </div>
    </div>
  );
}

// Chart-specific error state
export function ChartErrorState({
  errorState,
  title,
  message,
  onRetry,
  retryLabel = "Reload Chart",
  isRetrying = false,
  showRetry = true,
  className = ""
}: BaseErrorProps & { errorState: ErrorState }) {
  const IconComponent = ERROR_ICONS[errorState] || BarChart3;

  return (
    <div className={`flex items-center justify-center h-64 ${className}`}>
      <div className="text-center space-y-4">
        <div className="p-4 rounded-full bg-gray-50 mx-auto w-fit">
          <IconComponent className="h-8 w-8 text-gray-400" />
        </div>
        <div className="space-y-2">
          <h4 className="text-base font-medium text-gray-900">{title}</h4>
          <p className="text-sm text-gray-500 max-w-xs">{message}</p>
        </div>
        {showRetry && onRetry && (
          <Button 
            onClick={onRetry} 
            disabled={isRetrying}
            variant="outline"
            size="sm"
          >
            <RefreshCw className={`h-3 w-3 mr-2 ${isRetrying ? 'animate-spin' : ''}`} />
            {isRetrying ? 'Loading...' : retryLabel}
          </Button>
        )}
      </div>
    </div>
  );
}

// Modal-specific error state
export function ModalErrorState({
  errorState,
  title,
  message,
  onRetry,
  retryLabel = "Try Again",
  isRetrying = false,
  showRetry = true,
  className = ""
}: BaseErrorProps & { errorState: ErrorState }) {
  return (
    <Alert variant="destructive" className={className}>
      <AlertCircle className="h-4 w-4" />
      <AlertDescription className="flex items-center justify-between">
        <div>
          <div className="font-medium">{title}</div>
          <div className="text-sm mt-1">{message}</div>
        </div>
        {showRetry && onRetry && (
          <Button 
            onClick={onRetry} 
            disabled={isRetrying}
            variant="outline"
            size="sm"
            className="ml-4"
          >
            <RefreshCw className={`h-3 w-3 mr-1 ${isRetrying ? 'animate-spin' : ''}`} />
            {isRetrying ? 'Retrying...' : retryLabel}
          </Button>
        )}
      </AlertDescription>
    </Alert>
  );
}

// Card-specific error state
export function CardErrorState({
  errorState,
  title,
  message,
  onRetry,
  retryLabel = "Refresh",
  isRetrying = false,
  showRetry = true,
  className = ""
}: BaseErrorProps & { errorState: ErrorState }) {
  const IconComponent = ERROR_ICONS[errorState] || Inbox;

  return (
    <Card className={`border-dashed ${className}`}>
      <CardContent className="text-center py-8">
        <div className="flex flex-col items-center space-y-3">
          <div className="p-3 rounded-full bg-gray-50">
            <IconComponent className="h-5 w-5 text-gray-400" />
          </div>
          <div className="space-y-1">
            <h4 className="text-sm font-medium text-gray-900">{title}</h4>
            <p className="text-xs text-gray-500">{message}</p>
          </div>
          {showRetry && onRetry && (
            <Button 
              onClick={onRetry} 
              disabled={isRetrying}
              variant="ghost"
              size="sm"
            >
              <RefreshCw className={`h-3 w-3 mr-2 ${isRetrying ? 'animate-spin' : ''}`} />
              {isRetrying ? 'Loading...' : retryLabel}
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// Connection status indicator
export function ConnectionStatus({ 
  isConnected, 
  isConnecting = false,
  lastUpdated,
  className = "" 
}: {
  isConnected: boolean;
  isConnecting?: boolean;
  lastUpdated?: Date | string;
  className?: string;
}) {
  const getStatusColor = () => {
    if (isConnecting) return "text-yellow-600";
    return isConnected ? "text-green-600" : "text-red-600";
  };

  const getStatusText = () => {
    if (isConnecting) return "Connecting...";
    return isConnected ? "Connected" : "Disconnected";
  };

  const IconComponent = isConnecting ? RefreshCw : (isConnected ? Wifi : WifiOff);

  return (
    <div className={`flex items-center gap-2 text-xs ${className}`}>
      <IconComponent className={`h-3 w-3 ${getStatusColor()} ${isConnecting ? 'animate-spin' : ''}`} />
      <span className={getStatusColor()}>{getStatusText()}</span>
      {lastUpdated && isConnected && (
        <span className="text-gray-500">
          • Updated {new Date(lastUpdated).toLocaleTimeString()}
        </span>
      )}
    </div>
  );
}

// No data placeholder with action
export function NoDataPlaceholder({
  title = "No Data",
  message = "There's nothing to show here yet.",
  actionLabel,
  onAction,
  icon: Icon = Inbox,
  className = ""
}: {
  title?: string;
  message?: string;
  actionLabel?: string;
  onAction?: () => void;
  icon?: React.ComponentType<{ className?: string }>;
  className?: string;
}) {
  return (
    <div className={`text-center py-12 ${className}`}>
      <div className="flex flex-col items-center space-y-4">
        <div className="p-4 rounded-full bg-gray-50">
          <Icon className="h-8 w-8 text-gray-400" />
        </div>
        <div className="space-y-2">
          <h3 className="text-lg font-medium text-gray-900">{title}</h3>
          <p className="text-gray-500 max-w-md">{message}</p>
        </div>
        {actionLabel && onAction && (
          <Button onClick={onAction} variant="outline">
            {actionLabel}
          </Button>
        )}
      </div>
    </div>
  );
}
