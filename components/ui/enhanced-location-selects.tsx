"use client";

import * as React from "react";
import { EnhancedSelect } from "./enhanced-select";
import type { City, Township, Location } from "@/types/locations";

interface EnhancedCitySelectProps {
  cities: City[];
  value?: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  isLoading?: boolean;
}

export function EnhancedCitySelect({
  cities,
  value,
  onValueChange,
  placeholder = "Select your city",
  disabled = false,
  className,
  isLoading = false,
}: EnhancedCitySelectProps) {
  const options = cities.map((city) => ({
    value: city._id,
    label: city.name,
    description: `${city.name} - ${city.provinceId ? 'Province' : 'City'}`,
  }));

  return (
    <EnhancedSelect
      options={options}
      value={value}
      onValueChange={onValueChange}
      placeholder={placeholder}
      disabled={disabled}
      className={className}
      isLoading={isLoading}
      searchable={true}
      emptyMessage="No cities available"
    />
  );
}

interface EnhancedTownshipSelectProps {
  townships: Township[];
  value?: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  isLoading?: boolean;
}

export function EnhancedTownshipSelect({
  townships,
  value,
  onValueChange,
  placeholder = "Select your township",
  disabled = false,
  className,
  isLoading = false,
}: EnhancedTownshipSelectProps) {
  const options = townships.map((township) => ({
    value: township._id,
    label: township.name,
    description: township.description || `Township in ${township.cityId ? 'selected city' : 'area'}`,
  }));

  return (
    <EnhancedSelect
      options={options}
      value={value}
      onValueChange={onValueChange}
      placeholder={placeholder}
      disabled={disabled}
      className={className}
      isLoading={isLoading}
      searchable={true}
      emptyMessage="No townships available"
    />
  );
}

interface EnhancedLocationSelectProps {
  locations: Location[];
  value?: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  isLoading?: boolean;
}

export function EnhancedLocationSelect({
  locations,
  value,
  onValueChange,
  placeholder = "Select your location",
  disabled = false,
  className,
  isLoading = false,
}: EnhancedLocationSelectProps) {
  const options = locations.map((location) => ({
    value: location._id,
    label: location.name,
    description: location.description || `Location in ${location.townshipId ? 'selected township' : 'area'}`,
  }));

  return (
    <EnhancedSelect
      options={options}
      value={value}
      onValueChange={onValueChange}
      placeholder={placeholder}
      disabled={disabled}
      className={className}
      isLoading={isLoading}
      searchable={true}
      emptyMessage="No locations available"
    />
  );
}

// Generic enhanced select for any dropdown with search
interface EnhancedDropdownSelectProps {
  options: Array<{
    value: string;
    label: string;
    description?: string;
  }>;
  value?: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  isLoading?: boolean;
  searchable?: boolean;
  emptyMessage?: string;
}

export function EnhancedDropdownSelect({
  options,
  value,
  onValueChange,
  placeholder = "Select an option",
  disabled = false,
  className,
  isLoading = false,
  searchable = true,
  emptyMessage = "No options available",
}: EnhancedDropdownSelectProps) {
  return (
    <EnhancedSelect
      options={options}
      value={value}
      onValueChange={onValueChange}
      placeholder={placeholder}
      disabled={disabled}
      className={className}
      isLoading={isLoading}
      searchable={searchable}
      emptyMessage={emptyMessage}
    />
  );
}
