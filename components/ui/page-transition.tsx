// components/ui/page-transition.tsx

"use client";

import { motion, AnimatePresence } from "framer-motion";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import { PageTransitionLoader } from "./professional-loader";

interface PageTransitionProps {
  children: React.ReactNode;
  className?: string;
}

interface TransitionWrapperProps {
  children: React.ReactNode;
  pageKey: string;
  className?: string;
}

// Page transition animations
const pageVariants = {
  initial: {
    opacity: 0,
    y: 20,
    scale: 0.98
  },
  in: {
    opacity: 1,
    y: 0,
    scale: 1
  },
  out: {
    opacity: 0,
    y: -20,
    scale: 1.02
  }
};

const pageTransition = {
  type: "tween",
  ease: "anticipate",
  duration: 0.4
};

// Main page transition component
export function PageTransition({ children, className }: PageTransitionProps) {
  const pathname = usePathname();
  const [isLoading, setIsLoading] = useState(false);
  const [previousPath, setPreviousPath] = useState(pathname);

  useEffect(() => {
    if (pathname !== previousPath) {
      setIsLoading(true);
      setPreviousPath(pathname);
      
      // Simulate loading time for smooth transition
      const timer = setTimeout(() => {
        setIsLoading(false);
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [pathname, previousPath]);

  return (
    <>
      <PageTransitionLoader 
        isLoading={isLoading} 
        fromPage={previousPath}
        toPage={pathname}
      />
      
      <AnimatePresence mode="wait" initial={false}>
        <motion.div
          key={pathname}
          initial="initial"
          animate="in"
          exit="out"
          variants={pageVariants}
          transition={pageTransition}
          className={className}
        >
          {children}
        </motion.div>
      </AnimatePresence>
    </>
  );
}

// Transition wrapper for individual components
export function TransitionWrapper({ children, pageKey, className }: TransitionWrapperProps) {
  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={pageKey}
        initial="initial"
        animate="in"
        exit="out"
        variants={pageVariants}
        transition={pageTransition}
        className={className}
      >
        {children}
      </motion.div>
    </AnimatePresence>
  );
}

// Staggered children animation for lists/grids
export function StaggeredContainer({ children, className }: { children: React.ReactNode; className?: string }) {
  const containerVariants = {
    initial: {},
    in: {
      transition: {
        staggerChildren: 0.1
      }
    },
    out: {}
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="initial"
      animate="in"
      exit="out"
      className={className}
    >
      {children}
    </motion.div>
  );
}

// Individual item animation for staggered lists
export function StaggeredItem({ children, className }: { children: React.ReactNode; className?: string }) {
  const itemVariants = {
    initial: {
      opacity: 0,
      y: 20
    },
    in: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3
      }
    },
    out: {
      opacity: 0,
      y: -10,
      transition: {
        duration: 0.2
      }
    }
  };

  return (
    <motion.div
      variants={itemVariants}
      className={className}
    >
      {children}
    </motion.div>
  );
}

// Slide transition for mobile sidebar
export function SlideTransition({ 
  children, 
  isOpen, 
  direction = "left",
  className 
}: { 
  children: React.ReactNode; 
  isOpen: boolean; 
  direction?: "left" | "right" | "up" | "down";
  className?: string;
}) {
  const slideVariants = {
    closed: {
      x: direction === "left" ? "-100%" : direction === "right" ? "100%" : 0,
      y: direction === "up" ? "-100%" : direction === "down" ? "100%" : 0,
      opacity: 0
    },
    open: {
      x: 0,
      y: 0,
      opacity: 1
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial="closed"
          animate="open"
          exit="closed"
          variants={slideVariants}
          transition={{
            type: "spring",
            damping: 25,
            stiffness: 200
          }}
          className={className}
        >
          {children}
        </motion.div>
      )}
    </AnimatePresence>
  );
}

// Fade transition for overlays
export function FadeTransition({ 
  children, 
  isVisible, 
  className,
  duration = 0.3 
}: { 
  children: React.ReactNode; 
  isVisible: boolean; 
  className?: string;
  duration?: number;
}) {
  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration }}
          className={className}
        >
          {children}
        </motion.div>
      )}
    </AnimatePresence>
  );
}

// Scale transition for modals/popups
export function ScaleTransition({ 
  children, 
  isVisible, 
  className 
}: { 
  children: React.ReactNode; 
  isVisible: boolean; 
  className?: string;
}) {
  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{
            type: "spring",
            damping: 20,
            stiffness: 300
          }}
          className={className}
        >
          {children}
        </motion.div>
      )}
    </AnimatePresence>
  );
}
