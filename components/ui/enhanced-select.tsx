"use client";

import * as React from "react";
import { useState, useRef, useEffect, useCallback, useMemo } from "react";
import { Check, ChevronDown, Search, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { createPortal } from "react-dom";

interface SelectOption {
  value: string;
  label: string;
  description?: string;
}

interface EnhancedSelectProps {
  options: SelectOption[];
  value?: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  isLoading?: boolean;
  searchable?: boolean;
  emptyMessage?: string;
}

export const EnhancedSelect = React.memo(function EnhancedSelect({
  options,
  value,
  onValueChange,
  placeholder = "Select an option",
  disabled = false,
  className,
  isLoading = false,
  searchable = true,
  emptyMessage = "No options found",
}: EnhancedSelectProps) {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [mounted, setMounted] = useState(false);

  const triggerRef = useRef<HTMLButtonElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Ensure component is mounted before rendering portal
  useEffect(() => {
    setMounted(true);
  }, []);

  const selectedOption = useMemo(
    () => options.find((option) => option.value === value),
    [options, value]
  );

  // Filter options based on search query
  const filteredOptions = useMemo(() => {
    if (!searchable) return options;

    const query = searchQuery.toLowerCase();
    return options.filter((option) =>
      option.label.toLowerCase().includes(query) ||
      (option.description && option.description.toLowerCase().includes(query))
    );
  }, [options, searchQuery, searchable]);

  // Calculate dropdown position
  const getDropdownStyle = useCallback(() => {
    if (!triggerRef.current || !open) return {};

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;
    const dropdownHeight = 300;
    const spaceBelow = viewportHeight - triggerRect.bottom;
    const spaceAbove = triggerRect.top;

    // Determine if dropdown should open above or below
    const shouldOpenAbove = spaceBelow < dropdownHeight && spaceAbove > spaceBelow;

    // Calculate position
    let top = shouldOpenAbove
      ? triggerRect.top - Math.min(dropdownHeight, spaceAbove - 20)
      : triggerRect.bottom + 8;

    let left = triggerRect.left;
    let width = triggerRect.width;

    // Ensure dropdown doesn't go off screen
    if (left + width > viewportWidth) {
      left = viewportWidth - width - 10;
    }
    if (left < 10) {
      left = 10;
      width = Math.min(width, viewportWidth - 20);
    }

    return {
      position: 'fixed' as const,
      top: `${Math.max(10, top)}px`,
      left: `${left}px`,
      width: `${width}px`,
      maxHeight: '300px',
      minWidth: '200px',
      zIndex: 99999,
    };
  }, [open]);

  // Handle opening dropdown
  const handleOpen = useCallback(() => {
    if (disabled || isLoading) return;
    setOpen(true);
    setSearchQuery("");
  }, [disabled, isLoading]);

  // Handle closing dropdown
  const handleClose = useCallback(() => {
    setOpen(false);
    setSearchQuery("");
  }, []);

  // Handle option selection
  const handleSelect = useCallback((optionValue: string) => {
    onValueChange(optionValue);
    handleClose();
  }, [onValueChange, handleClose]);

  // Close dropdown when clicking outside, scrolling, or resizing
  useEffect(() => {
    if (!open) return;

    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        triggerRef.current &&
        !triggerRef.current.contains(event.target as Node)
      ) {
        handleClose();
      }
    };

    const handleScroll = () => handleClose();
    const handleResize = () => handleClose();

    document.addEventListener("mousedown", handleClickOutside);
    document.addEventListener("scroll", handleScroll, true);
    window.addEventListener("resize", handleResize);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("scroll", handleScroll, true);
      window.removeEventListener("resize", handleResize);
    };
  }, [open]);

  // Handle keyboard navigation
  useEffect(() => {
    if (!open) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        handleClose();
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [open]);

  // Render dropdown content
  const renderDropdown = useCallback(() => {
    if (!open || !mounted) return null;

    return (
      <>
        {/* Backdrop */}
        <div
          className="fixed inset-0 z-[99998] bg-black/20 md:bg-transparent"
          onClick={handleClose}
        />

        {/* Dropdown */}
        <div
          ref={dropdownRef}
          className="bg-white border border-gray-300 rounded-lg shadow-2xl"
          style={getDropdownStyle()}
          onMouseDown={(e) => e.stopPropagation()}
        >
          {/* Search Input */}
          {searchable && (
            <div className="p-3 border-b border-gray-100">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-8 h-9 text-sm border-gray-200 focus:border-emerald-500 focus:ring-emerald-500"
                  autoFocus
                />
                {searchQuery && (
                  <button
                    onClick={() => setSearchQuery("")}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 hover:bg-gray-100 rounded"
                  >
                    <X className="h-3 w-3 text-gray-400" />
                  </button>
                )}
              </div>
            </div>
          )}

          {/* Options List */}
          <div className="max-h-60 overflow-y-auto">
            {isLoading ? (
              <div className="p-4 text-center">
                <div className="w-6 h-6 border-2 border-emerald-500 border-t-transparent rounded-full animate-spin mx-auto mb-2" />
                <p className="text-sm text-gray-500">Loading...</p>
              </div>
            ) : filteredOptions.length === 0 ? (
              <div className="p-4 text-center text-sm text-gray-500">
                {searchQuery ? "No options found." : emptyMessage}
              </div>
            ) : (
              <div className="p-1">
                {filteredOptions.map((option) => (
                  <div
                    key={option.value}
                    onClick={() => handleSelect(option.value)}
                    className={cn(
                      "flex items-center gap-3 p-3 cursor-pointer hover:bg-emerald-50 rounded-md transition-colors",
                      value === option.value && "bg-emerald-100"
                    )}
                  >
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-gray-900 truncate">
                        {option.label}
                      </div>
                      {option.description && (
                        <div className="text-sm text-gray-500 truncate">
                          {option.description}
                        </div>
                      )}
                    </div>
                    {value === option.value && (
                      <Check className="h-4 w-4 text-emerald-600 flex-shrink-0" />
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </>
    );
  }, [open, mounted, handleClose, getDropdownStyle, searchable, searchQuery, isLoading, filteredOptions, emptyMessage, value, handleSelect]);

  return (
    <>
      <Button
        ref={triggerRef}
        variant="outline"
        role="combobox"
        aria-expanded={open}
        onClick={handleOpen}
        disabled={disabled || isLoading}
        className={cn(
          "w-full h-12 justify-between text-left font-normal border-2 hover:border-emerald-300 focus:border-emerald-500 focus:ring-2 focus:ring-emerald-200",
          !value && "text-muted-foreground",
          open && "border-emerald-500 ring-2 ring-emerald-200",
          className
        )}
      >
        <span className="truncate">
          {isLoading ? "Loading..." : selectedOption?.label || placeholder}
        </span>
        <ChevronDown className={cn(
          "ml-2 h-4 w-4 shrink-0 opacity-50 transition-transform",
          open && "rotate-180"
        )} />
      </Button>

      {/* Portal dropdown to body */}
      {mounted && typeof document !== "undefined" && createPortal(
        renderDropdown(),
        document.body
      )}
    </>
  );
});