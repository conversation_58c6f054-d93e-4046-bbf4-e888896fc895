// components/ui/enhanced-location-selects-v2.tsx

"use client";

import * as React from "react";
import { useState, useCallback } from "react";
import { LocationSelectionButton, CompactLocationButton } from "./LocationSelectionButton";
import { EnhancedSelect } from "./enhanced-select";
import type { City, Township, Location } from "@/types/locations";
import type { LocationSelectionResult } from "@/components/modals/LocationSelectionModal";

interface BaseLocationSelectProps {
  value?: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  isLoading?: boolean;
  modalMode?: boolean; // New prop to enable modal mode
  error?: string;
  required?: boolean;
}

// Enhanced Province Select with modal mode support
interface EnhancedProvinceSelectProps extends BaseLocationSelectProps {
  provinces: Array<{ _id: string; name: string; code: string }>;
}

export function EnhancedProvinceSelect({
  provinces,
  value,
  onValueChange,
  placeholder = "Select your province",
  disabled = false,
  className,
  isLoading = false,
  modalMode = false,
  error,
  required = false
}: EnhancedProvinceSelectProps) {
  const [locationSelection, setLocationSelection] = useState<LocationSelectionResult | null>(null);

  // If modal mode is enabled, use the LocationSelectionButton
  if (modalMode) {
    const handleLocationSelect = useCallback((result: LocationSelectionResult) => {
      setLocationSelection(result);
      onValueChange(result.provinceId);
    }, [onValueChange]);

    // Find current province from value
    const currentProvince = provinces.find(p => p._id === value);
    const currentSelection = currentProvince ? {
      provinceId: currentProvince._id,
      provinceName: currentProvince.name,
      cityId: "",
      cityName: "",
      townshipId: "",
      townshipName: "",
      locationId: "",
      locationName: "",
      fullPath: currentProvince.name
    } as LocationSelectionResult : null;

    return (
      <CompactLocationButton
        value={currentSelection}
        onLocationSelect={handleLocationSelect}
        placeholder={placeholder}
        disabled={disabled}
        className={className}
      />
    );
  }

  // Fallback to original dropdown behavior
  const options = provinces.map((province) => ({
    value: province._id,
    label: `${province.name} (${province.code})`,
    description: province.name,
  }));

  return (
    <EnhancedSelect
      options={options}
      value={value}
      onValueChange={onValueChange}
      placeholder={placeholder}
      disabled={disabled}
      className={className}
      isLoading={isLoading}
      searchable={true}
      emptyMessage="No provinces available"
    />
  );
}

// Enhanced City Select with modal mode support
interface EnhancedCitySelectProps extends BaseLocationSelectProps {
  cities: City[];
}

export function EnhancedCitySelect({
  cities,
  value,
  onValueChange,
  placeholder = "Select your city",
  disabled = false,
  className,
  isLoading = false,
  modalMode = false,
  error,
  required = false
}: EnhancedCitySelectProps) {
  const [locationSelection, setLocationSelection] = useState<LocationSelectionResult | null>(null);

  if (modalMode) {
    const handleLocationSelect = useCallback((result: LocationSelectionResult) => {
      setLocationSelection(result);
      onValueChange(result.cityId);
    }, [onValueChange]);

    const currentCity = cities.find(c => c._id === value);
    const currentSelection = currentCity ? {
      provinceId: "",
      provinceName: "",
      cityId: currentCity._id,
      cityName: currentCity.name,
      townshipId: "",
      townshipName: "",
      locationId: "",
      locationName: "",
      fullPath: currentCity.name
    } as LocationSelectionResult : null;

    return (
      <CompactLocationButton
        value={currentSelection}
        onLocationSelect={handleLocationSelect}
        placeholder={placeholder}
        disabled={disabled}
        className={className}
      />
    );
  }

  const options = cities.map((city) => ({
    value: city._id,
    label: city.name,
    description: `${city.name} - ${city.provinceId ? 'Province' : 'City'}`,
  }));

  return (
    <EnhancedSelect
      options={options}
      value={value}
      onValueChange={onValueChange}
      placeholder={placeholder}
      disabled={disabled}
      className={className}
      isLoading={isLoading}
      searchable={true}
      emptyMessage="No cities available"
    />
  );
}

// Enhanced Township Select with modal mode support
interface EnhancedTownshipSelectProps extends BaseLocationSelectProps {
  townships: Township[];
}

export function EnhancedTownshipSelect({
  townships,
  value,
  onValueChange,
  placeholder = "Select your township",
  disabled = false,
  className,
  isLoading = false,
  modalMode = false,
  error,
  required = false
}: EnhancedTownshipSelectProps) {
  const [locationSelection, setLocationSelection] = useState<LocationSelectionResult | null>(null);

  if (modalMode) {
    const handleLocationSelect = useCallback((result: LocationSelectionResult) => {
      setLocationSelection(result);
      onValueChange(result.townshipId);
    }, [onValueChange]);

    const currentTownship = townships.find(t => t._id === value);
    const currentSelection = currentTownship ? {
      provinceId: "",
      provinceName: "",
      cityId: "",
      cityName: "",
      townshipId: currentTownship._id,
      townshipName: currentTownship.name,
      locationId: "",
      locationName: "",
      fullPath: currentTownship.name
    } as LocationSelectionResult : null;

    return (
      <CompactLocationButton
        value={currentSelection}
        onLocationSelect={handleLocationSelect}
        placeholder={placeholder}
        disabled={disabled}
        className={className}
      />
    );
  }

  const options = townships.map((township) => ({
    value: township._id,
    label: township.name,
    description: township.description || `Township in ${township.cityId ? 'selected city' : 'area'}`,
  }));

  return (
    <EnhancedSelect
      options={options}
      value={value}
      onValueChange={onValueChange}
      placeholder={placeholder}
      disabled={disabled}
      className={className}
      isLoading={isLoading}
      searchable={true}
      emptyMessage="No townships available"
    />
  );
}

// Enhanced Location Select with modal mode support
interface EnhancedLocationSelectProps extends BaseLocationSelectProps {
  locations: Location[];
}

export function EnhancedLocationSelect({
  locations,
  value,
  onValueChange,
  placeholder = "Select your location",
  disabled = false,
  className,
  isLoading = false,
  modalMode = false,
  error,
  required = false
}: EnhancedLocationSelectProps) {
  const [locationSelection, setLocationSelection] = useState<LocationSelectionResult | null>(null);

  if (modalMode) {
    const handleLocationSelect = useCallback((result: LocationSelectionResult) => {
      setLocationSelection(result);
      onValueChange(result.locationId);
    }, [onValueChange]);

    const currentLocation = locations.find(l => l._id === value);
    const currentSelection = currentLocation ? {
      provinceId: "",
      provinceName: "",
      cityId: "",
      cityName: "",
      townshipId: "",
      townshipName: "",
      locationId: currentLocation._id,
      locationName: currentLocation.name,
      fullPath: currentLocation.name
    } as LocationSelectionResult : null;

    return (
      <CompactLocationButton
        value={currentSelection}
        onLocationSelect={handleLocationSelect}
        placeholder={placeholder}
        disabled={disabled}
        className={className}
      />
    );
  }

  const options = locations.map((location) => ({
    value: location._id,
    label: location.name,
    description: location.description || `Location in ${location.townshipId ? 'selected township' : 'area'}`,
  }));

  return (
    <EnhancedSelect
      options={options}
      value={value}
      onValueChange={onValueChange}
      placeholder={placeholder}
      disabled={disabled}
      className={className}
      isLoading={isLoading}
      searchable={true}
      emptyMessage="No locations available"
    />
  );
}

// Complete Location Selector - handles the entire hierarchy in one component
interface CompleteLocationSelectorProps {
  value?: LocationSelectionResult | null;
  onLocationSelect: (result: LocationSelectionResult) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  error?: string;
  required?: boolean;
  showFullPath?: boolean;
  size?: "sm" | "md" | "lg";
}

export function CompleteLocationSelector({
  value,
  onLocationSelect,
  placeholder = "Select your complete location",
  disabled = false,
  className,
  error,
  required = false,
  showFullPath = true,
  size = "md"
}: CompleteLocationSelectorProps) {
  return (
    <LocationSelectionButton
      value={value}
      onLocationSelect={onLocationSelect}
      placeholder={placeholder}
      disabled={disabled}
      className={className}
      error={error}
      required={required}
      showFullPath={showFullPath}
      size={size}
      variant="outline"
    />
  );
}

// Hook to detect if we're inside a modal (for auto-enabling modal mode)
export function useModalDetection() {
  const [isInModal, setIsInModal] = React.useState(false);

  React.useEffect(() => {
    // Check if we're inside a modal by looking for modal-related classes in parent elements
    const checkModalContext = () => {
      const element = document.activeElement || document.body;
      const modalParent = element.closest('[role="dialog"], .modal, [data-modal="true"], [data-modal-open="true"]');
      setIsInModal(!!modalParent);
    };

    checkModalContext();

    // Re-check when focus changes
    document.addEventListener('focusin', checkModalContext);
    return () => document.removeEventListener('focusin', checkModalContext);
  }, []);

  return isInModal;
}
