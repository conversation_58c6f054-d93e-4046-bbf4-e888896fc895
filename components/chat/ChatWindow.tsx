
// components/chat/ChatWindow.tsx
"use client"

import React, { useState, useCallback, useRef, useEffect } from "react"
import { ThumbsUp, ThumbsDown, Copy, Edit, Send, Volume2, ChevronDown, ChevronUp, X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Avatar } from "@/components/ui/avatar"
import { cn } from "@/lib/utils"
import { v4 as uuidv4 } from 'uuid'

interface Message {
  id: string
  content: string
  isUser: boolean
  timestamp: Date
}

interface FAQ {
  question: string
  answer: string
  isOpen: boolean
}


export const ChatWindow = ({ onClose }: { onClose: () => void }) => {
    const [messages, setMessages] = useState<Message[]>([
      {
        id: 'welcome-' + Date.now(),
        content: "Hi there! I'm <PERSON>, your personal shopping assistant. I'm here to help you with anything related to Stokvel Market - from finding products and joining groups to tracking orders and understanding how our platform works. How can I assist you today? 😊",
        isUser: false,
        timestamp: new Date(),
      }
    ])
    const [inputValue, setInputValue] = useState("")
    const [isTyping, setIsTyping] = useState(false)
    const [showSendButton, setShowSendButton] = useState(false)
    const [quickActions, setQuickActions] = useState<any[]>([])
    const [showQuickActions, setShowQuickActions] = useState(true)
    const [aiServiceStatus, setAiServiceStatus] = useState<'full' | 'limited' | 'checking'>('checking')
    const textareaRef = useRef<HTMLTextAreaElement>(null)
    const messagesEndRef = useRef<HTMLDivElement>(null)
    const [sessionId] = useState(() => uuidv4())

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [messages])

  // Load quick actions and check AI service status on component mount
  useEffect(() => {
    const loadQuickActions = async () => {
      try {
        const response = await fetch('/api/ai/chat-actions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...(localStorage.getItem('accessToken') && {
              'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
            })
          },
          body: JSON.stringify({
            action: 'get_quick_actions',
            context: {
              sessionId,
              currentPage: window.location.pathname
            }
          }),
        });

        if (response.ok) {
          const data = await response.json();
          if (data.success && data.actions) {
            setQuickActions(data.actions);
          }
        }
      } catch (error) {
        console.error('Failed to load quick actions:', error);
      }
    };

    const checkAIServiceStatus = async () => {
      try {
        const response = await fetch('/api/ai/health');
        if (response.ok) {
          const health = await response.json();
          if (health.status === 'healthy') {
            setAiServiceStatus('full');
          } else {
            setAiServiceStatus('limited');
          }
        } else {
          setAiServiceStatus('limited');
        }
      } catch (error) {
        console.error('Failed to check AI service status:', error);
        setAiServiceStatus('limited');
      }
    };

    loadQuickActions();
    checkAIServiceStatus();
  }, [sessionId])

  const [faqs, setFaqs] = useState<FAQ[]>([
    {
      question: "How do I join a Stockvel?",
      answer:
        "You can join a Stockvel by navigating to our Membership section and selecting a plan that suits your needs. Follow the registration process to become a member.",
      isOpen: false,
    },
    {
      question: "What are the investment options?",
      answer:
        "We offer various investment options including mutual funds, stocks, and fixed deposits. Each option is carefully selected to maximize returns while managing risk.",
      isOpen: false,
    },
    {
      question: "How secure are my investments?",
      answer:
        "Your investments are protected by state-of-the-art security measures and we're fully compliant with financial regulations. We also provide insurance coverage for added protection.",
      isOpen: false,
    },
  ])

  const toggleFAQ = (index: number) => {
    setFaqs(
      faqs.map((faq, i) => ({
        ...faq,
        isOpen: i === index ? !faq.isOpen : faq.isOpen,
      })),
    )
  }


  const debounce = <F extends (...args: Array<unknown>) => void>(
    func: F, 
    wait: number
  ) => {
    let timeout: NodeJS.Timeout | null = null
    return (...args: Parameters<F>) => {
      if (timeout) {
        clearTimeout(timeout)
      }
      timeout = setTimeout(() => {
        func(...args)
        timeout = null
      }, wait)
    }
  }

  
  const debouncedStopTyping = useCallback(
    debounce(() => {
      setIsTyping(false)
      if (inputValue.trim().length > 0) {
        setShowSendButton(true)
      }
    }, 1500),
    [inputValue]
  )

// Input change handler with dynamic textarea resizing
const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value
    setInputValue(value)
    setIsTyping(true)
    setShowSendButton(false)
    debouncedStopTyping()

    // Dynamic textarea resizing
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      const scrollHeight = textareaRef.current.scrollHeight
      textareaRef.current.style.height = `${Math.min(Math.max(scrollHeight, 48), 150)}px`
    }
  }


const handleSubmit = async (e: React.FormEvent) => {
  e.preventDefault();
  if (!inputValue.trim()) return;

  const userMessage: Message = {
    id: Date.now().toString(),
    content: inputValue,
    isUser: true,
    timestamp: new Date(),
  };

  setMessages(prev => [...prev, userMessage]);
  const currentQuery = inputValue;
  setInputValue("");
  setShowSendButton(false);
  setIsTyping(false);

  // Add typing indicator
  const typingMessage: Message = {
    id: 'typing-' + Date.now(),
    content: 'Sarah is typing...',
    isUser: false,
    timestamp: new Date(),
  };
  setMessages(prev => [...prev, typingMessage]);

  try {
    // Get user context from current page/session
    const userContext = {
      currentPage: window.location.pathname,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString()
    };

    const response = await fetch('/api/ai/ask', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Include auth token if available
        ...(localStorage.getItem('accessToken') && {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        })
      },
      body: JSON.stringify({
        query: currentQuery,
        sessionId,
        userContext
      }),
    });

    if (!response.ok) {
      // Remove typing indicator
      setMessages(prev => prev.filter(msg => msg.id !== typingMessage.id));

      // Add error message
      const errorMessage: Message = {
        id: Date.now().toString(),
        content: "I'm experiencing some technical difficulties right now. Please try again in a moment. In the meantime, I can still help you with basic questions about our platform!",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
      return;
    }

    const reader = response.body?.getReader();
    if (!reader) {
      // Remove typing indicator
      setMessages(prev => prev.filter(msg => msg.id !== typingMessage.id));

      // Add error message
      const errorMessage: Message = {
        id: Date.now().toString(),
        content: "I'm having trouble processing your request right now. Please try asking your question again.",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
      return;
    }

    // Remove typing indicator and add actual AI message
    setMessages(prev => prev.filter(msg => msg.id !== typingMessage.id));

    const aiMessage: Message = {
      id: Date.now().toString(),
      content: '',
      isUser: false,
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, aiMessage]);

    const decoder = new TextDecoder();
    while (true) {
      const { value, done } = await reader.read();
      if (done) break;
      
      const chunk = decoder.decode(value);
      setMessages(prev => {
        const newMessages = [...prev];
        const lastMessage = newMessages[newMessages.length - 1];
        lastMessage.content += chunk;

        // Detect if we're in limited mode based on response content
        if (chunk.includes('limited mode') || chunk.includes('quota limits') || chunk.includes('basic mode')) {
          setAiServiceStatus('limited');
        }

        return newMessages;
      });
    }
  } catch (error) {
    console.error('Chat error:', error);

    // Remove typing indicator if it exists
    setMessages(prev => prev.filter(msg => !msg.id.startsWith('typing-')));

    // Add user-friendly error message
    const errorMessage: Message = {
      id: Date.now().toString(),
      content: "I apologize, but I'm having some technical difficulties right now. Please try again in a moment. If the problem persists, you can contact our support team for immediate assistance.",
      isUser: false,
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, errorMessage]);
  }
};  return (
    <div className="flex flex-col w-80 h-[32rem] bg-white rounded-xl shadow-lg overflow-hidden">
      <div className="flex justify-between bg-[#2A7C6C] items-center p-4 border-b">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
            <span className="text-sm font-semibold text-white">S</span>
          </div>
          <div>
            <h3
              className="text-lg font-semibold text-white"
              style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
            >
              Sarah
            </h3>
            <div className="flex items-center gap-2">
              <p className="text-xs text-white/80">Customer Service Representative</p>
              {aiServiceStatus === 'full' && (
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span className="text-xs text-green-200">AI Active</span>
                </div>
              )}
              {aiServiceStatus === 'limited' && (
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                  <span className="text-xs text-yellow-200">Basic Mode</span>
                </div>
              )}
              {aiServiceStatus === 'checking' && (
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse"></div>
                  <span className="text-xs text-gray-300">Checking...</span>
                </div>
              )}
            </div>
          </div>
        </div>
        <Button variant="ghost" size="sm" onClick={onClose}>
          <X className="h-4 w-4" />
        </Button>
      </div>
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        <div className="mb-4">
          <h3
            className="text-lg font-semibold mb-3 text-[#2F4858]"
            style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
          >
            Frequently Asked Questions
          </h3>
          {faqs.map((faq, index) => (
            <div key={index} className="mb-2">
              <button
                onClick={() => toggleFAQ(index)}
                className="w-full text-left p-2 bg-gray-50 hover:bg-gray-100 rounded-md"
              >
                <div className="flex justify-between items-center">
                  <span className="font-medium text-[#2F4858]">{faq.question}</span>
                  <span>{faq.isOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}</span>
                </div>
              </button>
              {faq.isOpen && (
                <div
                  className="p-2 text-sm text-gray-600 bg-gray-50/50 rounded-md mt-1"
                  style={{ fontFamily: "Avenir, sans-serif" }}
                >
                  {faq.answer}
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Quick Actions */}
        {showQuickActions && quickActions.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-semibold text-[#2A7C6C] mb-2">Quick Actions</h4>
            <div className="flex flex-wrap gap-2">
              {quickActions.map((action, index) => (
                <button
                  key={index}
                  onClick={() => {
                    if (action.url) {
                      window.open(action.url, '_blank');
                    } else {
                      setInputValue(action.label);
                      setShowQuickActions(false);
                    }
                  }}
                  className="px-3 py-1 text-xs bg-[#2A7C6C]/10 text-[#2A7C6C] rounded-full hover:bg-[#2A7C6C]/20 transition-colors"
                >
                  {action.label}
                </button>
              ))}
            </div>
            <button
              onClick={() => setShowQuickActions(false)}
              className="text-xs text-gray-500 mt-2 hover:text-gray-700"
            >
              Hide suggestions
            </button>
          </div>
        )}

        {messages.map((message) => (
          <div key={message.id} className={`flex gap-4 ${message.isUser ? "flex-row-reverse" : ""}`}>
            <Avatar className={`h-8 w-8 ${message.isUser ? "bg-[#2A7C6C]" : "bg-gradient-to-br from-[#2A7C6C] to-[#236358]"}`}>
              {message.isUser ? (
                <span className="text-xs text-white font-semibold">You</span>
              ) : (
                <div className="w-full h-full bg-white/20 rounded-full flex items-center justify-center">
                  <span className="text-xs font-semibold text-white">S</span>
                </div>
              )}
            </Avatar>
            <div className={`flex-1 space-y-2 ${message.isUser ? "items-end" : "items-start"}`}>
              <div className={`relative p-4 rounded-lg ${message.isUser ? "bg-[#2A7C6C] text-white" : "bg-gray-50"}`}>
                {!message.isUser && (
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-sm font-semibold text-[#2A7C6C]">Sarah</span>
                    <span className="text-xs text-gray-500">
                      {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </span>
                  </div>
                )}
                <div className={`${message.content === 'Sarah is typing...' ? 'flex items-center gap-2' : ''}`}>
                  {message.content === 'Sarah is typing...' ? (
                    <>
                      <div className="flex gap-1">
                        <div className="w-2 h-2 bg-[#2A7C6C] rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-[#2A7C6C] rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                        <div className="w-2 h-2 bg-[#2A7C6C] rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                      </div>
                      <span className="text-sm text-gray-500 italic">Sarah is typing...</span>
                    </>
                  ) : (
                    <p style={{ fontFamily: "Avenir, sans-serif" }}>{message.content}</p>
                  )}
                </div>
                {!message.isUser && message.content !== 'Sarah is typing...' && (
                  <div className="flex gap-2 mt-3 text-sm justify-start">
                    <button className="hover:text-[#2A7C6C] transition-colors p-1 rounded hover:bg-gray-100" title="Helpful">
                      <ThumbsUp className="h-4 w-4" />
                    </button>
                    <button className="hover:text-[#2A7C6C] transition-colors p-1 rounded hover:bg-gray-100" title="Not helpful">
                      <ThumbsDown className="h-4 w-4" />
                    </button>
                    <button
                      className="hover:text-[#2A7C6C] transition-colors p-1 rounded hover:bg-gray-100"
                      title="Copy message"
                      onClick={() => navigator.clipboard.writeText(message.content)}
                    >
                      <Copy className="h-4 w-4" />
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      <div className="bg-[#F0F2F5] px-4 py-3">
        <form onSubmit={handleSubmit} className="flex items-center w-full gap-2">
          <div className="relative flex-1">
            <textarea
              ref={textareaRef}
              value={inputValue}
              onChange={handleInputChange}
              placeholder="Type a message"
              rows={1}
              className={cn(
                "w-full px-4 py-3 bg-white border-0 rounded-2xl focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 shadow-sm placeholder:text-gray-500 resize-none overflow-hidden transition-all duration-300 ease-in-out transform-gpu",
                isTyping && "py-5 shadow-md scale-[1.02] translate-y-[-2px]"
              )}
              style={{ 
                fontFamily: "Avenir, sans-serif", 
                minHeight: "48px", 
                maxHeight: "150px" 
              }}
            />
            <Button
              type="submit"
              size="sm"
              className={cn(
                "absolute right-2 top-1/2 -translate-y-1/2 bg-[#2A7C6C] hover:bg-[#236358] rounded-full w-8 h-8 p-0 flex items-center justify-center shadow-sm transition-all duration-300 ease-in-out transform-gpu",
                (!showSendButton || isTyping) && "opacity-0 translate-x-4",
                showSendButton && !isTyping && "opacity-100 translate-x-0"
              )}
              disabled={!showSendButton || isTyping}
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
          <Button 
            variant="ghost" 
            size="icon"
            className={cn(
              "rounded-full h-10 w-10 hover:bg-white/80 text-[#2A7C6C] flex-shrink-0 transition-all duration-300 ease-in-out transform-gpu",
              isTyping && "opacity-0 scale-90 translate-x-4"
            )}
          >
            <Volume2 className="h-5 w-5" />
          </Button>
        </form>
      </div>

    </div>
  )
}

