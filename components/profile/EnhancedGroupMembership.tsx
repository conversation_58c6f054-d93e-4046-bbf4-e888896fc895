"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useAuth } from "@/context/AuthContext";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  Users, 
  MapPin, 
  TrendingUp, 
  ShoppingCart,
  Star,
  AlertTriangle,
  CheckCircle,
  Clock,
  ArrowRight,
  ExternalLink,
  UserMinus,
  Package
} from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import { toast } from "sonner";
import Link from "next/link";

interface GroupMembershipStatus {
  hasActiveGroup: boolean;
  activeGroups: {
    _id: string;
    name: string;
    description: string;
    geolocation?: string;
    members: number;
    joinedAt: Date;
    isLatest: boolean;
  }[];
  previousGroups: {
    _id: string;
    name: string;
    description: string;
    geolocation?: string;
    members: number;
    leftAt?: Date;
    hasUndeliveredItems: boolean;
    undeliveredItemsCount: number;
  }[];
  canJoinNewGroup: boolean;
  requiresRelocation: boolean;
  multipleActiveGroups: boolean;
}

export function EnhancedGroupMembership() {
  const { user } = useAuth();
  const [membershipStatus, setMembershipStatus] = useState<GroupMembershipStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [leavingGroup, setLeavingGroup] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      fetchMembershipStatus();
    }
  }, [user]);

  const fetchMembershipStatus = async () => {
    if (!user) return;
    
    setLoading(true);
    try {
      const response = await fetch(`/api/users/${user._id}/group-membership-status`);
      const data = await response.json();
      
      if (data.success) {
        setMembershipStatus(data.data);
      } else {
        toast.error("Failed to load group membership status");
      }
    } catch (error) {
      console.error('Error fetching membership status:', error);
      toast.error("Failed to load group membership status");
    } finally {
      setLoading(false);
    }
  };

  const handleLeaveGroup = async (groupId: string, groupName: string) => {
    if (!user || !membershipStatus) return;
    
    // If user has multiple groups, we need to specify which group to transfer items to
    const otherActiveGroups = membershipStatus.activeGroups.filter(g => g._id !== groupId);
    
    if (otherActiveGroups.length === 0) {
      toast.error("Cannot leave your only active group. Join another group first.");
      return;
    }

    const transferToGroupId = otherActiveGroups[0]._id; // Transfer to the first remaining group
    
    setLeavingGroup(groupId);
    try {
      const response = await fetch('/api/stokvel-groups/leave', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user._id,
          groupId,
          transferItemsToGroupId: transferToGroupId
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        toast.success(data.message);
        fetchMembershipStatus(); // Refresh the status
      } else {
        toast.error(data.error || "Failed to leave group");
      }
    } catch (error) {
      console.error('Error leaving group:', error);
      toast.error("Failed to leave group");
    } finally {
      setLeavingGroup(null);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Group Membership</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 2 }).map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!membershipStatus) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Group Membership</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600">Failed to load group membership information.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Multiple Groups Warning */}
      {membershipStatus.multipleActiveGroups && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-red-50 border border-red-200 rounded-lg p-4"
        >
          <div className="flex items-start">
            <AlertTriangle className="h-5 w-5 text-red-600 mr-3 mt-0.5 flex-shrink-0" />
            <div>
              <h4 className="font-semibold text-red-800 mb-2">Multiple Group Membership Detected</h4>
              <p className="text-sm text-red-700 mb-3">
                You are currently a member of multiple groups. Our new policy allows only one active group membership. 
                Please leave all but one group to comply with the updated policy.
              </p>
              <p className="text-xs text-red-600">
                We recommend keeping your most recent group and leaving the others.
              </p>
            </div>
          </div>
        </motion.div>
      )}

      {/* Current Active Groups */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Users className="h-5 w-5 mr-2 text-green-600" />
            Current Active Groups
            {membershipStatus.activeGroups.length > 1 && (
              <Badge variant="destructive" className="ml-2">
                {membershipStatus.activeGroups.length} Groups
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {membershipStatus.activeGroups.length === 0 ? (
            <div className="text-center py-8">
              <Users className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <p className="text-gray-600 mb-4">You are not currently a member of any group.</p>
              <Link href="/groups">
                <Button className="bg-gradient-to-r from-[#2A7C6C] to-[#1E5A4F] text-white">
                  <Users className="h-4 w-4 mr-2" />
                  Browse Groups
                </Button>
              </Link>
            </div>
          ) : (
            <div className="space-y-4">
              {membershipStatus.activeGroups.map((group, index) => (
                <motion.div
                  key={group._id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className={`p-4 rounded-lg border ${
                    group.isLatest && membershipStatus.multipleActiveGroups
                      ? 'bg-blue-50 border-blue-200'
                      : 'bg-green-50 border-green-200'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center mb-2">
                        <h4 className="font-semibold text-gray-900">{group.name}</h4>
                        {group.isLatest && membershipStatus.multipleActiveGroups && (
                          <Badge variant="secondary" className="ml-2 bg-blue-100 text-blue-700">
                            Latest Joined
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{group.description}</p>
                      {group.geolocation && (
                        <div className="flex items-center text-gray-500 text-sm mb-2">
                          <MapPin className="h-4 w-4 mr-1" />
                          {group.geolocation}
                        </div>
                      )}
                      <div className="flex items-center text-gray-500 text-sm">
                        <Users className="h-4 w-4 mr-1" />
                        {group.members} members
                      </div>
                    </div>
                    
                    <div className="flex flex-col gap-2 ml-4">
                      <Link href={`/group/${group._id}`}>
                        <Button variant="outline" size="sm" className="w-full">
                          <ExternalLink className="h-4 w-4 mr-2" />
                          View Group
                        </Button>
                      </Link>
                      
                      {membershipStatus.multipleActiveGroups && (
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleLeaveGroup(group._id, group.name)}
                          disabled={leavingGroup === group._id}
                          className="w-full"
                        >
                          {leavingGroup === group._id ? (
                            <>
                              <Clock className="h-4 w-4 mr-2 animate-spin" />
                              Leaving...
                            </>
                          ) : (
                            <>
                              <UserMinus className="h-4 w-4 mr-2" />
                              Leave Group
                            </>
                          )}
                        </Button>
                      )}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Previous Groups */}
      {membershipStatus.previousGroups.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Clock className="h-5 w-5 mr-2 text-gray-600" />
              Previous Groups
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {membershipStatus.previousGroups.map((group, index) => (
                <motion.div
                  key={group._id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className={`p-4 rounded-lg border ${
                    group.hasUndeliveredItems 
                      ? 'bg-amber-50 border-amber-200' 
                      : 'bg-gray-50 border-gray-200'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center mb-2">
                        <h4 className="font-medium text-gray-900">{group.name}</h4>
                        {group.hasUndeliveredItems && (
                          <Badge variant="outline" className="ml-2 bg-amber-100 text-amber-800 border-amber-300">
                            <Package className="h-3 w-3 mr-1" />
                            {group.undeliveredItemsCount} pending items
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{group.description}</p>
                      {group.geolocation && (
                        <div className="flex items-center text-gray-500 text-sm">
                          <MapPin className="h-4 w-4 mr-1" />
                          {group.geolocation}
                        </div>
                      )}
                    </div>
                    
                    {group.hasUndeliveredItems && (
                      <div className="ml-4">
                        <p className="text-xs text-amber-700 mb-2">
                          You have unfinished transactions in this group
                        </p>
                        <Button variant="outline" size="sm" disabled>
                          Items Transferred
                        </Button>
                      </div>
                    )}
                  </div>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Group Management Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Group Management</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Link href="/groups">
              <Button variant="outline" className="w-full h-auto p-4">
                <div className="text-center">
                  <Users className="h-6 w-6 mx-auto mb-2 text-[#2A7C6C]" />
                  <div className="font-medium">Browse Groups</div>
                  <div className="text-sm text-gray-600">Find and join new groups</div>
                </div>
              </Button>
            </Link>
            
            <Link href="/profile/groups">
              <Button variant="outline" className="w-full h-auto p-4">
                <div className="text-center">
                  <TrendingUp className="h-6 w-6 mx-auto mb-2 text-purple-600" />
                  <div className="font-medium">Group Analytics</div>
                  <div className="text-sm text-gray-600">View detailed group statistics</div>
                </div>
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
