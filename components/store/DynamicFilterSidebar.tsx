"use client";

import { useState } from "react";
import { <PERSON>lider } from "@/components/ui/slider";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import type { ProductCategory } from "@/types/productCategory";
import { Star, Filter, X, Package, DollarSign, ShieldCheck, Award, Tag, Sparkles } from "lucide-react";
import { motion } from "framer-motion";

interface DynamicFilterSidebarProps {
  categories: ProductCategory[];
  selectedCategory: string;
  selectedSubcategories: string[];
  setSelectedSubcategories: (subcategories: string[]) => void;
  selectedAttributes: Record<string, string[]>;
  setSelectedAttributes: (attributes: Record<string, string[]>) => void;
  minPrice: number;
  setMinPrice: (price: number) => void;
  maxPrice: number;
  setMaxPrice: (price: number) => void;
  inStock?: boolean;
  setInStock?: (inStock: boolean) => void;
  rating?: number | null;
  setRating?: (rating: number | null) => void;
  sortBy?: string;
  setSortBy?: (sortBy: string) => void;
  brands?: string[];
  setBrands?: (brands: string[]) => void;
}

export function DynamicFilterSidebar({
  // These props are kept for future implementation
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  categories,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  selectedCategory,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  selectedSubcategories,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  setSelectedSubcategories,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  selectedAttributes,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  setSelectedAttributes,
  minPrice,
  setMinPrice,
  maxPrice,
  setMaxPrice,
  inStock = false,
  setInStock = () => {},
  rating = null,
  setRating = () => {},
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  sortBy = "",
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  setSortBy = () => {},
  brands = [],
  setBrands = () => {},
}: DynamicFilterSidebarProps) {
  const [priceRange, setPriceRange] = useState<[number, number]>([minPrice, maxPrice === Infinity ? 10000 : maxPrice]);

  const handlePriceChange = (values: number[]) => {
    setPriceRange([values[0], values[1]]);
  };

  const applyPriceFilter = () => {
    setMinPrice(priceRange[0]);
    setMaxPrice(priceRange[1]);
  };

  const handleBrandToggle = (brand: string) => {
    if (brands.includes(brand)) {
      setBrands(brands.filter((b) => b !== brand));
    } else {
      setBrands([...brands, brand]);
    }
  };

  // Sample brands - in a real app, these would come from your API
  const availableBrands = [
    "Apple",
    "Samsung",
    "Sony",
    "LG",
    "Bose",
    "Nike",
    "Adidas",
    "Puma",
  ];

  // Get active filter count
  const getActiveFilterCount = () => {
    let count = 0;
    if (minPrice > 0 || maxPrice !== Infinity) count++;
    if (inStock) count++;
    if (rating) count++;
    if (brands.length > 0) count++;
    return count;
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-md md:shadow-xl rounded-xl md:rounded-2xl overflow-hidden">
        <CardHeader className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white p-3 md:p-6">
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 md:h-5 md:w-5" />
              <span className="text-sm md:text-base" style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}>
                Filters
              </span>
            </div>
            {getActiveFilterCount() > 0 && (
              <Badge variant="secondary" className="bg-white/20 text-white border-white/30 text-xs">
                {getActiveFilterCount()}
              </Badge>
            )}
          </CardTitle>
        </CardHeader>

        <CardContent className="p-3 md:p-6 space-y-3 md:space-y-6">
          {/* Quick Actions - Mobile Optimized */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              className="flex-1 border-purple-200 text-purple-600 hover:bg-purple-50 text-xs md:text-sm"
              onClick={() => {
                setMinPrice(0);
                setMaxPrice(Infinity);
                setInStock(false);
                setRating(null);
                setBrands([]);
              }}
            >
              <X className="h-3 w-3 md:h-4 md:w-4 mr-1" />
              Clear All
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="border-green-200 text-green-600 hover:bg-green-50 text-xs md:text-sm"
              onClick={() => setInStock(true)}
            >
              <Package className="h-3 w-3 md:h-4 md:w-4 mr-1" />
              In Stock
            </Button>
          </div>

          <Accordion type="multiple" defaultValue={["price", "availability"]} className="space-y-2 md:space-y-4">
            {/* Price Range Filter */}
            <AccordionItem value="price" className="border border-gray-200 rounded-lg md:rounded-xl overflow-hidden">
              <AccordionTrigger className="px-3 md:px-4 py-2 md:py-3 hover:bg-gray-50 [&[data-state=open]]:bg-purple-50">
                <div className="flex items-center gap-2">
                  <DollarSign className="h-3 w-3 md:h-4 md:w-4 text-purple-600" />
                  <span className="font-medium text-sm md:text-base">Price Range</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-3 md:px-4 pb-3 md:pb-4">
                <div className="space-y-3 md:space-y-4">
                  <div className="px-1 md:px-2">
                    <Slider
                      defaultValue={[priceRange[0], priceRange[1]]}
                      max={10000}
                      step={100}
                      value={[priceRange[0], priceRange[1]]}
                      onValueChange={handlePriceChange}
                      className="[&_[role=slider]]:bg-purple-600 [&_[role=slider]]:border-purple-600"
                    />
                  </div>
                  <div className="flex items-center justify-between text-xs md:text-sm text-gray-600">
                    <Badge variant="outline" className="border-purple-200 text-purple-700 text-xs">
                      R{priceRange[0].toLocaleString()}
                    </Badge>
                    <span className="text-xs">to</span>
                    <Badge variant="outline" className="border-purple-200 text-purple-700 text-xs">
                      R{priceRange[1] === 10000 ? "10,000+" : priceRange[1].toLocaleString()}
                    </Badge>
                  </div>
                  <Button
                    size="sm"
                    onClick={applyPriceFilter}
                    className="w-full bg-purple-600 hover:bg-purple-700 text-xs md:text-sm"
                  >
                    Apply Price Filter
                  </Button>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Availability Filter */}
            <AccordionItem value="availability" className="border border-gray-200 rounded-xl overflow-hidden">
              <AccordionTrigger className="px-4 py-3 hover:bg-gray-50 [&[data-state=open]]:bg-green-50">
                <div className="flex items-center gap-2">
                  <ShieldCheck className="h-4 w-4 text-green-600" />
                  <span className="font-medium">Availability</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4">
                <div className="space-y-3">
                  <div className="flex items-center space-x-3 p-3 rounded-lg border border-gray-100 hover:bg-green-50 transition-colors">
                    <Checkbox
                      id="in-stock"
                      checked={inStock}
                      onCheckedChange={(checked) => setInStock(checked as boolean)}
                      className="data-[state=checked]:bg-green-600 data-[state=checked]:border-green-600"
                    />
                    <Label htmlFor="in-stock" className="flex items-center gap-2 cursor-pointer">
                      <Package className="h-4 w-4 text-green-600" />
                      <span>In Stock Only</span>
                    </Label>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Rating Filter - Hidden on mobile */}
            <AccordionItem value="rating" className="hidden md:block border border-gray-200 rounded-xl overflow-hidden">
              <AccordionTrigger className="px-4 py-3 hover:bg-gray-50 [&[data-state=open]]:bg-yellow-50">
                <div className="flex items-center gap-2">
                  <Award className="h-4 w-4 text-yellow-600" />
                  <span className="font-medium">Customer Rating</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4">
                <RadioGroup
                  value={rating?.toString() || ""}
                  onValueChange={(value) => setRating(value ? parseInt(value) : null)}
                  className="space-y-2"
                >
                  {[5, 4, 3, 2, 1].map((star) => (
                    <div key={star} className="flex items-center space-x-3 p-2 rounded-lg hover:bg-yellow-50 transition-colors">
                      <RadioGroupItem
                        value={star.toString()}
                        id={`rating-${star}`}
                        className="data-[state=checked]:bg-yellow-600 data-[state=checked]:border-yellow-600"
                      />
                      <Label htmlFor={`rating-${star}`} className="flex items-center cursor-pointer">
                        <div className="flex items-center">
                          {[...Array(star)].map((_, i) => (
                            <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                          ))}
                          {[...Array(5 - star)].map((_, i) => (
                            <Star key={i} className="h-4 w-4 text-gray-300" />
                          ))}
                        </div>
                        <span className="ml-2 text-sm text-gray-600">& Up</span>
                      </Label>
                    </div>
                  ))}
                </RadioGroup>
              </AccordionContent>
            </AccordionItem>

            {/* Brands Filter - Hidden on mobile */}
            <AccordionItem value="brands" className="hidden md:block border border-gray-200 rounded-xl overflow-hidden">
              <AccordionTrigger className="px-4 py-3 hover:bg-gray-50 [&[data-state=open]]:bg-blue-50">
                <div className="flex items-center gap-2">
                  <Tag className="h-4 w-4 text-blue-600" />
                  <span className="font-medium">Brands</span>
                  {brands.length > 0 && (
                    <Badge variant="secondary" className="ml-auto bg-blue-100 text-blue-700">
                      {brands.length}
                    </Badge>
                  )}
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4">
                <div className="space-y-2 max-h-48 overflow-y-auto">
                  {availableBrands.map((brand) => (
                    <div key={brand} className="flex items-center space-x-3 p-2 rounded-lg hover:bg-blue-50 transition-colors">
                      <Checkbox
                        id={`brand-${brand}`}
                        checked={brands.includes(brand)}
                        onCheckedChange={() => handleBrandToggle(brand)}
                        className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                      />
                      <Label htmlFor={`brand-${brand}`} className="cursor-pointer flex-1">
                        {brand}
                      </Label>
                      {brands.includes(brand) && (
                        <Sparkles className="h-3 w-3 text-blue-600" />
                      )}
                    </div>
                  ))}
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </CardContent>
      </Card>
    </motion.div>
  );
}
