"use client"

import { ProductCard } from "./ProductCard"
import { ProductListCard } from "./ProductListCard"
import { NoProducts } from "./NoProducts"
import type { Product } from "@/types/product"
import { motion } from "framer-motion"

interface ProductGridProps {
  products: Product[]
  isLoading: boolean
  selectedCategory?: string
  onResetCategory?: () => void
  viewMode?: 'grid' | 'list'
}

export function ProductGrid({ products, isLoading, selectedCategory, onResetCategory, viewMode = 'grid' }: ProductGridProps) {
  // Enhanced loading skeleton
  if (isLoading) {
    return (
      <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-8">
        <div className="text-center py-12">
          <div className="animate-pulse flex flex-col items-center">
            <div className="h-8 w-40 bg-gradient-to-r from-gray-200 to-gray-300 rounded-lg mb-6"></div>
            <div className={viewMode === 'grid'
              ? "grid grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-6 lg:gap-8 w-full"
              : "space-y-3 md:space-y-4 w-full"
            }>
              {[...Array(viewMode === 'grid' ? 6 : 6)].map((_, i) => (
                <motion.div
                  key={i}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: i * 0.05 }}
                  className={viewMode === 'grid'
                    ? "bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg md:rounded-xl h-64 md:h-80"
                    : "bg-gradient-to-r from-gray-100 to-gray-200 rounded-lg md:rounded-xl h-24 md:h-32"
                  }
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-8">
      {products.length > 0 ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          {/* Results header */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">
              {products.length} product{products.length !== 1 ? 's' : ''} found
            </h2>
            <div className="text-sm text-gray-500">
              {selectedCategory && selectedCategory !== "All" && (
                <span>in {selectedCategory}</span>
              )}
            </div>
          </div>

          {/* Products display - Mobile optimized grid */}
          {viewMode === 'grid' ? (
            <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-6 lg:gap-8">
              {products.map((product, index) => (
                <motion.div
                  key={product._id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05, duration: 0.3 }}
                >
                  <ProductCard product={product} />
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="space-y-3 md:space-y-4">
              {products.map((product, index) => (
                <motion.div
                  key={product._id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.03, duration: 0.3 }}
                >
                  <ProductListCard product={product} />
                </motion.div>
              ))}
            </div>
          )}
        </motion.div>
      ) : (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          <NoProducts
            categoryName={selectedCategory === "All" ? "any category" : selectedCategory}
            onReset={() => onResetCategory ? onResetCategory() : null}
          />
        </motion.div>
      )}
    </div>
  )
}