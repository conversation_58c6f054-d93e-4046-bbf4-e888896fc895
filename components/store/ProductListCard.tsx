"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { ShoppingCart, CheckCircle, Eye, Star, Heart } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useAddToCartMutation } from "@/lib/redux/features/cart/cartApiSlice";
import { useAuth } from "@/context/AuthContext";
import { useGroupMembership } from "@/lib/redux/hooks/useGroupMembership";
import type { Product } from "@/types/product";
import { ConfirmationModal } from "@/components/ConfirmationModal";
import { useRouter } from "next/navigation";
import { JoinGroupModal } from "@/components/modals/JoinGroupModal";

interface ProductListCardProps {
  product: Product;
}

export function ProductListCard({ product }: ProductListCardProps) {
  const [addToCart, { isLoading: isAdding }] = useAddToCartMutation();
  const [success, setSuccess] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showJoinModal, setShowJoinModal] = useState(false);
  const { user } = useAuth();
  const router = useRouter();
  const { activeGroupId, isLoading: membershipLoading } = useGroupMembership();

  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => setSuccess(false), 2000);
      return () => clearTimeout(timer);
    }
  }, [success]);

  const getButtonText = () => {
    if (isAdding) return "Adding...";
    if (success) return "Added!";
    if (!user || !activeGroupId) return "Join Group to Shop";
    return "Add to Cart";
  };

  const handleAddToCart = async () => {
    if (!user || !activeGroupId) {
      setShowJoinModal(true);
      return;
    }

    if (membershipLoading) return;

    setShowConfirmation(true);
  };

  const confirmAddToCart = async () => {
    try {
      await addToCart({
        groupId: activeGroupId!,
        productId: product._id,
        quantity: 1,
      }).unwrap();
      setSuccess(true);
      setShowConfirmation(false);
    } catch (error: any) {
      console.error('Failed to add to cart:', error);
      
      let errorMessage = 'Failed to add product to cart. Please try again.';
      if (error?.data?.message) {
        errorMessage = error.data.message;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      alert(errorMessage);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <>
      <Card className="group overflow-hidden border-none shadow-md hover:shadow-xl transition-all duration-300 rounded-xl bg-white/80 backdrop-blur-sm">
        <CardContent className="p-6">
          <div className="flex gap-6">
            {/* Product Image */}
            <div className="relative w-32 h-32 flex-shrink-0 overflow-hidden rounded-lg bg-gray-50">
              <Image
                src={`/api/images/${product.image}`}
                alt={product.name}
                fill
                className="object-contain p-2 group-hover:scale-105 transition-transform duration-300"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = "/placeholder.svg";
                }}
              />
            </div>

            {/* Product Details */}
            <div className="flex-1 min-w-0">
              <div className="flex justify-between items-start mb-2">
                <div className="flex-1">
                  <h3 
                    className="font-semibold text-lg text-gray-900 group-hover:text-purple-600 transition-colors duration-200 line-clamp-2"
                    style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
                  >
                    {product.name}
                  </h3>
                  <p className="text-sm text-gray-500 mb-2">
                    {product.category && typeof product.category !== 'string' && 'name' in product.category
                      ? product.category.name
                      : 'Uncategorized'}
                  </p>
                </div>
                
                {/* Stock Badge */}
                <Badge 
                  variant={product.stock > 0 ? "default" : "destructive"}
                  className="ml-4"
                >
                  {product.stock > 0 ? `${product.stock} in stock` : 'Out of stock'}
                </Badge>
              </div>

              {/* Description */}
              <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                {product.description || "No description available"}
              </p>

              {/* Rating */}
              {product.rating && (
                <div className="flex items-center gap-2 mb-4">
                  <div className="flex">
                    {renderStars(Math.round(product.rating))}
                  </div>
                  <span className="text-sm text-gray-500">
                    ({product.rating.toFixed(1)})
                  </span>
                </div>
              )}

              {/* Price and Actions */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <span className="text-2xl font-bold text-gray-900">
                    {formatPrice(product.price)}
                  </span>
                  {product.originalPrice && product.originalPrice > product.price && (
                    <span className="text-lg text-gray-500 line-through">
                      {formatPrice(product.originalPrice)}
                    </span>
                  )}
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-10 w-10 rounded-full hover:bg-purple-50 hover:border-purple-300"
                    onClick={() => router.push(`/products/${product._id}`)}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-10 w-10 rounded-full hover:bg-red-50 hover:border-red-300"
                  >
                    <Heart className="h-4 w-4" />
                  </Button>

                  <Button
                    onClick={handleAddToCart}
                    disabled={isAdding || product.stock === 0}
                    className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-full transition-all duration-200 hover:scale-105"
                  >
                    {success ? (
                      <CheckCircle className="h-4 w-4 mr-2" />
                    ) : (
                      <ShoppingCart className="h-4 w-4 mr-2" />
                    )}
                    {getButtonText()}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <ConfirmationModal
        isOpen={showConfirmation}
        onClose={() => setShowConfirmation(false)}
        onConfirm={confirmAddToCart}
        title="Add to Cart"
        message={`Are you sure you want to add "${product.name}" to your cart?`}
        confirmText="Add to Cart"
        cancelText="Cancel"
      />

      <JoinGroupModal
        isOpen={showJoinModal}
        onClose={() => setShowJoinModal(false)}
      />
    </>
  );
}
