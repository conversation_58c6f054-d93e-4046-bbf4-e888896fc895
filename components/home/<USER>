"use client"

import { useRef, useEffect, useState, useCallback } from 'react'
import { motion } from 'framer-motion'
import { But<PERSON> } from '@/components/ui/button'
import { Play, Users, TrendingUp, Heart } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { JoinGroupModal } from '@/components/modals/JoinGroupModal'

export function FullWidthVideo() {
  const videoRef = useRef<HTMLVideoElement>(null)
  const router = useRouter()
  const [isJoinGroupOpen, setIsJoinGroupOpen] = useState(false)

  const openJoinGroupModal = useCallback(() => {
    setIsJoinGroupOpen(true)
  }, [])

  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.play().catch(error => {
        console.error("Auto-play was prevented:", error)
      })
    }
  }, [])

  return (
    <section className="relative w-full h-screen min-h-[600px] overflow-hidden">
      <video
        ref={videoRef}
        className="absolute top-0 left-0 w-full h-full object-cover"
        autoPlay
        muted
        loop
        playsInline
        poster="/video-poster.jpg"
      >
        <source src="/2016731-hd_1920_1080_30fps.mp4" type="video/mp4" />
        Your browser does not support the video tag.
      </video>

      {/* Modern Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-black/70 via-black/50 to-black/70" />

      {/* Pattern Overlay */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.3'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3Ccircle cx='10' cy='10' r='1'/%3E%3Ccircle cx='50' cy='10' r='1'/%3E%3Ccircle cx='10' cy='50' r='1'/%3E%3Ccircle cx='50' cy='50' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      {/* Content */}
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="text-center text-white px-4 sm:px-6 lg:px-8 max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-8"
          >
            <h2
              className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold leading-tight"
              style={{
                fontFamily: 'ClashDisplay-Variable, sans-serif',
                letterSpacing: '-0.03em'
              }}
            >
              Experience the
              <span className="block text-[#7FDBCA]">Future of Shopping</span>
            </h2>

            <div className="w-32 h-1 bg-gradient-to-r from-[#7FDBCA] to-white mx-auto rounded-full" />

            <p
              className="text-lg sm:text-xl md:text-2xl lg:text-3xl max-w-4xl mx-auto leading-relaxed text-white/90"
              style={{ fontFamily: 'Avenir, sans-serif' }}
            >
              Join our thriving community and discover how collective buying power
              is transforming grocery shopping across South Africa
            </p>

            {/* Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="grid grid-cols-1 sm:grid-cols-3 gap-6 sm:gap-8 max-w-2xl mx-auto mt-8 sm:mt-12"
            >
              <div className="text-center">
                <div className="h-16 w-16 bg-white/10 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-3 border border-white/20">
                  <Users className="h-8 w-8 text-[#7FDBCA]" />
                </div>
                <h3 className="text-xl sm:text-2xl md:text-3xl font-bold text-white mb-1" style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}>
                  10K+
                </h3>
                <p className="text-white/70 text-sm" style={{ fontFamily: 'Avenir, sans-serif' }}>
                  Active Members
                </p>
              </div>

              <div className="text-center">
                <div className="h-16 w-16 bg-white/10 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-3 border border-white/20">
                  <TrendingUp className="h-8 w-8 text-[#7FDBCA]" />
                </div>
                <h3 className="text-xl sm:text-2xl md:text-3xl font-bold text-white mb-1" style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}>
                  35%
                </h3>
                <p className="text-white/70 text-sm" style={{ fontFamily: 'Avenir, sans-serif' }}>
                  Average Savings
                </p>
              </div>

              <div className="text-center">
                <div className="h-16 w-16 bg-white/10 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-3 border border-white/20">
                  <Heart className="h-8 w-8 text-[#7FDBCA]" />
                </div>
                <h3 className="text-xl sm:text-2xl md:text-3xl font-bold text-white mb-1" style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}>
                  500+
                </h3>
                <p className="text-white/70 text-sm" style={{ fontFamily: 'Avenir, sans-serif' }}>
                  Communities
                </p>
              </div>
            </motion.div>

            {/* Call to Action */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center items-center mt-8 sm:mt-12"
            >
              <Button
                onClick={openJoinGroupModal}
                className="group relative overflow-hidden bg-gradient-to-r from-[#7FDBCA] to-[#2A7C6C] hover:from-[#2A7C6C] hover:to-[#7FDBCA] text-white rounded-full px-6 sm:px-10 py-3 sm:py-4 text-base sm:text-lg font-semibold shadow-2xl hover:shadow-3xl transform hover:-translate-y-1 transition-all duration-300 w-full sm:w-auto"
                style={{ fontFamily: 'Avenir, sans-serif' }}
              >
                <span className="relative z-10 flex items-center">
                  <Heart className="h-5 w-5 mr-2" />
                  Join the Movement
                </span>
              </Button>

              <Button
                variant="outline"
                onClick={() => router.push('/aboutus')}
                className="border-2 border-white/80 text-white bg-white/10 backdrop-blur-sm hover:bg-white hover:text-[#2A7C6C] hover:border-white rounded-full px-6 sm:px-10 py-3 sm:py-4 text-base sm:text-lg font-semibold transition-all duration-300 shadow-lg w-full sm:w-auto"
                style={{ fontFamily: 'Avenir, sans-serif' }}
              >
                <Play className="h-5 w-5 mr-2" />
                Watch Our Story
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </div>

      {/* Floating Elements */}
      <div className="absolute top-20 left-20 w-32 h-32 bg-[#7FDBCA]/20 rounded-full blur-3xl" />
      <div className="absolute bottom-20 right-20 w-40 h-40 bg-white/10 rounded-full blur-3xl" />

      {/* Modal */}
      <JoinGroupModal isOpen={isJoinGroupOpen} onClose={() => setIsJoinGroupOpen(false)} />
    </section>
  )
}

