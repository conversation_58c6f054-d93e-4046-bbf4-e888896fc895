


"use client"

import { useState, useCallback } from "react"
import Image from "next/image"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, TrendingUp, Heart, Sparkles, ArrowRight, Star } from "lucide-react"
import { JoinGroupModal } from "@/components/modals/JoinGroupModal"

export function AboutSection() {
  const [isJoinGroupOpen, setIsJoinGroupOpen] = useState(false)

  const openJoinGroupModal = useCallback(() => {
    setIsJoinGroupOpen(true)
  }, [])

  return (
    <section className="relative py-16 sm:py-32 bg-gradient-to-br from-[#2A7C6C] via-[#2A7C6C] to-[#1E5A4F] text-white px-4 md:px-6 overflow-hidden">
      {/* Enhanced Pattern Background */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.4'%3E%3Cpath d='M0 0h80v80H0V0zm20 20v40h40V20H20zm20 35a15 15 0 1 1 0-30 15 15 0 0 1 0 30z' fill-rule='nonzero'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      {/* Floating Gradient Orbs */}
      <div className="absolute top-10 left-10 w-40 h-40 bg-gradient-to-r from-[#7FDBCA]/30 to-transparent rounded-full blur-3xl animate-pulse" />
      <div className="absolute bottom-10 right-10 w-60 h-60 bg-gradient-to-l from-white/20 to-transparent rounded-full blur-3xl" />
      <div className="absolute top-1/2 left-1/3 w-32 h-32 bg-gradient-to-r from-[#7FDBCA]/20 to-transparent rounded-full blur-2xl animate-bounce" style={{ animationDuration: '3s' }} />

      {/* Decorative Elements */}
      <div className="absolute top-20 right-20 w-4 h-4 bg-[#7FDBCA] rounded-full opacity-60 animate-ping" />
      <div className="absolute bottom-32 left-16 w-2 h-2 bg-white rounded-full opacity-40" />
      <div className="absolute top-40 left-1/4 w-3 h-3 bg-[#7FDBCA]/60 rounded-full animate-pulse" />

      <div className="container mx-auto relative z-10">
        <div className="grid md:grid-cols-2 gap-8 sm:gap-12 lg:gap-20 items-center">
          {/* Enhanced Image Section */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="relative aspect-[4/4] w-full group"
          >
            {/* Floating Badge */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="absolute -top-4 -right-4 z-20"
            >
              <Badge className="bg-gradient-to-r from-[#7FDBCA] to-[#2A7C6C] text-white border-0 px-4 py-2 text-sm font-semibold shadow-xl">
                <Star className="h-4 w-4 mr-1" />
                Premium Experience
              </Badge>
            </motion.div>

            {/* Enhanced Image Container */}
            <div className="relative h-full">
              <div className="absolute inset-0 bg-gradient-to-tr from-white/30 to-transparent rounded-3xl group-hover:from-[#7FDBCA]/20 transition-all duration-500" />
              <div className="absolute -inset-4 bg-gradient-to-r from-[#7FDBCA]/20 to-[#2A7C6C]/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500" />
              <Image
                src="/products/landingImage.png"
                alt="Community-driven shopping experience"
                fill
                className="object-cover rounded-3xl shadow-2xl group-hover:shadow-3xl transition-all duration-500 relative z-10"
                sizes="(max-width: 768px) 100vw, 50vw"
                priority
              />

              {/* Floating Stats */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
                className="absolute bottom-6 left-6 right-6 z-20"
              >
                <div className="bg-white/95 backdrop-blur-xl rounded-2xl p-4 shadow-xl border border-white/20">
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="flex items-center justify-center mb-1">
                        <Users className="h-4 w-4 text-[#2A7C6C] mr-1" />
                        <span className="text-lg font-bold text-[#2A7C6C]">10K+</span>
                      </div>
                      <p className="text-xs text-gray-600">Members</p>
                    </div>
                    <div>
                      <div className="flex items-center justify-center mb-1">
                        <TrendingUp className="h-4 w-4 text-[#2A7C6C] mr-1" />
                        <span className="text-lg font-bold text-[#2A7C6C]">35%</span>
                      </div>
                      <p className="text-xs text-gray-600">Savings</p>
                    </div>
                    <div>
                      <div className="flex items-center justify-center mb-1">
                        <Heart className="h-4 w-4 text-[#2A7C6C] mr-1" />
                        <span className="text-lg font-bold text-[#2A7C6C]">500+</span>
                      </div>
                      <p className="text-xs text-gray-600">Groups</p>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>

          {/* Enhanced Content Section */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="space-y-8"
          >
            {/* Header with Animation */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="space-y-6"
            >
              {/* Decorative Badge */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                className="inline-flex items-center gap-2 bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full border border-white/20"
              >
                <Sparkles className="h-4 w-4 text-[#7FDBCA]" />
                <span className="text-white/90 text-sm font-medium" style={{ fontFamily: "Avenir, sans-serif" }}>
                  Transforming Communities
                </span>
              </motion.div>

              <h1
                className="text-white text-3xl sm:text-5xl md:text-6xl lg:text-7xl font-bold leading-tight"
                style={{
                  fontFamily: "ClashDisplay-Variable, sans-serif",
                  letterSpacing: "-0.03em",
                }}
              >
                <motion.span
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.3 }}
                  className="block"
                >
                  Redefining
                </motion.span>
                <motion.span
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                  className="block text-[#7FDBCA] relative"
                >
                  Community Commerce
                  {/* Decorative underline */}
                  <motion.div
                    initial={{ width: 0 }}
                    whileInView={{ width: "100%" }}
                    transition={{ duration: 0.8, delay: 0.6 }}
                    className="absolute -bottom-2 left-0 h-1 bg-gradient-to-r from-[#7FDBCA] to-white rounded-full"
                  />
                </motion.span>
              </h1>

              <motion.h2
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
                className="text-white/90 text-lg sm:text-xl md:text-2xl font-medium"
                style={{
                  fontFamily: "ClashDisplay-Variable, sans-serif",
                  letterSpacing: "-0.01em",
                }}
              >
                Where neighbors become shopping partners
              </motion.h2>
            </motion.div>

            {/* Enhanced Description */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="space-y-6"
            >
              <p className="text-white/90 text-base sm:text-lg md:text-xl leading-relaxed" style={{ fontFamily: "Avenir, sans-serif" }}>
                We&apos;re transforming how South Africa shops by connecting communities through collective buying power.
                Our platform turns everyday grocery shopping into a shared experience that saves money and builds relationships.
              </p>

              <p className="text-white/80 text-base sm:text-lg md:text-xl leading-relaxed" style={{ fontFamily: "Avenir, sans-serif" }}>
                Shop together while maintaining complete privacy in your grocery choices. Your neighbors don&apos;t need to know what you purchased.
                Leverage social connections and community buying power to save money while selecting your own unique grocery items.
                Each member enjoys individual choice—your purchases remain private and personal.
              </p>

              <p className="text-white/80 text-base sm:text-lg md:text-xl leading-relaxed" style={{ fontFamily: "Avenir, sans-serif" }}>
                Experience the future of retail where technology meets tradition, creating meaningful savings
                while strengthening the fabric of our communities across the nation.
              </p>

              {/* Feature Highlights */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4 mt-6 sm:mt-8">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.7 }}
                  className="flex items-center gap-2 sm:gap-3 bg-white/10 backdrop-blur-sm p-3 sm:p-4 rounded-xl border border-white/20"
                >
                  <div className="h-8 w-8 sm:h-10 sm:w-10 bg-[#7FDBCA] rounded-lg flex items-center justify-center flex-shrink-0">
                    <Users className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
                  </div>
                  <div className="min-w-0">
                    <p className="text-white font-semibold text-xs sm:text-sm">Community First</p>
                    <p className="text-white/70 text-xs">Local connections</p>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.8 }}
                  className="flex items-center gap-2 sm:gap-3 bg-white/10 backdrop-blur-sm p-3 sm:p-4 rounded-xl border border-white/20"
                >
                  <div className="h-8 w-8 sm:h-10 sm:w-10 bg-[#7FDBCA] rounded-lg flex items-center justify-center flex-shrink-0">
                    <TrendingUp className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
                  </div>
                  <div className="min-w-0">
                    <p className="text-white font-semibold text-xs sm:text-sm">Smart Savings</p>
                    <p className="text-white/70 text-xs">Up to 35% off</p>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.9 }}
                  className="flex items-center gap-2 sm:gap-3 bg-white/10 backdrop-blur-sm p-3 sm:p-4 rounded-xl border border-white/20"
                >
                  <div className="h-8 w-8 sm:h-10 sm:w-10 bg-[#7FDBCA] rounded-lg flex items-center justify-center flex-shrink-0">
                    <Heart className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
                  </div>
                  <div className="min-w-0">
                    <p className="text-white font-semibold text-xs sm:text-sm">Trusted Platform</p>
                    <p className="text-white/70 text-xs">Secure & reliable</p>
                  </div>
                </motion.div>
              </div>
            </motion.div>

            {/* Enhanced CTA Section */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.0 }}
              className="pt-6 sm:pt-8"
            >
              <div className="flex flex-col sm:flex-row gap-4 items-start">
                <Button
                  onClick={openJoinGroupModal}
                  className="group relative overflow-hidden rounded-full px-6 sm:px-10 py-3 sm:py-4 text-base sm:text-lg font-semibold bg-white text-[#2A7C6C] hover:bg-white/95 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1 hover:scale-105 w-full sm:w-auto"
                  style={{ fontFamily: "Avenir, sans-serif" }}
                >
                  <span className="relative z-10 flex items-center gap-2">
                    Join Community
                    <ArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-[#7FDBCA] to-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                  {/* Floating sparkles */}
                  <div className="absolute -top-1 -right-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <Sparkles className="h-4 w-4 text-[#2A7C6C] animate-pulse" />
                  </div>
                </Button>

                {/* Trust indicators */}
                <div className="flex items-center gap-2 text-white/70 text-sm mt-2 sm:mt-0">
                  <div className="flex -space-x-2">
                    <div className="w-8 h-8 bg-gradient-to-r from-[#7FDBCA] to-[#2A7C6C] rounded-full border-2 border-white flex items-center justify-center">
                      <span className="text-white text-xs font-bold">10K</span>
                    </div>
                    <div className="w-8 h-8 bg-gradient-to-r from-[#2A7C6C] to-[#1E5A4F] rounded-full border-2 border-white flex items-center justify-center">
                      <Heart className="h-3 w-3 text-white" />
                    </div>
                  </div>
                  <span style={{ fontFamily: "Avenir, sans-serif" }}>
                    Join 10,000+ happy members
                  </span>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </div>

      {/* Modal */}
      <JoinGroupModal isOpen={isJoinGroupOpen} onClose={() => setIsJoinGroupOpen(false)} />
    </section>
  )
}

