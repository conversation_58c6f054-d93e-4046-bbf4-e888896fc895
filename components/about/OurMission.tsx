export function OurMission() {
    return (
      <section className="relative py-32 px-4 md:px-6 bg-gradient-to-br from-slate-50 via-white to-slate-100 text-[#2F4858] overflow-hidden">
        {/* Geometric Pattern Background */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%232A7C6C' fill-opacity='0.4'%3E%3Cpolygon points='50 0 60 40 100 50 60 60 50 100 40 60 0 50 40 40'/%3E%3C/g%3E%3C/svg%3E")`,
          }} />
        </div>

        {/* Floating Elements */}
        <div className="absolute top-20 left-10 w-20 h-20 bg-[#2A7C6C]/10 rounded-full blur-xl" />
        <div className="absolute bottom-20 right-10 w-32 h-32 bg-[#7FDBCA]/20 rounded-full blur-2xl" />
        <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-[#2A7C6C]/5 rounded-full blur-lg" />

        <div className="container mx-auto relative z-10">
          <div className="text-center mb-16">
            <div className="inline-block p-4 bg-[#2A7C6C]/10 rounded-2xl mb-8">
              <h2
                className="text-5xl md:text-6xl lg:text-7xl font-bold bg-gradient-to-r from-[#2A7C6C] to-[#1E5A4F] bg-clip-text text-transparent"
                style={{
                  fontFamily: 'ClashDisplay-Variable, sans-serif',
                  letterSpacing: '-0.03em'
                }}
              >
                Our Purpose
              </h2>
            </div>

            <div className="w-24 h-1 bg-gradient-to-r from-[#2A7C6C] to-[#7FDBCA] mx-auto mb-12 rounded-full" />
          </div>

          <div className="max-w-5xl mx-auto">
            <div className="grid md:grid-cols-2 gap-12 items-center">
              <div className="space-y-8">
                <div className="bg-white/70 backdrop-blur-sm p-8 rounded-3xl shadow-lg border border-white/20">
                  <h3
                    className="text-2xl md:text-3xl font-bold mb-6 text-[#2A7C6C]"
                    style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
                  >
                    Empowering Communities
                  </h3>
                  <p
                    className="text-lg md:text-xl leading-relaxed text-gray-700"
                    style={{ fontFamily: 'Avenir, sans-serif' }}
                  >
                    We believe in the transformative power of collective action. By bringing neighbors together
                    through shared shopping experiences, we&apos;re not just reducing costs—we&apos;re rebuilding
                    the connections that make communities thrive.
                  </p>
                </div>
              </div>

              <div className="space-y-8">
                <div className="bg-white/70 backdrop-blur-sm p-8 rounded-3xl shadow-lg border border-white/20">
                  <h3
                    className="text-2xl md:text-3xl font-bold mb-6 text-[#2A7C6C]"
                    style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
                  >
                    Accessible Innovation
                  </h3>
                  <p
                    className="text-lg md:text-xl leading-relaxed text-gray-700"
                    style={{ fontFamily: 'Avenir, sans-serif' }}
                  >
                    Technology should serve everyone. Our platform democratizes bulk buying benefits,
                    making premium groceries accessible to all families while fostering financial wellness
                    and community solidarity across South Africa.
                  </p>
                </div>
              </div>
            </div>

            <div className="mt-16 text-center">
              <div className="bg-gradient-to-r from-[#2A7C6C]/10 to-[#7FDBCA]/10 p-8 rounded-3xl border border-[#2A7C6C]/20">
                <p
                  className="text-xl md:text-2xl font-medium text-[#2F4858] leading-relaxed"
                  style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
                >
                  "Ubuntu: I am because we are. Together, we shop smarter, save more, and build stronger communities."
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    )
  }
  
  