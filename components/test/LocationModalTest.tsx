// components/test/LocationModalTest.tsx

"use client";

import { useState } from "react";
import { LocationSelectionButton } from "@/components/ui/LocationSelectionButton";
import { CompleteLocationSelector } from "@/components/ui/enhanced-location-selects-v2";
import type { LocationSelectionResult } from "@/components/modals/LocationSelectionModal";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MapPin, CheckCircle } from "lucide-react";

export function LocationModalTest() {
  const [selectedLocation, setSelectedLocation] = useState<LocationSelectionResult | null>(null);
  const [selectedLocation2, setSelectedLocation2] = useState<LocationSelectionResult | null>(null);

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-gray-900">
          Location Selection Modal Test
        </h1>
        <p className="text-gray-600">
          Test the new modal-based location selection system
        </p>
      </div>

      {/* Test 1: Standard Location Selection Button */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5 text-blue-600" />
            Standard Location Selection
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <LocationSelectionButton
            value={selectedLocation}
            onLocationSelect={setSelectedLocation}
            placeholder="Select your location"
            size="lg"
            showFullPath={true}
            required={true}
          />
          
          {selectedLocation && (
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                <div className="space-y-2">
                  <h4 className="font-medium text-green-900">
                    Location Selected Successfully!
                  </h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">Province</Badge>
                      <span>{selectedLocation.provinceName}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">City</Badge>
                      <span>{selectedLocation.cityName}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">Township</Badge>
                      <span>{selectedLocation.townshipName}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">Location</Badge>
                      <span>{selectedLocation.locationName}</span>
                    </div>
                  </div>
                  <div className="mt-2 p-2 bg-white rounded border">
                    <p className="text-xs text-gray-600">Full Path:</p>
                    <p className="font-mono text-sm">{selectedLocation.fullPath}</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Test 2: Complete Location Selector */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5 text-purple-600" />
            Complete Location Selector
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <CompleteLocationSelector
            value={selectedLocation2}
            onLocationSelect={setSelectedLocation2}
            placeholder="Choose your complete location"
            size="md"
            showFullPath={false}
          />
          
          {selectedLocation2 && (
            <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-purple-600 mt-0.5" />
                <div className="space-y-2">
                  <h4 className="font-medium text-purple-900">
                    Complete Location Selected!
                  </h4>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>
                      <span className="font-medium">Province ID:</span>
                      <p className="font-mono text-xs">{selectedLocation2.provinceId}</p>
                    </div>
                    <div>
                      <span className="font-medium">City ID:</span>
                      <p className="font-mono text-xs">{selectedLocation2.cityId}</p>
                    </div>
                    <div>
                      <span className="font-medium">Township ID:</span>
                      <p className="font-mono text-xs">{selectedLocation2.townshipId}</p>
                    </div>
                    <div>
                      <span className="font-medium">Location ID:</span>
                      <p className="font-mono text-xs">{selectedLocation2.locationId}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Test 3: Mobile Simulation */}
      <Card>
        <CardHeader>
          <CardTitle>Mobile Simulation Test</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="max-w-sm mx-auto border-2 border-gray-300 rounded-2xl p-4 bg-gray-50">
            <div className="space-y-4">
              <h3 className="text-center font-medium">Mobile View</h3>
              <LocationSelectionButton
                value={selectedLocation}
                onLocationSelect={setSelectedLocation}
                placeholder="Tap to select location"
                size="lg"
                showFullPath={true}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Debug Information */}
      <Card>
        <CardHeader>
          <CardTitle>Debug Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">Selected Location 1:</h4>
              <pre className="text-xs bg-gray-100 p-3 rounded overflow-auto">
                {JSON.stringify(selectedLocation, null, 2)}
              </pre>
            </div>
            <div>
              <h4 className="font-medium mb-2">Selected Location 2:</h4>
              <pre className="text-xs bg-gray-100 p-3 rounded overflow-auto">
                {JSON.stringify(selectedLocation2, null, 2)}
              </pre>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
