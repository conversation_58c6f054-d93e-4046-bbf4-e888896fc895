// components/modals/GroupRequestModal.tsx

"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X, Users, MapPin, AlertCircle, CheckCircle, Loader2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import type { LocationSelectionResult } from "./LocationSelectionModal";

interface GroupRequestModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: GroupRequestData) => Promise<void>;
  locationSelection: LocationSelectionResult;
  userEmail: string;
  userName: string;
  userPhone?: string;
}

export interface GroupRequestData {
  requestedGroupName: string;
  groupDescription?: string;
}

const modalVariants = {
  hidden: { 
    opacity: 0,
    scale: 0.95,
    y: 20
  },
  visible: { 
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      type: "spring",
      damping: 25,
      stiffness: 300,
      duration: 0.4
    }
  },
  exit: { 
    opacity: 0,
    scale: 0.95,
    y: 20,
    transition: {
      duration: 0.2
    }
  }
};

const overlayVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
  exit: { opacity: 0 }
};

export function GroupRequestModal({
  isOpen,
  onClose,
  onSubmit,
  locationSelection,
  userEmail,
  userName,
  userPhone
}: GroupRequestModalProps) {
  const [formData, setFormData] = useState<GroupRequestData>({
    requestedGroupName: "",
    groupDescription: ""
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Group name validation
    if (!formData.requestedGroupName.trim()) {
      newErrors.requestedGroupName = "Group name is required";
    } else if (formData.requestedGroupName.trim().length < 3) {
      newErrors.requestedGroupName = "Group name must be at least 3 characters";
    } else if (formData.requestedGroupName.trim().length > 50) {
      newErrors.requestedGroupName = "Group name must be less than 50 characters";
    } else if (!/^[a-zA-Z0-9\s\-_]+$/.test(formData.requestedGroupName.trim())) {
      newErrors.requestedGroupName = "Group name can only contain letters, numbers, spaces, hyphens, and underscores";
    }

    // Description validation (optional but if provided, must be reasonable length)
    if (formData.groupDescription && formData.groupDescription.trim().length > 500) {
      newErrors.groupDescription = "Description must be less than 500 characters";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    setErrors({});

    try {
      await onSubmit({
        requestedGroupName: formData.requestedGroupName.trim(),
        groupDescription: formData.groupDescription?.trim() || undefined
      });
      
      setSubmitSuccess(true);
      
      // Close modal after showing success message
      setTimeout(() => {
        onClose();
        setSubmitSuccess(false);
        setFormData({ requestedGroupName: "", groupDescription: "" });
      }, 2000);
      
    } catch (error) {
      console.error("Error submitting group request:", error);
      setErrors({ 
        submit: error instanceof Error ? error.message : "Failed to submit request. Please try again." 
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
      setFormData({ requestedGroupName: "", groupDescription: "" });
      setErrors({});
      setSubmitSuccess(false);
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        {/* Overlay */}
        <motion.div
          variants={overlayVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          onClick={handleClose}
        />

        {/* Modal */}
        <motion.div
          variants={modalVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          className="relative w-full max-w-md bg-white rounded-2xl shadow-2xl overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="px-6 py-4 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                  <Users className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 
                    className="text-lg font-semibold text-gray-900"
                    style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
                  >
                    Request New Group
                  </h3>
                  <p className="text-sm text-gray-500">Create a group for your location</p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleClose}
                disabled={isSubmitting}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>
          </div>

          {/* Content */}
          <div className="px-6 py-4">
            {submitSuccess ? (
              // Success State
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                className="text-center py-8"
              >
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">Request Submitted!</h4>
                <p className="text-sm text-gray-600">
                  Your group request has been sent to our admin team for review. 
                  You'll be notified once it's approved.
                </p>
              </motion.div>
            ) : (
              // Form State
              <>
                {/* Location Display */}
                <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-xl">
                  <div className="flex items-center gap-2 mb-2">
                    <MapPin className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium text-blue-800">Selected Location</span>
                  </div>
                  <p className="text-sm text-blue-700">{locationSelection.fullPath}</p>
                </div>

                {/* User Info Display */}
                <div className="mb-6 p-4 bg-gray-50 border border-gray-200 rounded-xl">
                  <h4 className="text-sm font-medium text-gray-800 mb-2">Request Details</h4>
                  <div className="space-y-1 text-sm text-gray-600">
                    <p><span className="font-medium">Name:</span> {userName}</p>
                    <p><span className="font-medium">Email:</span> {userEmail}</p>
                    {userPhone && <p><span className="font-medium">Phone:</span> {userPhone}</p>}
                  </div>
                </div>

                {/* Form */}
                <form onSubmit={handleSubmit} className="space-y-4">
                  {/* Group Name */}
                  <div className="space-y-2">
                    <Label htmlFor="groupName" className="text-sm font-medium text-gray-700">
                      Group Name *
                    </Label>
                    <Input
                      id="groupName"
                      type="text"
                      value={formData.requestedGroupName}
                      onChange={(e) => setFormData(prev => ({ ...prev, requestedGroupName: e.target.value }))}
                      placeholder="Enter your group name"
                      className={errors.requestedGroupName ? "border-red-300 focus:border-red-500" : ""}
                      disabled={isSubmitting}
                      maxLength={50}
                    />
                    {errors.requestedGroupName && (
                      <p className="text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        {errors.requestedGroupName}
                      </p>
                    )}
                  </div>

                  {/* Group Description */}
                  <div className="space-y-2">
                    <Label htmlFor="groupDescription" className="text-sm font-medium text-gray-700">
                      Description (Optional)
                    </Label>
                    <Textarea
                      id="groupDescription"
                      value={formData.groupDescription}
                      onChange={(e) => setFormData(prev => ({ ...prev, groupDescription: e.target.value }))}
                      placeholder="Briefly describe your group's purpose or goals"
                      className={errors.groupDescription ? "border-red-300 focus:border-red-500" : ""}
                      disabled={isSubmitting}
                      maxLength={500}
                      rows={3}
                    />
                    {errors.groupDescription && (
                      <p className="text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        {errors.groupDescription}
                      </p>
                    )}
                    <p className="text-xs text-gray-500">
                      {formData.groupDescription?.length || 0}/500 characters
                    </p>
                  </div>

                  {/* Submit Error */}
                  {errors.submit && (
                    <Alert variant="destructive">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>{errors.submit}</AlertDescription>
                    </Alert>
                  )}

                  {/* Info Message */}
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      Your request will be reviewed by our admin team. Once approved, 
                      you'll become the group admin and can start inviting members.
                    </AlertDescription>
                  </Alert>

                  {/* Submit Button */}
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full h-11 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium"
                  >
                    {isSubmitting ? (
                      <div className="flex items-center gap-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Submitting Request...
                      </div>
                    ) : (
                      "Submit Group Request"
                    )}
                  </Button>
                </form>
              </>
            )}
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
}
