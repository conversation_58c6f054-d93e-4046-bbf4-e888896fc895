// components/modals/wizard-steps/EmailVerificationStep.tsx

"use client";

import { useState, useEffect, useMemo } from "react";
import { motion } from "framer-motion";
import { Mail, CheckCircle, AlertCircle, Loader2, ArrowRight } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { isValidEmail } from "@/lib/utils";
import { debounce } from "lodash";
import {
  useCheckUserByEmailQuery
} from "@/lib/redux/features/groups/groupsApiSlice";
import {
  useCheckMembershipByEmailQuery,
  useCheckAnyGroupMembershipQuery
} from "@/lib/redux/features/groupMembership/groupMembershipApiSlice";
import type { WizardData, WizardStep } from "../JoinGroupWizard";

interface EmailVerificationStepProps {
  wizardData: WizardData;
  updateWizardData: (data: Partial<WizardData>) => void;
  clearWizardData: () => void;
  goToStep: (step: WizardStep) => void;
  onSuccess?: () => void;
  productId?: string;
  setIsLoading: (loading: boolean) => void;
}

export function EmailVerificationStep({
  wizardData,
  updateWizardData,
  clearWizardData,
  goToStep,
  setIsLoading
}: EmailVerificationStepProps) {
  const [email, setEmail] = useState(wizardData.email || "");
  const [emailForQuery, setEmailForQuery] = useState("");
  const [emailMessage, setEmailMessage] = useState("");
  const [isValidEmailEntered, setIsValidEmailEntered] = useState(false);

  // API Queries
  const {
    isLoading: checkingEmail,
    data: userByEmailData
  } = useCheckUserByEmailQuery(emailForQuery, { skip: !emailForQuery });

  const {
    isLoading: checkingMembership,
    data: membershipData
  } = useCheckMembershipByEmailQuery(emailForQuery, { skip: !emailForQuery });

  const {
    data: anyGroupMembershipData
  } = useCheckAnyGroupMembershipQuery({ email: emailForQuery }, { skip: !emailForQuery });

  // Debounced email check
  const debouncedCheckEmail = useMemo(
    () => debounce((emailValue: string) => {
      setEmailForQuery(emailValue);
    }, 500),
    []
  );

  // Handle email input changes
  const handleEmailChange = (value: string) => {
    setEmail(value);
    const isValid = isValidEmail(value);
    setIsValidEmailEntered(isValid);

    // If this is a new email (different from what's in wizard data), clear previous data
    if (isValid && value !== wizardData.email) {
      // Clear previous wizard data when starting with a new email
      clearWizardData();
    }

    if (isValid) {
      setEmailMessage("Checking email...");
      debouncedCheckEmail(value);
    } else if (value) {
      setEmailMessage("Please enter a valid email address");
      setEmailForQuery("");
    } else {
      setEmailMessage("");
      setEmailForQuery("");
    }
  };

  // Handle API response effects
  useEffect(() => {
    if (checkingEmail || checkingMembership) {
      setEmailMessage("Checking email...");
      return;
    }

    if (!emailForQuery) return;

    const isMemberOfAnyGroup = membershipData?.isMember || anyGroupMembershipData?.isMemberOfAnyGroup || false;

    // Update wizard data only if it has changed
    const currentWizardEmail = wizardData.email;
    const currentIsUserKnown = wizardData.isUserKnown;
    const currentIsMemberOfAnyGroup = wizardData.isMemberOfAnyGroup;

    if (
      currentWizardEmail !== emailForQuery ||
      currentIsUserKnown !== !!userByEmailData ||
      currentIsMemberOfAnyGroup !== isMemberOfAnyGroup
    ) {
      updateWizardData({
        email: emailForQuery,
        isUserKnown: !!userByEmailData,
        isMemberOfAnyGroup,
        membershipData,
        userByEmailData
      });
    }

    if (userByEmailData) {
      if (isMemberOfAnyGroup) {
        if (membershipData?.group) {
          setEmailMessage(`Welcome back! You're a member of ${membershipData.group.groupName}.`);
        } else {
          setEmailMessage("Welcome back! You're already a member of a group.");
        }
      } else {
        setEmailMessage(`Welcome back, ${userByEmailData.name}!`);
      }
    } else {
      setEmailMessage("This email is available for registration.");
    }
  }, [userByEmailData, membershipData, anyGroupMembershipData, checkingEmail, checkingMembership, emailForQuery, wizardData.email, wizardData.isUserKnown, wizardData.isMemberOfAnyGroup, updateWizardData]);

  const handleContinue = () => {
    if (!isValidEmailEntered || !emailForQuery) return;

    const isMemberOfAnyGroup = membershipData?.isMember || anyGroupMembershipData?.isMemberOfAnyGroup || false;

    // Ensure the verified email is saved to wizard data and localStorage
    const verifiedWizardData = {
      email: emailForQuery,
      isUserKnown: !!userByEmailData,
      isMemberOfAnyGroup,
      membershipData,
      userByEmailData
    };

    updateWizardData(verifiedWizardData);

    // Also save directly to localStorage to ensure persistence
    localStorage.setItem('joinGroupWizardData', JSON.stringify({
      ...wizardData,
      ...verifiedWizardData
    }));

    if (userByEmailData) {
      if (isMemberOfAnyGroup) {
        // User exists and is in a group - show login or relocation options
        goToStep("login");
      } else {
        // User exists but not in any group - go to group selection
        goToStep("group-selection");
      }
    } else {
      // New user - go to group selection first
      goToStep("group-selection");
    }
  };

  const getEmailInputStatus = () => {
    if (!email) return "default";
    if (!isValidEmailEntered) return "error";
    if (checkingEmail || checkingMembership) return "loading";
    return "success";
  };

  const getStatusIcon = () => {
    const status = getEmailInputStatus();
    switch (status) {
      case "loading":
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "error":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getMessageColor = () => {
    if (!email) return "text-gray-500";
    if (!isValidEmailEntered) return "text-red-600";
    if (checkingEmail || checkingMembership) return "text-blue-600";
    if (userByEmailData) return "text-green-600";
    return "text-blue-600";
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center space-y-2"
      >
        <div className="mx-auto w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mb-4">
          <Mail className="h-8 w-8 text-white" />
        </div>
        <h3 
          className="text-xl font-semibold text-gray-800"
          style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
        >
          Let's start with your email
        </h3>
        <p 
          className="text-gray-600"
          style={{ fontFamily: "Avenir, sans-serif" }}
        >
          We'll check if you're already part of our community
        </p>
      </motion.div>

      {/* Email Input */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="space-y-3"
      >
        <Label htmlFor="email" className="text-sm font-medium text-gray-700">
          Email Address
        </Label>
        <div className="relative">
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
            <Mail className="h-4 w-4 text-gray-400" />
          </div>
          <Input
            id="email"
            type="email"
            placeholder="Enter your email address"
            value={email}
            onChange={(e) => handleEmailChange(e.target.value)}
            className={`pl-10 pr-10 h-12 text-base transition-all duration-200 ${
              getEmailInputStatus() === "success" 
                ? "border-green-500 focus-visible:ring-green-500" 
                : getEmailInputStatus() === "error"
                ? "border-red-500 focus-visible:ring-red-500"
                : "border-gray-300 focus-visible:ring-blue-500"
            }`}
            style={{ fontFamily: "Avenir, sans-serif" }}
          />
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            {getStatusIcon()}
          </div>
        </div>
        
        {/* Status Message */}
        {emailMessage && (
          <motion.p
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            className={`text-sm ${getMessageColor()} flex items-center gap-2`}
            style={{ fontFamily: "Avenir, sans-serif" }}
          >
            {emailMessage}
          </motion.p>
        )}
      </motion.div>

      {/* Continue Button */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="pt-4"
      >
        <Button
          onClick={handleContinue}
          disabled={!isValidEmailEntered || !emailForQuery || checkingEmail || checkingMembership}
          className="w-full h-12 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
          style={{ fontFamily: "Avenir, sans-serif" }}
        >
          <span className="flex items-center gap-2">
            Continue
            <ArrowRight className="h-4 w-4" />
          </span>
        </Button>
      </motion.div>
    </div>
  );
}
