// components/modals/wizard-steps/GroupSelectionStep.tsx

"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { MapPin, Users, ArrowRight, ArrowLeft, CheckCircle, Mail, Shield, Building, Home, Map, TrendingUp } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { LocationSelectionButton } from "@/components/ui/LocationSelectionButton";
import type { LocationSelectionResult } from "@/components/modals/LocationSelectionModal";
import {
  useGetAllStokvelGroupsQuery,
  useJoinGroupMutation
} from "@/lib/redux/features/groups/groupsApiSlice";
import { useAddToCartMutation } from "@/lib/redux/features/cart/cartApiSlice";
import { useLocations } from "@/lib/redux/hooks/useLocations";
import type { WizardData, WizardStep } from "../JoinGroupWizard";
import type { StokvelGroup } from "@/types/stokvelgroup";

interface GroupSelectionStepProps {
  wizardData: WizardData;
  updateWizardData: (data: Partial<WizardData>) => void;
  clearWizardData: () => void;
  goToStep: (step: WizardStep) => void;
  onSuccess?: () => void;
  productId?: string;
  setIsLoading: (loading: boolean) => void;
}

export function GroupSelectionStep({
  wizardData,
  updateWizardData,
  clearWizardData,
  goToStep,
  onSuccess,
  productId,
  setIsLoading
}: GroupSelectionStepProps) {
  const [selectedGroupId, setSelectedGroupId] = useState(wizardData.selectedGroupId || "");
  const [availableGroups, setAvailableGroups] = useState<StokvelGroup[]>([]);
  const [locationSelection, setLocationSelection] = useState<LocationSelectionResult | null>(
    wizardData.locationSelection || null
  );

  // API hooks
  const { data: allGroups = [] } = useGetAllStokvelGroupsQuery();
  const [joinGroup] = useJoinGroupMutation();
  const [addToCart] = useAddToCartMutation();

  // Filter groups based on selected location
  useEffect(() => {
    const selectedLocationId = locationSelection?.locationId || wizardData.selectedLocationId;
    if (!selectedLocationId) {
      setAvailableGroups([]);
      return;
    }

    // Filter groups by the selected locationId
    console.log('Filtering groups:', {
      allGroupsCount: allGroups?.length || 0,
      selectedLocationId,
      allGroups: allGroups?.slice(0, 2) // Log first 2 groups for debugging
    });

    const filtered = (allGroups || []).filter(group =>
      group && group.locationId === selectedLocationId
    );

    console.log('Filtered groups:', {
      filteredCount: filtered.length,
      filtered: filtered.slice(0, 2) // Log first 2 filtered groups
    });

    setAvailableGroups(filtered || []);

    // Clear selected group when location changes
    if (selectedGroupId && !filtered.find(g => g._id === selectedGroupId)) {
      setSelectedGroupId("");
      updateWizardData({ selectedGroupId: "" });
    }
  }, [locationSelection?.locationId, wizardData.selectedLocationId, allGroups, selectedGroupId, updateWizardData]);

  // Handle location selection change
  const handleLocationSelectionChange = (result: LocationSelectionResult) => {
    setLocationSelection(result);
    updateWizardData({
      locationSelection: result,
      selectedProvinceId: result.provinceId,
      selectedCityId: result.cityId,
      selectedTownshipId: result.townshipId,
      selectedLocationId: result.locationId,
      location: result.locationId, // For backward compatibility
      selectedGroupId: "" // Clear selected group when location changes
    });
  };

  const handleGroupSelect = (groupId: string) => {
    setSelectedGroupId(groupId);

    // Update wizard data with selected group
    const updatedData = {
      selectedGroupId: groupId,
      email: wizardData.email // Ensure email is preserved
    };
    updateWizardData(updatedData);

    // Also save to localStorage immediately
    const dataToSave = {
      ...wizardData,
      ...updatedData
    };
    localStorage.setItem('joinGroupWizardData', JSON.stringify(dataToSave));

    // Debug log
    console.log('Group selected:', { groupId, email: wizardData.email, dataToSave });
  };

  // Handle joining group for existing users
  const handleJoinGroup = async (groupId: string) => {
    if (!wizardData.userByEmailData?._id) {
      console.error("User ID not found");
      return;
    }

    setIsLoading(true);

    try {
      const result = await joinGroup({
        userId: wizardData.userByEmailData._id,
        groupId: groupId,
        isRelocation: false
      }).unwrap();

      if (result.success) {
        // Add product to cart if provided
        if (productId) {
          try {
            await addToCart({
              userId: wizardData.userByEmailData._id,
              productId,
              quantity: 1,
              groupId: groupId
            }).unwrap();
          } catch (error) {
            console.error("Error adding product to cart:", error);
          }
        }

        // Success - clear wizard data and close modal
        clearWizardData();
        setTimeout(() => {
          onSuccess?.(groupId);
        }, 500);
      } else {
        console.error("Group join failed:", result);
      }
    } catch (error) {
      console.error("Error joining group:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleContinue = () => {
    if (!canProceed) return;

    // Prepare the complete wizard data with email and group selection
    const updatedWizardData = {
      email: wizardData.email, // Ensure email is preserved
      locationSelection: locationSelection,
      selectedProvinceId: locationSelection?.provinceId,
      selectedCityId: locationSelection?.cityId,
      selectedTownshipId: locationSelection?.townshipId,
      selectedLocationId: locationSelection?.locationId,
      selectedGroupId: selectedGroupId,
      // Preserve all existing wizard data
      isUserKnown: wizardData.isUserKnown,
      isMemberOfAnyGroup: wizardData.isMemberOfAnyGroup,
      membershipData: wizardData.membershipData,
      userByEmailData: wizardData.userByEmailData,
      anyGroupMembershipData: wizardData.anyGroupMembershipData
    };

    // Save to wizard data state
    updateWizardData(updatedWizardData);

    // Also save directly to localStorage to ensure persistence
    const finalDataToSave = {
      ...wizardData,
      ...updatedWizardData
    };
    localStorage.setItem('joinGroupWizardData', JSON.stringify(finalDataToSave));

    // Debug log
    console.log('Continuing to next step with data:', finalDataToSave);

    // Navigate to appropriate next step
    if (wizardData.isUserKnown) {
      // Existing user - join the group immediately
      handleJoinGroup(selectedGroupId);
    } else {
      // New user - go to registration first
      goToStep("user-registration");
    }
  };

  const canProceed = locationSelection && selectedGroupId;

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center space-y-2"
      >
        <div className="mx-auto w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center mb-4">
          <Users className="h-8 w-8 text-white" />
        </div>
        <h3
          className="text-xl font-semibold text-gray-800"
          style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
        >
          Choose Your Stokvel Group
        </h3>
        <p
          className="text-gray-600"
          style={{ fontFamily: "Avenir, sans-serif" }}
        >
          Select your location to find and join a local Stokvel group
        </p>
      </motion.div>

      {/* Verified Email Display */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="p-4 bg-green-50 border border-green-200 rounded-xl"
      >
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
            <Shield className="h-4 w-4 text-white" />
          </div>
          <div className="flex-1">
            <p className="text-sm font-medium text-green-800">Verified Email Address</p>
            <p className="text-sm text-green-600 flex items-center gap-2">
              <Mail className="h-3 w-3" />
              {wizardData.email}
            </p>
          </div>
          <CheckCircle className="h-5 w-5 text-green-500" />
        </div>
      </motion.div>

      {/* Location Selection - New Modal-Based Approach */}
      <div className="space-y-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="space-y-3"
        >
          <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
            <MapPin className="h-4 w-4 text-blue-600" />
            Your Location
          </Label>
          <p className="text-xs text-gray-500">
            Select your location to find available Stokvel groups
          </p>

          <LocationSelectionButton
            value={locationSelection}
            onLocationSelect={handleLocationSelectionChange}
            placeholder="Tap to select your location"
            className="text-base sm:text-sm"
            size="lg"
            showFullPath={true}
            required={true}
          />
        </motion.div>
      </div>

      {/* Available Groups */}
      {locationSelection && availableGroups.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="space-y-4"
        >
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-emerald-600" />
              <h4 className="font-semibold text-gray-800">
                Available Stokvel Groups ({availableGroups.length})
              </h4>
            </div>
            <p className="text-sm text-gray-600">
              Groups in {locationSelection.fullPath}
            </p>
          </div>
          <div className="grid gap-4 max-h-96 overflow-y-auto">
            {availableGroups.map((group, index) => {

              return (
                <motion.div
                  key={group._id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 * index }}
                  className="group"
                >
                  <Card
                    className={`cursor-pointer transition-all duration-300 hover:shadow-lg ${
                      selectedGroupId === group._id
                        ? "ring-2 ring-emerald-500 bg-emerald-50 border-emerald-200 shadow-md"
                        : "hover:shadow-md border-gray-200 hover:border-emerald-300"
                    }`}
                    onClick={() => handleGroupSelect(group._id)}
                  >
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1 space-y-3">
                          {/* Group Header */}
                          <div className="flex items-center gap-3">
                            <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center">
                              <Users className="h-6 w-6 text-white" />
                            </div>
                            <div>
                              <h5 className="font-bold text-lg text-gray-800">
                                {group.name}
                              </h5>
                              <p className="text-sm text-gray-600 flex items-center gap-1">
                                <MapPin className="h-3 w-3" />
                                {locationSelection.fullPath}
                              </p>
                            </div>
                          </div>

                          {/* Group Stats */}
                          <div className="flex items-center gap-4">
                            <div className="flex items-center gap-2">
                              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                <Users className="h-4 w-4 text-blue-600" />
                              </div>
                              <div>
                                <p className="text-sm font-medium text-gray-800">
                                  {group.members?.length || 0} Members
                                </p>
                                <p className="text-xs text-gray-500">Active participants</p>
                              </div>
                            </div>

                            {group.totalSales && (
                              <div className="flex items-center gap-2">
                                <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                  <TrendingUp className="h-4 w-4 text-green-600" />
                                </div>
                                <div>
                                  <p className="text-sm font-medium text-gray-800">
                                    R{group.totalSales.toLocaleString()}
                                  </p>
                                  <p className="text-xs text-gray-500">Total saved</p>
                                </div>
                              </div>
                            )}
                          </div>

                          {/* Group Description */}
                          {group.description && (
                            <p className="text-sm text-gray-600 line-clamp-2">
                              {group.description}
                            </p>
                          )}

                          {/* Group Badges */}
                          <div className="flex items-center gap-2 flex-wrap">
                            <Badge className="bg-emerald-100 text-emerald-700 border-emerald-200">
                              Active Group
                            </Badge>
                            {group.isVerified && (
                              <Badge className="bg-blue-100 text-blue-700 border-blue-200">
                                Verified
                              </Badge>
                            )}
                            {group.members?.length >= 10 && (
                              <Badge className="bg-purple-100 text-purple-700 border-purple-200">
                                Popular
                              </Badge>
                            )}
                          </div>
                        </div>

                        {/* Selection Indicator */}
                        <div className="flex-shrink-0 ml-4">
                          {selectedGroupId === group._id ? (
                            <div className="w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center">
                              <CheckCircle className="h-5 w-5 text-white" />
                            </div>
                          ) : (
                            <div className="w-8 h-8 border-2 border-gray-300 rounded-full group-hover:border-emerald-400 transition-colors" />
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </div>
        </motion.div>
      )}

      {/* No Groups Message */}
      {locationSelection && availableGroups.length === 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="text-center py-8"
        >
          <div className="mx-auto w-16 h-16 bg-amber-100 rounded-2xl flex items-center justify-center mb-4">
            <MapPin className="h-8 w-8 text-amber-600" />
          </div>
          <h4 className="font-semibold text-gray-800 mb-2">No Stokvel groups in this location yet</h4>
          <p className="text-sm text-gray-600 mb-4">
            Be the first to start a Stokvel group in your area, or check nearby locations.
          </p>
          <Button
            variant="outline"
            onClick={() => {
              // Clear location selection to try again
              setLocationSelection(null);
              setSelectedGroupId("");
              updateWizardData({
                locationSelection: null,
                selectedLocationId: "",
                selectedGroupId: ""
              });
            }}
            className="border-amber-300 text-amber-700 hover:bg-amber-50"
          >
            Try Different Location
          </Button>
        </motion.div>
      )}

      {/* Continue Button */}
      {canProceed && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
        >
          <Button
            onClick={handleContinue}
            className="w-full h-12 bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white font-semibold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl"
            style={{ fontFamily: "Avenir, sans-serif" }}
          >
            <span className="flex items-center gap-2">
              Continue with Selected Group
              <ArrowRight className="h-4 w-4" />
            </span>
          </Button>
        </motion.div>
      )}

      {/* Back Button */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
        className="pt-2"
      >
        <Button
          variant="ghost"
          onClick={() => goToStep("email-verification")}
          className="w-full h-10 text-gray-600 hover:text-gray-800"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Email Verification
        </Button>
      </motion.div>
    </div>
  );
}
