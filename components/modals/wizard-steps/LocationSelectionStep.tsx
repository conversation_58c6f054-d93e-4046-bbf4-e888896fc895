// components/modals/wizard-steps/LocationSelectionStep.tsx

"use client";

import { useState, useEffect, useCallback } from "react";
import { motion } from "framer-motion";
import { MapPin, Users, ArrowLeft, CheckCircle, Mail, Shield, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";

import { Skeleton } from "@/components/ui/skeleton";
import { CompleteLocationSelector } from "@/components/ui/enhanced-location-selects-v2";
import { LocationSelectionButton } from "@/components/ui/LocationSelectionButton";
import type { LocationSelectionResult } from "@/components/modals/LocationSelectionModal";
import {
  useGetAllStokvelGroupsQuery,
  useJoinGroupMutation,
  useStoreLocationRequestMutation
} from "@/lib/redux/features/groups/groupsApiSlice";
import { useCreateGroupRequestMutation } from "@/lib/redux/features/groupRequests/groupRequestsApiSlice";
import { useAddToCartMutation } from "@/lib/redux/features/cart/cartApiSlice";
import { useLocations } from "@/lib/redux/hooks/useLocations";
import { PremiumGroupCard } from "./PremiumGroupCard";
import { GroupRequestModal, type GroupRequestData } from "@/components/modals/GroupRequestModal";
import type { WizardData, WizardStep } from "../JoinGroupWizard";
import type { StokvelGroup } from "@/types/stokvelgroup";

interface LocationSelectionStepProps {
  wizardData: WizardData;
  updateWizardData: (data: Partial<WizardData>) => void;
  clearWizardData: () => void;
  goToStep: (step: WizardStep) => void;
  onSuccess?: (groupId?: string) => void;
  productId?: string;
  setIsLoading: (loading: boolean) => void;
}

export function LocationSelectionStep({
  wizardData,
  updateWizardData,
  clearWizardData,
  goToStep,
  onSuccess,
  productId,
  setIsLoading
}: LocationSelectionStepProps) {
  const [selectedGroupId, setSelectedGroupId] = useState(wizardData.selectedGroupId || "");
  const [availableGroups, setAvailableGroups] = useState<StokvelGroup[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [locationSelection, setLocationSelection] = useState<LocationSelectionResult | null>(
    wizardData.locationSelection || null
  );
  const [showGroupRequestModal, setShowGroupRequestModal] = useState(false);

  // API hooks
  const { data: allGroups = [] } = useGetAllStokvelGroupsQuery();
  const [joinGroup] = useJoinGroupMutation();
  const [storeLocationRequest] = useStoreLocationRequestMutation();
  const [createGroupRequest] = useCreateGroupRequestMutation();
  const [addToCart] = useAddToCartMutation();

  // Check if we have a pre-selected group from group-selection step
  const hasPreSelectedGroup = wizardData.selectedGroupId && wizardData.selectedLocationId;



  // Filter groups based on selected location
  useEffect(() => {
    const selectedLocationId = locationSelection?.locationId || wizardData.selectedLocationId;
    if (!selectedLocationId) {
      setAvailableGroups([]);
      return;
    }

    // Filter groups by the selected locationId
    const filtered = allGroups.filter(group =>
      group.locationId === selectedLocationId
    );
    setAvailableGroups(filtered);
    console.log(`Found ${filtered.length} groups for location ${selectedLocationId}`);

    // Clear selected group when location changes
    if (selectedGroupId && !filtered.find(g => g._id === selectedGroupId)) {
      setSelectedGroupId("");
      updateWizardData({ selectedGroupId: "" });
    }
  }, [locationSelection?.locationId, wizardData.selectedLocationId, allGroups, selectedGroupId, updateWizardData]);

  // Handle location selection change
  const handleLocationSelectionChange = (result: LocationSelectionResult) => {
    setLocationSelection(result);
    updateWizardData({
      locationSelection: result,
      selectedProvinceId: result.provinceId,
      selectedCityId: result.cityId,
      selectedTownshipId: result.townshipId,
      selectedLocationId: result.locationId,
      location: result.locationId, // For backward compatibility
      selectedGroupId: "" // Clear selected group when location changes
    });
  };



  // Handle group request submission
  const handleGroupRequest = async (requestData: GroupRequestData) => {
    if (!locationSelection || !wizardData.userByEmailData) {
      throw new Error("Missing required data for group request");
    }

    // Create the group request using the enhanced data structure
    const groupRequestPayload = {
      // User information
      userId: wizardData.userByEmailData._id,
      userEmail: wizardData.userByEmailData.email,
      userName: wizardData.userByEmailData.name,
      userPhone: wizardData.userByEmailData.phone,

      // Location hierarchy
      provinceId: locationSelection.provinceId,
      provinceName: locationSelection.provinceName,
      cityId: locationSelection.cityId,
      cityName: locationSelection.cityName,
      townshipId: locationSelection.townshipId,
      townshipName: locationSelection.townshipName,
      locationId: locationSelection.locationId,
      locationName: locationSelection.locationName,
      fullLocationPath: locationSelection.fullPath,

      // Group details
      requestedGroupName: requestData.requestedGroupName,
      groupDescription: requestData.groupDescription
    };

    // Use the new group request API
    await createGroupRequest(groupRequestPayload).unwrap();
  };

  const handleSubmit = useCallback(async () => {
    // Use pre-selected group if available, otherwise use current selection
    const groupToJoin = wizardData.selectedGroupId || selectedGroupId;
    const locationToUse = locationSelection?.locationId || wizardData.selectedLocationId;

    if (!groupToJoin && !locationToUse) return;

    setIsSubmitting(true);
    setIsLoading(true);

    try {
      const userId = wizardData.userByEmailData?._id;
      if (!userId) {
        throw new Error("User ID not found");
      }

      if (groupToJoin) {
        // Join selected group
        const result = await joinGroup({
          userId,
          groupId: groupToJoin,
          isRelocation: false
        }).unwrap();

        if (result.success) {
          // Add product to cart if provided
          if (productId) {
            try {
              await addToCart({
                userId,
                productId,
                quantity: 1,
                groupId: groupToJoin
              }).unwrap();
            } catch (error) {
              console.error("Error adding product to cart:", error);
            }
          }

          // Success - clear wizard data and close modal
          clearWizardData();
          setTimeout(() => {
            onSuccess?.(groupToJoin);
          }, 500);
        }
      } else {
        // No matching groups - show group request modal instead of simple location request
        setShowGroupRequestModal(true);
        return; // Don't close the wizard yet
      }
    } catch (error) {
      console.error("Error in location selection:", error);
    } finally {
      setIsSubmitting(false);
      setIsLoading(false);
    }
  }, [
    wizardData.selectedGroupId,
    wizardData.selectedLocationId,
    wizardData.userByEmailData?._id,
    selectedGroupId,
    locationSelection?.locationId,
    joinGroup,
    addToCart,
    storeLocationRequest,
    clearWizardData,
    onSuccess,
    productId,
    setIsLoading,
    setIsSubmitting
  ]);

  // Auto-join pre-selected group
  useEffect(() => {
    if (hasPreSelectedGroup && !isSubmitting) {
      // Small delay to show the UI briefly before auto-joining
      const timer = setTimeout(() => {
        handleSubmit();
      }, 1500);
      return () => clearTimeout(timer);
    }
  }, [hasPreSelectedGroup, isSubmitting, handleSubmit]);

  const canProceed = locationSelection && (selectedGroupId || availableGroups.length === 0);

  // If we have a pre-selected group, show joining confirmation
  if (hasPreSelectedGroup) {
    return (
      <div className="space-y-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center space-y-2"
        >
          <div className="mx-auto w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center mb-4">
            <Users className="h-8 w-8 text-white" />
          </div>
          <h3
            className="text-xl font-semibold text-gray-800"
            style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
          >
            Joining Your Selected Group
          </h3>
          <p
            className="text-gray-600"
            style={{ fontFamily: "Avenir, sans-serif" }}
          >
            We're adding you to your chosen Stokvel group
          </p>
        </motion.div>

        {/* Verified Email Display */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="p-4 bg-green-50 border border-green-200 rounded-xl"
        >
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
              <Shield className="h-4 w-4 text-white" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-green-800">Verified Email Address</p>
              <p className="text-sm text-green-600 flex items-center gap-2">
                <Mail className="h-3 w-3" />
                {wizardData.email}
              </p>
            </div>
            <CheckCircle className="h-5 w-5 text-green-500" />
          </div>
        </motion.div>

        {/* Processing Message */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.2 }}
          className="text-center py-8"
        >
          <div className="mx-auto w-16 h-16 bg-emerald-100 rounded-2xl flex items-center justify-center mb-4">
            <div className="w-8 h-8 border-2 border-emerald-500 border-t-transparent rounded-full animate-spin" />
          </div>
          <h4 className="font-semibold text-gray-800 mb-2">Processing your group membership</h4>
          <p className="text-sm text-gray-600">
            Please wait while we complete your registration...
          </p>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center space-y-2"
      >
        <div className="mx-auto w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mb-4">
          <MapPin className="h-8 w-8 text-white" />
        </div>
        <h3
          className="text-xl font-semibold text-gray-800"
          style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
        >
          Find Your Community
        </h3>
        <p
          className="text-gray-600"
          style={{ fontFamily: "Avenir, sans-serif" }}
        >
          Select your location to discover local Stokvel groups
        </p>
      </motion.div>

      {/* Verified Email Display */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="p-4 bg-green-50 border border-green-200 rounded-xl"
      >
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
            <Shield className="h-4 w-4 text-white" />
          </div>
          <div className="flex-1">
            <p className="text-sm font-medium text-green-800">Verified Email Address</p>
            <p className="text-sm text-green-600 flex items-center gap-2">
              <Mail className="h-3 w-3" />
              {wizardData.email}
            </p>
          </div>
          <CheckCircle className="h-5 w-5 text-green-500" />
        </div>
      </motion.div>

      {/* Location Selection - New Modal-Based Approach */}
      <div className="space-y-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="space-y-3"
        >
          <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
            <MapPin className="h-4 w-4 text-blue-600" />
            Your Location
          </Label>
          <p className="text-xs text-gray-500">
            Select your complete location to find nearby Stokvel groups
          </p>

          <LocationSelectionButton
            value={locationSelection}
            onLocationSelect={handleLocationSelectionChange}
            placeholder="Tap to select your location"
            className="text-base sm:text-sm"
            size="lg"
            showFullPath={true}
            required={true}
          />
        </motion.div>
      </div>

      {/* Available Groups */}
      {locationSelection && availableGroups.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="space-y-3"
        >
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-green-600" />
              <h4 className="font-semibold text-gray-800">
                Available Groups ({availableGroups.length})
              </h4>
            </div>
            <p className="text-sm text-gray-600">
              Groups in {locationSelection.fullPath}
            </p>
          </div>
          <div className="grid gap-4 max-h-96 overflow-y-auto">
            {availableGroups.map((group, index) => (
              <PremiumGroupCard
                key={group._id}
                group={group}
                isSelected={selectedGroupId === group._id}
                onSelect={setSelectedGroupId}
                locationPath={locationSelection.fullPath}
                index={index}
              />
            ))}
          </div>
        </motion.div>
      )}

      {/* No Groups Message with Request Option */}
      {locationSelection && availableGroups.length === 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center py-8"
        >
          <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
            <Users className="h-6 w-6 text-blue-600" />
          </div>
          <h4 className="font-medium text-gray-800 mb-2">No groups in this location yet</h4>
          <p className="text-sm text-gray-600 mb-4">
            Be the first to create a Stokvel group in your area!
          </p>
          <Button
            onClick={() => setShowGroupRequestModal(true)}
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium px-6 py-2 rounded-lg"
          >
            <Plus className="h-4 w-4 mr-2" />
            Request Group Creation
          </Button>
        </motion.div>
      )}

      {/* Continue Button */}
      {locationSelection && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
        >
          <Button
            onClick={handleSubmit}
            disabled={!canProceed || isSubmitting}
            className="w-full h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium"
            style={{ fontFamily: "Avenir, sans-serif" }}
          >
            {isSubmitting ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                Processing...
              </div>
            ) : selectedGroupId ? (
              "Join Selected Group"
            ) : (
              "Continue"
            )}
          </Button>
        </motion.div>
      )}

      {/* Back Button */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
        className="pt-2"
      >
        <Button
          variant="ghost"
          onClick={() => goToStep(wizardData.isUserKnown ? "email-verification" : "user-registration")}
          className="w-full h-10 text-gray-600 hover:text-gray-800"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Previous Step
        </Button>
      </motion.div>

      {/* Group Request Modal */}
      {locationSelection && wizardData.userByEmailData && (
        <GroupRequestModal
          isOpen={showGroupRequestModal}
          onClose={() => setShowGroupRequestModal(false)}
          onSubmit={async (requestData) => {
            await handleGroupRequest(requestData);
            setShowGroupRequestModal(false);
            // Show success and close wizard
            clearWizardData();
            setTimeout(() => {
              onSuccess?.();
            }, 500);
          }}
          locationSelection={locationSelection}
          userEmail={wizardData.userByEmailData.email}
          userName={wizardData.userByEmailData.name}
          userPhone={wizardData.userByEmailData.phone}
        />
      )}
    </div>
  );
}
