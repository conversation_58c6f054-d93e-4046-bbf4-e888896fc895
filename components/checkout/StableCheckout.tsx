'use client';

import { useState, memo, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ArrowLeft, ArrowRight, Check, CheckCircle2 } from 'lucide-react';
import { useCartContext } from '@/context/CartContext';
import { PaymentMethodSelector } from '@/components/payments/PaymentMethodSelector';
import { PaymentForm } from '@/components/payments/PaymentForm';
import { formatCurrency } from '@/lib/utils';
import { PaymentMethodType, PaymentFormData } from '@/types/payment';

// Validation schemas
const customerDetailsSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  address: z.string().min(5, 'Address must be at least 5 characters'),
  city: z.string().min(2, 'City must be at least 2 characters'),
  country: z.string().min(2, 'Country must be at least 2 characters'),
  postalCode: z.string().min(3, 'Postal code must be at least 3 characters'),
});

const paymentSchema = z.object({
  paymentMethod: z.enum(['credit_card', 'bank_transfer', 'eft', 'paypal']),
  savePaymentInfo: z.boolean().optional(),
});

interface StableCheckoutProps {
  groupId: string;
}

export const StableCheckout = memo(function StableCheckout({ groupId }: StableCheckoutProps) {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethodType>('credit_card');
  const [formData, setFormData] = useState<any>({});

  // Use stable cart data
  const { stableCartData, clearCart } = useCartContext();

  // React Hook Form setup for customer details
  const customerDetailsForm = useForm<z.infer<typeof customerDetailsSchema>>({
    resolver: zodResolver(customerDetailsSchema),
    defaultValues: {
      name: '',
      email: '',
      address: '',
      city: '',
      country: '',
      postalCode: '',
    }
  });

  // React Hook Form setup for payment
  const paymentForm = useForm<z.infer<typeof paymentSchema>>({
    resolver: zodResolver(paymentSchema),
    defaultValues: {
      paymentMethod: 'credit_card',
      savePaymentInfo: false,
    }
  });

  // Handle form submissions for each step
  const handleCustomerDetailsSubmit = useCallback((data: z.infer<typeof customerDetailsSchema>) => {
    setFormData(prev => ({ ...prev, ...data }));
    setCurrentStep(1);
  }, []);

  const handlePaymentMethodSelect = useCallback((method: PaymentMethodType) => {
    setSelectedPaymentMethod(method);
    setFormData(prev => ({ ...prev, paymentMethod: method }));
  }, []);

  const handlePaymentFormSubmit = useCallback(async (paymentFormData: PaymentFormData) => {
    setFormData(prev => ({ ...prev, ...paymentFormData }));
    setCurrentStep(2);
  }, []);

  // Submit the complete order with payment processing
  const handleOrderSubmit = useCallback(async () => {
    setIsSubmitting(true);
    try {
      // Simulate payment processing
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Clear cart after successful order
      await clearCart();
      
      // Redirect to success page
      router.push(`/groups/${groupId}/orders?success=true`);
    } catch (error) {
      console.error('Error submitting order:', error);
    } finally {
      setIsSubmitting(false);
    }
  }, [groupId, router, clearCart]);

  // Animation variants for step transitions
  const variants = {
    hidden: { opacity: 0, x: 50 },
    visible: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -50 }
  };

  // Render the current step
  const renderStep = () => {
    switch (currentStep) {
      case 0: // Customer details
        return (
          <motion.div
            key="customer-details"
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={variants}
            transition={{ duration: 0.3 }}
          >
            <CardHeader>
              <CardTitle>Customer Information</CardTitle>
              <CardDescription>
                Please provide your shipping and contact details
              </CardDescription>
            </CardHeader>
            <form onSubmit={customerDetailsForm.handleSubmit(handleCustomerDetailsSubmit)}>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Full Name</Label>
                    <Input
                      id="name"
                      {...customerDetailsForm.register('name')}
                    />
                    {customerDetailsForm.formState.errors.name && (
                      <p className="text-sm text-red-500">{customerDetailsForm.formState.errors.name.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address</Label>
                    <Input
                      id="email"
                      type="email"
                      {...customerDetailsForm.register('email')}
                    />
                    {customerDetailsForm.formState.errors.email && (
                      <p className="text-sm text-red-500">{customerDetailsForm.formState.errors.email.message}</p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="address">Street Address</Label>
                  <Input
                    id="address"
                    {...customerDetailsForm.register('address')}
                  />
                  {customerDetailsForm.formState.errors.address && (
                    <p className="text-sm text-red-500">{customerDetailsForm.formState.errors.address.message}</p>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="city">City</Label>
                    <Input
                      id="city"
                      {...customerDetailsForm.register('city')}
                    />
                    {customerDetailsForm.formState.errors.city && (
                      <p className="text-sm text-red-500">{customerDetailsForm.formState.errors.city.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="postalCode">Postal Code</Label>
                    <Input
                      id="postalCode"
                      {...customerDetailsForm.register('postalCode')}
                    />
                    {customerDetailsForm.formState.errors.postalCode && (
                      <p className="text-sm text-red-500">{customerDetailsForm.formState.errors.postalCode.message}</p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="country">Country</Label>
                  <Input
                    id="country"
                    {...customerDetailsForm.register('country')}
                  />
                  {customerDetailsForm.formState.errors.country && (
                    <p className="text-sm text-red-500">{customerDetailsForm.formState.errors.country.message}</p>
                  )}
                </div>
              </CardContent>

              <CardFooter className="flex justify-between">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.push(`/groups/${groupId}/new-order`)}
                >
                  <ArrowLeft className="mr-2 h-4 w-4" /> Back to Products
                </Button>
                <Button type="submit">
                  Continue to Payment <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </CardFooter>
            </form>
          </motion.div>
        );

      case 1: // Payment method
        return (
          <motion.div
            key="payment"
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={variants}
            transition={{ duration: 0.3 }}
          >
            <CardHeader>
              <CardTitle>Payment</CardTitle>
              <CardDescription>
                Choose your payment method and enter payment details
              </CardDescription>
            </CardHeader>

            <CardContent className="space-y-6">
              {/* Payment Method Selection */}
              <PaymentMethodSelector
                selectedMethod={selectedPaymentMethod}
                onMethodChange={handlePaymentMethodSelect}
                amount={stableCartData.subtotal}
                currency="ZAR"
                disabled={isSubmitting}
              />

              {/* Payment Form */}
              <PaymentForm
                paymentMethod={selectedPaymentMethod}
                onSubmit={handlePaymentFormSubmit}
                isLoading={isSubmitting}
                disabled={isSubmitting}
              />
            </CardContent>

            <CardFooter className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={() => setCurrentStep(0)}
                disabled={isSubmitting}
              >
                <ArrowLeft className="mr-2 h-4 w-4" /> Back to Information
              </Button>
            </CardFooter>
          </motion.div>
        );

      case 2: // Order summary
        return (
          <motion.div
            key="summary"
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={variants}
            transition={{ duration: 0.3 }}
          >
            <CardHeader>
              <CardTitle>Order Summary</CardTitle>
              <CardDescription>
                Review your order details before confirming
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Order Items */}
              <div className="space-y-4">
                <h3 className="font-medium">Order Items ({stableCartData.totalItems})</h3>
                {stableCartData.items.map((item) => (
                  <div key={item._id} className="flex justify-between items-center py-2 border-b">
                    <div>
                      <p className="font-medium">{item.name}</p>
                      <p className="text-sm text-gray-600">Qty: {item.quantity}</p>
                    </div>
                    <p className="font-medium">R {item.subtotal.toFixed(2)}</p>
                  </div>
                ))}
              </div>

              {/* Customer Info */}
              <div className="space-y-2">
                <h3 className="font-medium">Shipping Information</h3>
                <div className="text-sm text-gray-600">
                  <p>{formData.name}</p>
                  <p>{formData.email}</p>
                  <p>{formData.address}</p>
                  <p>{formData.city}, {formData.postalCode}</p>
                  <p>{formData.country}</p>
                </div>
              </div>

              {/* Payment Method */}
              <div className="space-y-2">
                <h3 className="font-medium">Payment Method</h3>
                <p className="text-sm text-gray-600">
                  {selectedPaymentMethod === 'credit_card' ? 'Credit Card' :
                   selectedPaymentMethod === 'bank_transfer' ? 'Bank Transfer' :
                   selectedPaymentMethod === 'eft' ? 'EFT' : 'PayPal'}
                </p>
              </div>

              {/* Total */}
              <div className="border-t pt-4">
                <div className="flex justify-between items-center text-lg font-bold">
                  <span>Total</span>
                  <span>R {stableCartData.subtotal.toFixed(2)}</span>
                </div>
              </div>
            </CardContent>

            <CardFooter className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={() => setCurrentStep(1)}
                disabled={isSubmitting}
              >
                <ArrowLeft className="mr-2 h-4 w-4" /> Back to Payment
              </Button>
              <Button
                onClick={handleOrderSubmit}
                disabled={isSubmitting}
                className="bg-green-600 hover:bg-green-700"
              >
                {isSubmitting ? (
                  <>Processing...</>
                ) : (
                  <>Place Order <Check className="ml-2 h-4 w-4" /></>
                )}
              </Button>
            </CardFooter>
          </motion.div>
        );

      default:
        return null;
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <AnimatePresence mode="wait">
        {renderStep()}
      </AnimatePresence>
    </Card>
  );
});
