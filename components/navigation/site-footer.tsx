"use client"

import Link from "next/link"
import Image from "next/image"
import { Instagram, Facebook, Twitter, Mail, Phone, MapPin, Heart, Sparkles } from 'lucide-react'
import { motion } from 'framer-motion'

type FooterLink = {
  label: string
  href: string
}

type FooterSection = {
  type: "contact"
  items: string[]
} | {
  type: "links"
  items: FooterLink[]
}

type FooterSections = {
  [key: string]: FooterSection
}

const footerSections: FooterSections = {
  "Get in Touch": {
    type: "contact",
    items: [
      "123 Community Street",
      "Johannesburg, Gauteng 2000",
      "<EMAIL>",
      "Tel: +27 11 456 7890"
    ]
  },
  "Quick Links": {
    type: "links",
    items: [
      { label: "About Us", href: "/aboutus" },
      { label: "How It Works", href: "/faq" },
      { label: "Community Groups", href: "/groups" },
      { label: "Support Center", href: "/contact" },
      { label: "Privacy Policy", href: "/privacy" }
    ]
  },
  "Shop Categories": {
    type: "links",
    items: [
      { label: "Fresh Produce", href: "/store?category=produce" },
      { label: "Dairy & Eggs", href: "/store?category=dairy" },
      { label: "Pantry Essentials", href: "/store?category=pantry" },
      { label: "Beverages", href: "/store?category=beverages" },
      { label: "Household Items", href: "/store?category=household" },
      { label: "Personal Care", href: "/store?category=personal-care" }
    ]
  }
}

// Custom TikTok icon component
const TikTokIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    <path d="M19.59 6.69a4.83 4.83 0 0 1-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 0 1-5.2 1.74 2.89 2.89 0 0 1 2.31-4.64 2.93 2.93 0 0 1 .88.13V9.4a6.84 6.84 0 0 0-1-.05A6.33 6.33 0 0 0 5 20.1a6.34 6.34 0 0 0 10.86-4.43v-7a8.16 8.16 0 0 0 4.77 1.52v-3.4a4.85 4.85 0 0 1-1-.1z"/>
  </svg>
)

const socialLinks = [
  { icon: Instagram, href: "https://instagram.com/stokvel", name: "Instagram" },
  { icon: Facebook, href: "https://facebook.com/stokvel", name: "Facebook" },
  { icon: Twitter, href: "https://twitter.com/stokvel", name: "Twitter" },
  { icon: TikTokIcon, href: "https://tiktok.com/@stokvel", name: "TikTok" }
]

export function SiteFooter() {
  return (
    <footer className="relative bg-gradient-to-br from-[#2A7C6C] via-[#2A7C6C] to-[#1E5A4F] text-white overflow-hidden">
      {/* Pattern Background */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.3'%3E%3Cpath d='M50 50L25 75L75 75z'/%3E%3Cpath d='M50 50L75 25L75 75z'/%3E%3Cpath d='M50 50L25 25L75 25z'/%3E%3Cpath d='M50 50L25 25L25 75z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      {/* Floating Elements */}
      <div className="absolute top-20 left-20 w-32 h-32 bg-[#7FDBCA]/20 rounded-full blur-3xl" />
      <div className="absolute bottom-20 right-20 w-40 h-40 bg-white/10 rounded-full blur-3xl" />

      <div className="container mx-auto px-4 py-20 relative z-10">
        {/* Header Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="flex items-center justify-center space-x-4 mb-6">
            <Image
              src="/StokvelLogo.avif"
              alt="Stokvel Logo"
              width={56}
              height={56}
              className="rounded-2xl shadow-lg"
            />
            <div>
              <h1
                className="text-4xl font-bold text-white"
                style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
              >
                Stokvel
              </h1>
              <p
                className="text-[#7FDBCA] text-sm -mt-1"
                style={{ fontFamily: "Avenir, sans-serif" }}
              >
                Community Commerce Platform
              </p>
            </div>
          </div>

          <div className="w-32 h-1 bg-gradient-to-r from-[#7FDBCA] to-white mx-auto mb-6 rounded-full" />

          <p
            className="text-white/90 text-xl max-w-3xl mx-auto leading-relaxed"
            style={{ fontFamily: 'Avenir, sans-serif' }}
          >
            Bringing communities together through the power of collective shopping.
            Experience ubuntu in every purchase.
          </p>
        </motion.div>

        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 mb-16">
          {/* Contact and Information Columns */}
          {Object.entries(footerSections).map(([title, section], index) => (
            <motion.div
              key={title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <h2
                className="text-2xl md:text-3xl font-bold mb-8 text-white"
                style={{
                  fontFamily: 'ClashDisplay-Variable, sans-serif',
                  letterSpacing: '-0.02em'
                }}
              >
                {title}
              </h2>
              <ul className="space-y-4">
                {section.type === "contact" ? (
                  // Contact information with icons
                  section.items.map((item, itemIndex) => {
                    const getIcon = (index: number) => {
                      switch(index) {
                        case 0: return <MapPin className="h-4 w-4" />
                        case 1: return <MapPin className="h-4 w-4" />
                        case 2: return <Mail className="h-4 w-4" />
                        case 3: return <Phone className="h-4 w-4" />
                        default: return null
                      }
                    }

                    return (
                      <motion.li
                        key={itemIndex}
                        whileHover={{ x: 5 }}
                        className="flex items-center text-white/90 hover:text-white transition-all duration-300"
                        style={{ fontFamily: 'Avenir, sans-serif' }}
                      >
                        <span className="mr-3 text-[#7FDBCA]">
                          {getIcon(itemIndex)}
                        </span>
                        {item}
                      </motion.li>
                    )
                  })
                ) : (
                  // Navigation links
                  section.items.map((item, itemIndex) => (
                    <motion.li
                      key={itemIndex}
                      whileHover={{ x: 5 }}
                    >
                      <Link
                        href={item.href}
                        className="text-white/90 hover:text-[#7FDBCA] transition-all duration-300 flex items-center group"
                        style={{ fontFamily: 'Avenir, sans-serif' }}
                      >
                        <span className="mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">→</span>
                        {item.label}
                      </Link>
                    </motion.li>
                  ))
                )}
              </ul>
            </motion.div>
          ))}

          {/* Enhanced Social Links Column */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <h2
              className="text-2xl md:text-3xl font-bold mb-8 text-white"
              style={{
                fontFamily: 'ClashDisplay-Variable, sans-serif',
                letterSpacing: '-0.02em'
              }}
            >
              Connect With Us
            </h2>

            <div className="space-y-6">
              <p
                className="text-white/90 text-sm leading-relaxed"
                style={{ fontFamily: 'Avenir, sans-serif' }}
              >
                Join our community on social media for updates, tips, and exclusive offers.
              </p>

              <div className="flex gap-4">
                {socialLinks.map((social, index) => (
                  <motion.div
                    key={index}
                    whileHover={{ scale: 1.1, y: -2 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Link
                      href={social.href}
                      className="h-12 w-12 rounded-2xl bg-white/10 backdrop-blur-sm border border-white/20 flex items-center justify-center text-white hover:bg-[#7FDBCA] hover:text-[#2A7C6C] transition-all duration-300 shadow-lg hover:shadow-xl"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <social.icon className="h-6 w-6" />
                      <span className="sr-only">{social.name}</span>
                    </Link>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        </div>

        {/* Bottom Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="border-t border-white/20 pt-8"
        >
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="flex items-center space-x-2">
              <p
                className="text-white/80 text-sm"
                style={{ fontFamily: 'Avenir, sans-serif' }}
              >
                © 2024 Stokvel. Made with
              </p>
              <Heart className="h-4 w-4 text-red-400 fill-current" />
              <p
                className="text-white/80 text-sm"
                style={{ fontFamily: 'Avenir, sans-serif' }}
              >
                for our community
              </p>
            </div>

            <div className="flex items-center space-x-6 text-sm text-white/80">
              <Link href="/privacy" className="hover:text-[#7FDBCA] transition-colors duration-300">
                Privacy Policy
              </Link>
              <Link href="/terms" className="hover:text-[#7FDBCA] transition-colors duration-300">
                Terms of Service
              </Link>
              <div className="flex items-center space-x-1">
                <Sparkles className="h-4 w-4 text-[#7FDBCA]" />
                <span>Ubuntu in Commerce</span>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </footer>
  )
}

