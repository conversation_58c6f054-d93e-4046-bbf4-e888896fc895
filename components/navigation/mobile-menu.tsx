// components/navigation/mobile-menu.tsx

import { useState, useEffect } from 'react'
import { createPortal } from 'react-dom'
import Link from 'next/link'
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { X } from 'lucide-react'

interface NavItem {
  name: string
  icon: React.ElementType
}

interface MobileMenuProps {
  isOpen: boolean
  onClose: () => void
  navItems: NavItem[]
  isActive: (path: string) => boolean
}

export function MobileMenu({ isOpen, onClose, navItems, isActive }: MobileMenuProps) {
  const [isClosing, setIsClosing] = useState(false)
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
  }, [])

  useEffect(() => {
    if (!isOpen) {
      setIsClosing(false)
    }
  }, [isOpen])

  // Prevent body scroll when menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isOpen])

  const handleClose = () => {
    setIsClosing(true)
    setTimeout(onClose, 300) // Match this with the transition duration
  }

  if (!isMounted || (!isOpen && !isClosing)) {
    return null
  }

  const menuContent = (
    <div
      className={cn(
        "mobile-menu-overlay bg-black/70 backdrop-blur-md transition-opacity duration-300",
        isClosing ? "opacity-0" : "opacity-100",
      )}
      onClick={handleClose}
      role="dialog"
      aria-modal="true"
      aria-label="Mobile navigation menu"
    >
      <div
        className={cn(
          "mobile-menu-panel w-3/4 max-w-xs bg-gradient-to-b from-[#2A7C6C] to-[#1E5A4F] backdrop-blur-md shadow-xl border-r border-[#2A7C6C]/30 flex flex-col transition-transform duration-300 ease-in-out",
          isClosing ? "-translate-x-full" : "translate-x-0",
        )}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header with close button */}
        <div className="flex justify-end p-4 pb-2 flex-shrink-0">
          <Button
            variant="ghost"
            size="icon"
            onClick={handleClose}
            className="text-white hover:bg-white/20 transition-colors duration-200"
            aria-label="Close mobile menu"
          >
            <X className="h-6 w-6" />
          </Button>
        </div>

        {/* Navigation items */}
        <nav className="flex-1 px-4 pb-4 space-y-2 overflow-y-auto">
          {navItems.map((item, index) => {
            const href = item.name === "Home" ? "/" : `/${item.name.toLowerCase()}`;
            return (
              <Link
                key={item.name}
                href={href as "/" | "/store" | "/aboutus" | "/faq" | "/contact"}
                className={cn(
                  "flex items-center py-4 px-4 text-lg font-medium transition-all duration-300 rounded-xl w-full group",
                  "hover:bg-white/20 hover:scale-[1.02] hover:shadow-lg",
                  isActive(item.name)
                    ? "text-white bg-white/30 shadow-lg border-l-4 border-white"
                    : "text-white/90 hover:text-white"
                )}
                onClick={handleClose}
                style={{
                  animationDelay: `${index * 50}ms`,
                  animation: isClosing ? 'none' : 'slideInFromLeft 0.3s ease-out forwards'
                }}
              >
                <item.icon className="mr-3 h-5 w-5 flex-shrink-0 transition-transform duration-200 group-hover:scale-110" />
                <span className="flex-1 font-medium">{item.name}</span>
              </Link>
            );
          })}
        </nav>
      </div>
    </div>
  )

  return createPortal(menuContent, document.body)
}

