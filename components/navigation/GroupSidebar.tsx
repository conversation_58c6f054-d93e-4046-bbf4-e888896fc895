
"use client";

import { useState } from "react";
import { usePathname } from "next/navigation";
import {
  ChevronLeft,
  ChevronRight,
  Store,
  Users,
  ShoppingCart,
  Truck,
  BarChart,
  Settings,
  HelpCircle,
  LogOut,
  Home,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import { useAuth } from "@/context/AuthContext";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

import Link from "next/link";
import Image from "next/image";

const mainNavItems = [
  { icon: Store, label: "Products", href: "/group/:groupId/products" },
  { icon: Users, label: "Members", href: "/group/:groupId/groupmembers" },
  { icon: ShoppingCart, label: "Group Cart", href: "/group/:groupId/groupcart" },
  { icon: Truck, label: "Deliveries", href: "/group/:groupId/deliveries" },
  { icon: Bar<PERSON>hart, label: "Progress", href: "/group/:groupId/progress" },
];

const secondaryNavItems = [
  { icon: Settings, label: "Group Settings", href: "/group/:groupId/settings" },
  { icon: HelpCircle, label: "Help", href: "/group/:groupId/help" },
];

interface GroupSidebarProps {
  groupName: string;
  groupId: string;
  groupLogo?: string;
}

export function GroupSidebar({ groupName, groupId, groupLogo = "/StokvelLogo.avif" }: GroupSidebarProps) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const pathname = usePathname();
  const { logout } = useAuth();

  const isLinkActive = (href: string) => {
    const groupPath = href.replace(":groupId", groupId);
    if (groupPath === `/group/${groupId}`) {
      return pathname === groupPath;
    }
    return pathname.startsWith(groupPath);
  };

  const renderNavItem = (item: { icon: React.ElementType; label: string; href: string }) => {
    const isActive = isLinkActive(item.href);
    const groupPath = item.href.replace(":groupId", groupId);
    return (
      <TooltipProvider key={item.label}>
        <Tooltip>
          <TooltipTrigger asChild>
            <Link href={groupPath as `/group/${string}` | `/group/${string}/products` | `/group/${string}/orders` | `/group/${string}/members` | `/group/${string}/analytics` | `/group/${string}/settings`}>
              <Button
                variant={isActive ? "secondary" : "ghost"}
                className={cn(
                  "w-full justify-start",
                  !isSidebarOpen && "px-2",
                  isActive && "bg-[#2A7C6C] text-white hover:bg-[#236658] hover:text-white"
                )}
              >
                <item.icon className="h-5 w-5 mr-2" />
                {isSidebarOpen && <span className="font-sans">{item.label}</span>}
              </Button>
            </Link>
          </TooltipTrigger>
          <TooltipContent side="right" sideOffset={10}>
            {item.label}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  };

  return (
    <aside
      className={cn(
        "relative flex flex-col",
        "min-h-screen bg-white border-r transition-all duration-300 ease-in-out",
        "shadow-[1px_0_3px_rgba(0,0,0,0.1)]",
        isSidebarOpen ? "w-64" : "w-20"
      )}

    >
      {/* Toggle Button */}
      <Button
        variant="ghost"
        size="icon"
        className="absolute -right-4 top-6 z-50 h-8 w-8 rounded-full border bg-white shadow-md"
        onClick={() => setIsSidebarOpen(!isSidebarOpen)}
        aria-label={isSidebarOpen ? "Collapse sidebar" : "Expand sidebar"}
      >
        {isSidebarOpen ? <ChevronLeft className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
      </Button>

      {/* Group Logo and Name Section - Navigate to Home */}
      <div className="p-6 relative" style={{ zIndex: 10 }}>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Link href="/" className="block">
                <Button
                  variant="ghost"
                  className="w-full justify-start p-0 hover:bg-gray-50 transition-colors duration-200"
                >
                  <div className="flex items-center gap-3">
                    <div className="relative h-10 w-10 overflow-hidden rounded-full">
                      <Image
                        src={groupLogo || "/placeholder.svg"}
                        alt={`${groupName} Logo`}
                        fill
                        className="object-cover"
                        sizes="40px"
                      />
                    </div>
                    {isSidebarOpen && (
                      <div className="flex items-center gap-2">
                        <h2
                          className="text-lg font-semibold text-[#2A7C6C] hover:text-[#236358] transition-colors duration-200"
                          style={{
                            fontFamily: "ClashDisplay-Variable, sans-serif",
                            letterSpacing: "-0.02em",
                          }}
                        >
                          {groupName}
                        </h2>
                        <Home className="h-4 w-4 text-[#2A7C6C] opacity-60" />
                      </div>
                    )}
                  </div>
                </Button>
              </Link>
            </TooltipTrigger>
            <TooltipContent side="right" sideOffset={10}>
              Go to Home Page
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      {/* Main Navigation */}
      <nav className="flex-1 px-4 overflow-y-auto relative z-0">
        <div className="space-y-1">{mainNavItems.map(renderNavItem)}</div>

        <Separator className="my-4" />

        {/* Secondary Navigation */}
        <div className="space-y-1">{secondaryNavItems.map(renderNavItem)}</div>
      </nav>

      {/* Exit Group Section */}
      <div className="p-4 mt-auto border-t">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                className={cn(
                  "w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50",
                  !isSidebarOpen && "px-2"
                )}
                onClick={logout}
                aria-label="Exit Group"
              >
                <LogOut className="h-5 w-5 mr-2" />
                {isSidebarOpen && <span className="font-sans">Exit Group</span>}
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right" sideOffset={10}>
              Exit Group
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </aside>
  );
}
