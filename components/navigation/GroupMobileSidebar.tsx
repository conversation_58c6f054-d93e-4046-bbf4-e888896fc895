// components/navigation/GroupMobileSidebar.tsx

"use client";

import { useState, useEffect } from 'react'
import { createPortal } from 'react-dom'
import Link from 'next/link'
import Image from 'next/image'
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import {
  X,
  Store,
  Users,
  ShoppingCart,
  Truck,
  BarChart,
  Settings,
  HelpCircle,
  LogOut,
  Home
} from 'lucide-react'
import { useAuth } from "@/context/AuthContext"
import { usePathname } from "next/navigation"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

const mainNavItems = [
  { icon: Store, label: "Products", href: "/group/:groupId/products" },
  { icon: Users, label: "Members", href: "/group/:groupId/groupmembers" },
  { icon: ShoppingCart, label: "Group Cart", href: "/group/:groupId/groupcart" },
  { icon: Truck, label: "Deliveries", href: "/group/:groupId/deliveries" },
  { icon: Bar<PERSON>hart, label: "Progress", href: "/group/:groupId/progress" },
];

const secondaryNavItems = [
  { icon: Settings, label: "Group Settings", href: "/group/:groupId/settings" },
  { icon: HelpCircle, label: "Help", href: "/group/:groupId/help" },
];

interface GroupMobileSidebarProps {
  isOpen: boolean
  onClose: () => void
  groupName: string
  groupId: string
  groupLogo?: string
}

export function GroupMobileSidebar({ 
  isOpen, 
  onClose, 
  groupName, 
  groupId, 
  groupLogo = "/StokvelLogo.avif" 
}: GroupMobileSidebarProps) {
  const [isClosing, setIsClosing] = useState(false)
  const [isMounted, setIsMounted] = useState(false)
  const pathname = usePathname()
  const { logout } = useAuth()

  useEffect(() => {
    setIsMounted(true)
  }, [])

  useEffect(() => {
    if (!isOpen) {
      setIsClosing(false)
    }
  }, [isOpen])

  // Prevent body scroll when menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isOpen])

  const handleClose = () => {
    setIsClosing(true)
    setTimeout(onClose, 300) // Match this with the transition duration
  }

  const isLinkActive = (href: string) => {
    const groupPath = href.replace(":groupId", groupId);
    if (groupPath === `/group/${groupId}`) {
      return pathname === groupPath;
    }
    return pathname.startsWith(groupPath);
  };

  if (!isMounted || (!isOpen && !isClosing)) {
    return null
  }

  const renderNavItem = (item: { icon: React.ElementType; label: string; href: string }, index: number) => {
    const isActive = isLinkActive(item.href);
    const groupPath = item.href.replace(":groupId", groupId);
    
    return (
      <Link
        key={item.label}
        href={groupPath as any}
        className={cn(
          "flex items-center py-4 px-4 text-lg font-medium transition-all duration-300 rounded-xl w-full group",
          "hover:bg-white/20 hover:scale-[1.02] hover:shadow-lg",
          isActive
            ? "text-white bg-white/30 shadow-lg border-l-4 border-white"
            : "text-white/90 hover:text-white"
        )}
        onClick={handleClose}
        style={{
          animationDelay: `${index * 50}ms`,
          animation: isClosing ? 'none' : 'slideInFromLeft 0.3s ease-out forwards'
        }}
      >
        <item.icon className="mr-3 h-5 w-5 flex-shrink-0 transition-transform duration-200 group-hover:scale-110" />
        <span className="flex-1 font-medium">{item.label}</span>
      </Link>
    );
  };

  const menuContent = (
    <div
      className="fixed inset-0 z-[9999] bg-black/70 backdrop-blur-md transition-opacity duration-300"
      style={{
        opacity: isClosing ? 0 : 1,
      }}
      onClick={handleClose}
      role="dialog"
      aria-modal="true"
      aria-label="Group mobile navigation menu"
    >
      <div
        className="fixed left-0 top-0 h-full w-3/4 max-w-xs bg-gradient-to-b from-[#2A7C6C] to-[#1E5A4F] backdrop-blur-md shadow-xl border-r border-[#2A7C6C]/30 flex flex-col transition-transform duration-300 ease-in-out"
        style={{
          transform: isClosing ? 'translateX(-100%)' : 'translateX(0)',
          zIndex: 10000,
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header with group info and close button */}
        <div className="flex items-center justify-between p-4 pb-2 flex-shrink-0">
          <Link
            href="/"
            className="flex items-center gap-3 hover:bg-white/10 rounded-lg p-2 -m-2 transition-colors duration-200"
            onClick={handleClose}
          >
            <div className="relative h-10 w-10 overflow-hidden rounded-full">
              <Image
                src={groupLogo}
                alt={`${groupName} Logo`}
                fill
                className="object-cover"
                sizes="40px"
              />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-white truncate max-w-[120px] flex items-center gap-2">
                {groupName}
                <Home className="h-4 w-4 text-white/60" />
              </h2>
              <p className="text-xs text-white/70">Tap to go home</p>
            </div>
          </Link>
          <Button
            variant="ghost"
            size="icon"
            onClick={handleClose}
            className="text-white hover:bg-white/20 transition-colors duration-200 flex-shrink-0"
            aria-label="Close mobile menu"
          >
            <X className="h-6 w-6" />
          </Button>
        </div>

        <Separator className="bg-white/20" />

        {/* Main Navigation */}
        <nav className="flex-1 px-4 py-4 space-y-2 overflow-y-auto">
          <div className="space-y-2">
            {mainNavItems.map((item, index) => renderNavItem(item, index))}
          </div>

          <Separator className="my-4 bg-white/20" />

          {/* Secondary Navigation */}
          <div className="space-y-2">
            {secondaryNavItems.map((item, index) => renderNavItem(item, mainNavItems.length + index))}
          </div>
        </nav>

        {/* Exit Group Section */}
        <div className="p-4 border-t border-white/20">
          <Button
            variant="ghost"
            className="w-full justify-start text-red-300 hover:text-red-200 hover:bg-red-500/20 transition-all duration-200"
            onClick={() => {
              logout();
              handleClose();
            }}
            aria-label="Exit Group"
          >
            <LogOut className="h-5 w-5 mr-3" />
            <span className="font-medium">Exit Group</span>
          </Button>
        </div>
      </div>
    </div>
  )

  return createPortal(menuContent, document.body)
}
