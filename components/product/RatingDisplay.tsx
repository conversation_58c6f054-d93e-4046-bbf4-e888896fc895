"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { 
  ThumbsUp, 
  Flag, 
  User, 
  Calendar,
  Shield,
  Edit,
  Trash2
} from "lucide-react";
import { RatingStars } from "./RatingStars";
import { RatingForm } from "./RatingForm";
import { 
  useGetProductRatingSummaryQuery,
  useGetProductRatingsQuery,
  useGetUserProductRatingQuery,
  useRatingActionMutation,
  useDeleteRatingMutation
} from "@/lib/redux/features/ratings/ratingsApiSlice";
import { motion, AnimatePresence } from "framer-motion";
import { toast } from "sonner";

interface RatingDisplayProps {
  productId: string;
  userId?: string;
}

export function RatingDisplay({ productId, userId }: RatingDisplayProps) {
  const [showRatingForm, setShowRatingForm] = useState(false);
  const [editingRating, setEditingRating] = useState(false);
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'highest' | 'lowest' | 'helpful'>('newest');
  
  // Fetch data
  const { data: summary, isLoading: summaryLoading } = useGetProductRatingSummaryQuery(productId);
  const { data: ratingsData, isLoading: ratingsLoading } = useGetProductRatingsQuery({
    productId,
    page: 1,
    limit: 10,
    sortBy
  });
  const { data: userRatingData } = useGetUserProductRatingQuery(
    { productId, userId: userId || '' },
    { skip: !userId }
  );

  // Mutations
  const [ratingAction] = useRatingActionMutation();
  const [deleteRating] = useDeleteRatingMutation();

  const userRating = userRatingData?.rating;
  const hasUserRated = !!userRating;

  const handleHelpful = async (ratingId: string) => {
    if (!userId) {
      toast.error('Please log in to mark reviews as helpful');
      return;
    }

    try {
      await ratingAction({
        ratingId,
        userId,
        action: 'helpful'
      }).unwrap();
      toast.success('Marked as helpful!');
    } catch (error) {
      toast.error('Failed to mark as helpful');
    }
  };

  const handleReport = async (ratingId: string) => {
    if (!userId) {
      toast.error('Please log in to report reviews');
      return;
    }

    try {
      await ratingAction({
        ratingId,
        userId,
        action: 'report'
      }).unwrap();
      toast.success('Review reported');
    } catch (error) {
      toast.error('Failed to report review');
    }
  };

  const handleDeleteRating = async () => {
    if (!userRating || !userId) return;

    const confirmed = window.confirm('Are you sure you want to delete your rating?');
    if (!confirmed) return;

    try {
      await deleteRating({
        ratingId: userRating._id,
        userId
      }).unwrap();
      toast.success('Rating deleted successfully');
      setEditingRating(false);
    } catch (error) {
      toast.error('Failed to delete rating');
    }
  };

  const formatDate = (date: string | Date) => {
    return new Date(date).toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (summaryLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-32 bg-gray-200 rounded mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Rating Summary */}
      {summary && (
        <Card className="shadow-lg rounded-xl">
          <CardHeader className="p-4 md:p-6">
            <CardTitle className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
              <span className="text-lg md:text-xl font-bold">Customer Reviews</span>
              {userId && !hasUserRated && (
                <Button
                  onClick={() => setShowRatingForm(true)}
                  className="bg-purple-600 hover:bg-purple-700 text-sm md:text-base h-9 md:h-10"
                  size="sm"
                >
                  Write a Review
                </Button>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 md:space-y-6 p-4 md:p-6">
            {/* Overall Rating - Mobile Optimized */}
            <div className="flex flex-col md:flex-row md:items-center gap-4 md:gap-6">
              <div className="text-center md:text-left">
                <div className="text-3xl md:text-4xl font-bold text-purple-600 mb-1">
                  {summary.averageRating.toFixed(1)}
                </div>
                <RatingStars rating={summary.averageRating} size="md" />
                <p className="text-xs md:text-sm text-gray-600 mt-1">
                  {summary.totalRatings} review{summary.totalRatings !== 1 ? 's' : ''}
                </p>
              </div>

              {/* Rating Distribution - Mobile Optimized */}
              <div className="flex-1 space-y-1.5 md:space-y-2">
                {[5, 4, 3, 2, 1].map((star) => (
                  <div key={star} className="flex items-center gap-2 md:gap-3">
                    <span className="text-xs md:text-sm w-6 md:w-8">{star} ★</span>
                    <Progress
                      value={summary.totalRatings > 0 ? (summary.ratingDistribution[star as keyof typeof summary.ratingDistribution] / summary.totalRatings) * 100 : 0}
                      className="flex-1 h-1.5 md:h-2"
                    />
                    <span className="text-xs md:text-sm text-gray-600 w-6 md:w-8">
                      {summary.ratingDistribution[star as keyof typeof summary.ratingDistribution]}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* User's Rating - Mobile Optimized */}
            {hasUserRated && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 md:p-4">
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 mb-2">
                  <h4 className="font-medium text-blue-900 text-sm md:text-base">Your Rating</h4>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setEditingRating(true)}
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleDeleteRating}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      Delete
                    </Button>
                  </div>
                </div>
                <RatingStars rating={userRating.rating} size="md" />
                {userRating.title && (
                  <h5 className="font-medium mt-2">{userRating.title}</h5>
                )}
                {userRating.review && (
                  <p className="text-gray-700 mt-1">{userRating.review}</p>
                )}
                <p className="text-xs text-gray-500 mt-2">
                  Reviewed on {formatDate(userRating.createdAt)}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Rating Form */}
      <AnimatePresence>
        {(showRatingForm || editingRating) && userId && (
          <RatingForm
            productId={productId}
            userId={userId}
            existingRating={editingRating ? userRating : null}
            onSuccess={() => {
              setShowRatingForm(false);
              setEditingRating(false);
            }}
            onCancel={() => {
              setShowRatingForm(false);
              setEditingRating(false);
            }}
          />
        )}
      </AnimatePresence>

      {/* Individual Ratings */}
      {ratingsData && ratingsData.ratings.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>All Reviews ({ratingsData.total})</CardTitle>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="px-3 py-1 border border-gray-300 rounded-md text-sm"
              >
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="highest">Highest Rated</option>
                <option value="lowest">Lowest Rated</option>
                <option value="helpful">Most Helpful</option>
              </select>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {ratingsData.ratings.map((rating, index) => (
              <motion.div
                key={rating._id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <div className="border-b border-gray-200 pb-4 last:border-b-0">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                        <User className="h-5 w-5 text-purple-600" />
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">
                            {typeof rating.userId === 'object' && 'name' in rating.userId 
                              ? rating.userId.name 
                              : 'Anonymous'}
                          </span>
                          {rating.isVerifiedPurchase && (
                            <Badge variant="secondary" className="bg-green-100 text-green-800">
                              <Shield className="h-3 w-3 mr-1" />
                              Verified Purchase
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-2 mt-1">
                          <RatingStars rating={rating.rating} size="sm" />
                          <span className="text-xs text-gray-500">
                            <Calendar className="h-3 w-3 inline mr-1" />
                            {formatDate(rating.createdAt)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {rating.title && (
                    <h4 className="font-medium mb-2">{rating.title}</h4>
                  )}

                  {rating.review && (
                    <p className="text-gray-700 mb-3">{rating.review}</p>
                  )}

                  <div className="flex items-center gap-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleHelpful(rating._id)}
                      className="text-gray-600 hover:text-purple-600"
                    >
                      <ThumbsUp className="h-4 w-4 mr-1" />
                      Helpful ({rating.helpfulVotes})
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleReport(rating._id)}
                      className="text-gray-600 hover:text-red-600"
                    >
                      <Flag className="h-4 w-4 mr-1" />
                      Report
                    </Button>
                  </div>
                </div>
              </motion.div>
            ))}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
