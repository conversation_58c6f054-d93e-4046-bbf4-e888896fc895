# Redux Toolkit Integration - Location Selection Modal

## Overview

The Location Selection Modal system has been successfully refactored to use **Redux Toolkit (RTK) and RTK Query** for complete consistency with the existing codebase architecture.

## Architecture

### State Management Pattern

```typescript
// ✅ CORRECT: Using RTK Query hooks
const { data: allGroups = [] } = useGetAllStokvelGroupsQuery();
const [joinGroup] = useJoinGroupMutation();
const [storeLocationRequest] = useStoreLocationRequestMutation();

// ✅ CORRECT: Using custom hook based on RTK Query
const {
  selectionData,
  handleProvinceChange,
  handleCityChange,
  handleTownshipChange,
  handleLocationChange,
  isSelectionComplete,
} = useLocations();
```

### Key Components Integration

#### 1. LocationSelectionModal.tsx
- **Uses**: `useLocations()` hook (RTK Query based)
- **Pattern**: Direct hook usage, no Context dependencies
- **State**: Local component state for UI, RTK Query for data

#### 2. LocationSelectionButton.tsx
- **Uses**: Direct props and local state
- **Pattern**: Self-contained component
- **State**: Local state for modal open/close

#### 3. LocationSelectionStep.tsx
- **Uses**: `useGetAllStokvelGroupsQuery`, `useJoinGroupMutation`, `useStoreLocationRequestMutation`
- **Pattern**: RTK Query hooks for all API operations
- **State**: Local state + RTK Query cache

#### 4. GroupSelectionStep.tsx
- **Uses**: `useGetAllStokvelGroupsQuery`
- **Pattern**: RTK Query for data fetching
- **State**: Local state + RTK Query cache

## RTK Query Hooks Used

### Location Management
```typescript
// From useLocations() hook
useGetProvincesQuery()
useLazyGetCitiesByProvinceQuery()
useLazyGetTownshipsByCityQuery()
useLazyGetLocationsByTownshipQuery()
```

### Group Management
```typescript
// From groupsApiSlice
useGetAllStokvelGroupsQuery()
useJoinGroupMutation()
useStoreLocationRequestMutation()
```

### Cart Management
```typescript
// From cartApiSlice
useAddToCartMutation()
```

## Data Flow

```
User Interaction
       ↓
LocationSelectionButton (Local State)
       ↓
LocationSelectionModal (useLocations hook)
       ↓
RTK Query (API calls)
       ↓
Redux Store (Cached data)
       ↓
Component Re-render (Automatic)
```

## Benefits of RTK Integration

### 1. Consistency
- ✅ Follows established codebase patterns
- ✅ Same state management approach across all components
- ✅ Consistent error handling and loading states

### 2. Performance
- ✅ Automatic caching with RTK Query
- ✅ Optimistic updates
- ✅ Background refetching
- ✅ Deduplication of requests

### 3. Developer Experience
- ✅ Type safety with TypeScript
- ✅ DevTools integration
- ✅ Predictable state updates
- ✅ Easy testing

### 4. Maintainability
- ✅ Centralized API logic
- ✅ Reusable hooks
- ✅ Clear separation of concerns
- ✅ Scalable architecture

## Removed Dependencies

### Context Providers (Removed)
```typescript
// ❌ REMOVED: Context-based approach
LocationModalProvider
useLocationModal()
useLocationSelection()
useWizardLocationSelection()
```

### Replaced With
```typescript
// ✅ CURRENT: RTK-based approach
useLocations() // RTK Query based
useGetAllStokvelGroupsQuery() // Direct RTK Query
Local component state for UI
```

## File Structure

```
components/
├── modals/
│   ├── LocationSelectionModal.tsx     // Uses useLocations()
│   └── wizard-steps/
│       ├── LocationSelectionStep.tsx  // Uses RTK Query hooks
│       └── GroupSelectionStep.tsx     // Uses RTK Query hooks
├── ui/
│   ├── LocationSelectionButton.tsx    // Self-contained
│   └── enhanced-location-selects-v2.tsx
lib/
├── redux/
│   ├── hooks/
│   │   └── useLocations.ts            // RTK Query based
│   └── features/
│       ├── groups/groupsApiSlice.ts   // RTK Query API
│       └── locations/locationsApiSlice.ts
└── utils/
    └── locationUtils.ts               // Pure utility functions
```

## Testing Integration

The components can be tested using standard RTK Query testing patterns:

```typescript
// Mock RTK Query hooks
jest.mock('@/lib/redux/features/groups/groupsApiSlice', () => ({
  useGetAllStokvelGroupsQuery: () => ({
    data: mockGroups,
    isLoading: false,
    error: null
  })
}));
```

## Migration Complete

✅ **All Context providers removed**
✅ **All components use RTK Query**
✅ **Consistent with existing codebase**
✅ **No breaking changes to API**
✅ **Full TypeScript support**
✅ **Performance optimized**

The Location Selection Modal system now fully adheres to the established Redux Toolkit patterns used throughout the StockvelMarket application.
