

// models/GroupOrder.ts

import mongoose, { Schema, Document, Model } from 'mongoose';

export interface IUserContribution {
  userId: mongoose.Types.ObjectId;
  userName: string;
  totalSpent: number;
  items: {
    product: mongoose.Types.ObjectId;
    quantity: number;
    subtotal: number;
  }[];
  contributionPercentage: number;
}

export interface IGroupOrderMilestone {
  name: string;
  targetAmount: number;
  currentAmount: number;
  isReached: boolean;
  reachedAt?: Date;
}

export enum GroupOrderStatus {
  DRAFT = 'draft',
  PENDING = 'pending',
  PROCESSING = 'processing',
  READY_FOR_DELIVERY = 'ready_for_delivery',
  SHIPPED = 'shipped',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

export interface IGroupOrder extends Document {
  groupId: mongoose.Types.ObjectId;
  orderItems: {
    product: mongoose.Types.ObjectId;
    quantity: number;
    userId: mongoose.Types.ObjectId;
    unitPrice: number;
    subtotal: number;
  }[];
  totalOrderValue: number;
  userContributions: IUserContribution[];
  status: GroupOrderStatus;
  statusHistory: {
    status: GroupOrderStatus;
    timestamp: Date;
    updatedBy: mongoose.Types.ObjectId;
  }[];
  milestones: IGroupOrderMilestone[];
  bulkDiscountTiers: {
    threshold: number;
    discountPercentage: number;
  }[];
  appliedDiscountTier?: {
    threshold: number;
    discountPercentage: number;
  };
  estimatedDeliveryDate?: Date;
  orderPlacedAt: Date;
  lastUpdatedAt: Date;
  groupOrderNotes?: string;
  paymentStatus: 'unpaid' | 'partially_paid' | 'fully_paid';
  lastModifiedBy?: mongoose.Types.ObjectId;
}

const UserContributionSchema: Schema = new Schema({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  userName: { type: String, required: true },
  totalSpent: { type: Number, default: 0 },
  items: [
    {
      product: { type: Schema.Types.ObjectId, ref: 'Product', required: true },
      quantity: { type: Number, required: true },
      subtotal: { type: Number, required: true }
    }
  ],
  contributionPercentage: { type: Number, default: 0 }
});

const GroupOrderMilestoneSchema: Schema = new Schema({
  name: { type: String, required: true },
  targetAmount: { type: Number, required: true },
  currentAmount: { type: Number, default: 0 },
  isReached: { type: Boolean, default: false },
  reachedAt: { type: Date }
});

const validateStatusTransition = (
  currentStatus: GroupOrderStatus,
  newStatus: GroupOrderStatus
): boolean => {
  const validTransitions: Record<GroupOrderStatus, GroupOrderStatus[]> = {
    [GroupOrderStatus.DRAFT]: [GroupOrderStatus.PENDING, GroupOrderStatus.CANCELLED],
    [GroupOrderStatus.PENDING]: [GroupOrderStatus.PROCESSING, GroupOrderStatus.CANCELLED],
    [GroupOrderStatus.PROCESSING]: [GroupOrderStatus.READY_FOR_DELIVERY, GroupOrderStatus.CANCELLED],
    [GroupOrderStatus.READY_FOR_DELIVERY]: [GroupOrderStatus.SHIPPED, GroupOrderStatus.CANCELLED],
    [GroupOrderStatus.SHIPPED]: [GroupOrderStatus.COMPLETED, GroupOrderStatus.PROCESSING],
    [GroupOrderStatus.COMPLETED]: [],
    [GroupOrderStatus.CANCELLED]: []
  };
  return validTransitions[currentStatus].includes(newStatus);
};

const GroupOrderSchema: Schema<IGroupOrder> = new Schema(
  {
    groupId: { type: Schema.Types.ObjectId, ref: 'StokvelGroup', required: true, index: true },
    orderItems: [
      {
        product: { type: Schema.Types.ObjectId, ref: 'Product', required: true },
        quantity: { type: Number, required: true },
        userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
        unitPrice: { type: Number, required: true },
        subtotal: { type: Number, required: true }
      }
    ],
    totalOrderValue: { type: Number, default: 0 },
    userContributions: [UserContributionSchema],
    status: {
      type: String,
      enum: Object.values(GroupOrderStatus),
      default: GroupOrderStatus.DRAFT,
      validate: {
        validator: function (this: IGroupOrder, newStatus: GroupOrderStatus) {
          return validateStatusTransition(this.status, newStatus);
        },
        message: 'Invalid status transition'
      }
    },
    statusHistory: [
      {
        status: { type: String, enum: Object.values(GroupOrderStatus), required: true },
        timestamp: { type: Date, default: Date.now },
        updatedBy: { type: Schema.Types.ObjectId, ref: 'User', required: true }
      }
    ],
    milestones: [GroupOrderMilestoneSchema],
    bulkDiscountTiers: [
      {
        threshold: { type: Number, required: true },
        discountPercentage: { type: Number, required: true }
      }
    ],
    appliedDiscountTier: {
      threshold: { type: Number },
      discountPercentage: { type: Number }
    },
    estimatedDeliveryDate: { type: Date },
    orderPlacedAt: { type: Date, default: Date.now, index: true },
    lastUpdatedAt: { type: Date, default: Date.now, index: true },
    groupOrderNotes: { type: String },
    paymentStatus: { 
      type: String, 
      enum: ['unpaid', 'partially_paid', 'fully_paid'], 
      default: 'unpaid'
    },
    lastModifiedBy: { type: Schema.Types.ObjectId, ref: 'User' }
  },
  { timestamps: true }
);

// Add compound indexes for better query performance
GroupOrderSchema.index({ groupId: 1, orderPlacedAt: -1 });
GroupOrderSchema.index({ groupId: 1, status: 1 });
GroupOrderSchema.index({ 'userContributions.userId': 1, orderPlacedAt: -1 });

// Pre-save middleware to update timestamps, contributions, milestones, and status history.
GroupOrderSchema.pre('save', function (next) {
  const order = this as IGroupOrder;

  order.lastUpdatedAt = new Date();

  if (order.totalOrderValue) {
    order.userContributions = order.userContributions.map((contribution) => ({
      ...contribution,
      contributionPercentage: (contribution.totalSpent / order.totalOrderValue) * 100
    }));
  }

  order.milestones.forEach((milestone) => {
    if (order.totalOrderValue >= milestone.targetAmount && !milestone.isReached) {
      milestone.isReached = true;
      milestone.reachedAt = new Date();
    }
  });

  if (order.bulkDiscountTiers && order.bulkDiscountTiers.length > 0) {
    const applicableTier = order.bulkDiscountTiers
      .filter((tier) => order.totalOrderValue >= tier.threshold)
      .sort((a, b) => b.threshold - a.threshold)[0];
    if (applicableTier) {
      order.appliedDiscountTier = applicableTier;
    }
  }

  if (this.isModified('status')) {
    order.statusHistory.push({
      status: order.status,
      timestamp: new Date(),
      updatedBy: order.lastModifiedBy || new mongoose.Types.ObjectId()
    });
  }

  next();
});

// Indexes for faster queries
GroupOrderSchema.index({ groupId: 1 });
GroupOrderSchema.index({ status: 1 });
GroupOrderSchema.index({ orderPlacedAt: -1 });

// Compound index to optimize queries filtering by group and status:
GroupOrderSchema.index({ groupId: 1, status: 1 });

// Additional indexes on payment status and last updated date:
GroupOrderSchema.index({ paymentStatus: 1 });
GroupOrderSchema.index({ lastUpdatedAt: -1 });

// Use existing model if available, or compile new model.
export const GroupOrder = mongoose.models.GroupOrder || mongoose.model<IGroupOrder>('GroupOrder', GroupOrderSchema);

export type GroupOrderModel = typeof GroupOrder;
