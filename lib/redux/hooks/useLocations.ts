// lib/redux/hooks/useLocations.ts

import { useState, useCallback, useMemo } from 'react';
import {
  useGetProvincesQuery,
  useLazyGetCitiesByProvinceQuery,
  useLazyGetTownshipsByCityQuery,
  useLazyGetLocationsByTownshipQuery,
  useCreateProvinceMutation,
  useCreateCityMutation,
  useCreateTownshipMutation,
  useCreateLocationMutation,
  useUpdateProvinceMutation,
  useUpdateCityMutation,
  useUpdateTownshipMutation,
  useUpdateLocationMutation,
  useDeleteProvinceMutation,
  useDeleteCityMutation,
  useDeleteTownshipMutation,
  useDeleteLocationMutation,
  useSearchLocationsQuery,
  useGetLocationStatsQuery,
} from '../features/locations/locationsApiSlice';
import type {
  Province,
  City,
  Township,
  Location,
  LocationSelectionData,
  CreateProvinceData,
  CreateCityData,
  CreateTownshipData,
  CreateLocationData,
  UpdateProvinceData,
  UpdateCityData,
  UpdateTownshipData,
  UpdateLocationData,
} from '@/types/locations';

/**
 * Custom hook for location management with hierarchical selection support
 */
export function useLocations() {
  // Selection state
  const [selectedProvinceId, setSelectedProvinceId] = useState('');
  const [selectedCityId, setSelectedCityId] = useState('');
  const [selectedTownshipId, setSelectedTownshipId] = useState('');
  const [selectedLocationId, setSelectedLocationId] = useState('');

  // API hooks
  const { data: provincesData, isLoading: provincesLoading, error: provincesError } = useGetProvincesQuery();
  const [getCities, { isLoading: citiesLoading }] = useLazyGetCitiesByProvinceQuery();
  const [getTownships, { isLoading: townshipsLoading }] = useLazyGetTownshipsByCityQuery();
  const [getLocations, { isLoading: locationsLoading }] = useLazyGetLocationsByTownshipQuery();

  // Mutation hooks
  const [createProvince] = useCreateProvinceMutation();
  const [createCity] = useCreateCityMutation();
  const [createTownship] = useCreateTownshipMutation();
  const [createLocation] = useCreateLocationMutation();
  const [updateProvince] = useUpdateProvinceMutation();
  const [updateCity] = useUpdateCityMutation();
  const [updateTownship] = useUpdateTownshipMutation();
  const [updateLocation] = useUpdateLocationMutation();
  const [deleteProvince] = useDeleteProvinceMutation();
  const [deleteCity] = useDeleteCityMutation();
  const [deleteTownship] = useDeleteTownshipMutation();
  const [deleteLocation] = useDeleteLocationMutation();

  // Available options state
  const [availableCities, setAvailableCities] = useState<City[]>([]);
  const [availableTownships, setAvailableTownships] = useState<Township[]>([]);
  const [availableLocations, setAvailableLocations] = useState<Location[]>([]);

  // Derived data
  const provinces = provincesData?.provinces || [];

  // Selection handlers
  const handleProvinceChange = useCallback(async (provinceId: string) => {
    setSelectedProvinceId(provinceId);
    setSelectedCityId('');
    setSelectedTownshipId('');
    setSelectedLocationId('');
    setAvailableCities([]);
    setAvailableTownships([]);
    setAvailableLocations([]);

    if (provinceId) {
      try {
        const result = await getCities(provinceId).unwrap();
        setAvailableCities(result.cities || []);
      } catch (error) {
        console.error('Error fetching cities:', error);
        setAvailableCities([]);
      }
    }
  }, [getCities]);

  const handleCityChange = useCallback(async (cityId: string) => {
    setSelectedCityId(cityId);
    setSelectedTownshipId('');
    setSelectedLocationId('');
    setAvailableTownships([]);
    setAvailableLocations([]);

    if (cityId) {
      try {
        const result = await getTownships(cityId).unwrap();
        setAvailableTownships(result.townships || []);
      } catch (error) {
        console.error('Error fetching townships:', error);
        setAvailableTownships([]);
      }
    }
  }, [getTownships]);

  const handleTownshipChange = useCallback(async (townshipId: string) => {
    setSelectedTownshipId(townshipId);
    setSelectedLocationId('');
    setAvailableLocations([]);

    if (townshipId) {
      try {
        const result = await getLocations(townshipId).unwrap();
        setAvailableLocations(result.locations || []);
      } catch (error) {
        console.error('Error fetching locations:', error);
        setAvailableLocations([]);
      }
    }
  }, [getLocations]);

  const handleLocationChange = useCallback((locationId: string) => {
    setSelectedLocationId(locationId);
  }, []);

  // Reset selection
  const resetSelection = useCallback(() => {
    setSelectedProvinceId('');
    setSelectedCityId('');
    setSelectedTownshipId('');
    setSelectedLocationId('');
    setAvailableCities([]);
    setAvailableTownships([]);
    setAvailableLocations([]);
  }, []);

  // Set selection programmatically
  const setSelection = useCallback(async (selection: {
    provinceId?: string;
    cityId?: string;
    townshipId?: string;
    locationId?: string;
  }) => {
    if (selection.provinceId) {
      await handleProvinceChange(selection.provinceId);
    }
    if (selection.cityId) {
      await handleCityChange(selection.cityId);
    }
    if (selection.townshipId) {
      await handleTownshipChange(selection.townshipId);
    }
    if (selection.locationId) {
      handleLocationChange(selection.locationId);
    }
  }, [handleProvinceChange, handleCityChange, handleTownshipChange, handleLocationChange]);

  // CRUD operations
  const createProvinceHandler = useCallback(async (data: CreateProvinceData) => {
    try {
      const result = await createProvince(data).unwrap();
      return result.province;
    } catch (error) {
      console.error('Error creating province:', error);
      throw error;
    }
  }, [createProvince]);

  const createCityHandler = useCallback(async (data: CreateCityData) => {
    try {
      const result = await createCity(data).unwrap();
      return result.city;
    } catch (error) {
      console.error('Error creating city:', error);
      throw error;
    }
  }, [createCity]);

  const createTownshipHandler = useCallback(async (data: CreateTownshipData) => {
    try {
      const result = await createTownship(data).unwrap();
      return result.township;
    } catch (error) {
      console.error('Error creating township:', error);
      throw error;
    }
  }, [createTownship]);

  const createLocationHandler = useCallback(async (data: CreateLocationData) => {
    try {
      // Validate data before sending
      if (!data.name || !data.townshipId) {
        throw new Error('Missing required fields: name and townshipId are required');
      }

      if (data.name.length < 2) {
        throw new Error('Location name must be at least 2 characters long');
      }

      const result = await createLocation(data).unwrap();
      return result.location;
    } catch (error) {
      // Re-throw with enhanced error information
      const enhancedError = {
        message: (error as any)?.message || (error as any)?.data?.error || 'Unknown error occurred',
        data: (error as any)?.data,
        status: (error as any)?.status,
        originalError: error
      };

      throw enhancedError;
    }
  }, [createLocation]);

  // Update operations
  const updateProvinceHandler = useCallback(async (data: UpdateProvinceData) => {
    try {
      const result = await updateProvince(data).unwrap();
      return result.province;
    } catch (error) {
      console.error('Error updating province:', error);
      throw error;
    }
  }, [updateProvince]);

  const updateCityHandler = useCallback(async (data: UpdateCityData & { provinceId: string }) => {
    try {
      const result = await updateCity(data).unwrap();
      return result.city;
    } catch (error) {
      console.error('Error updating city:', error);
      throw error;
    }
  }, [updateCity]);

  const updateTownshipHandler = useCallback(async (data: UpdateTownshipData & { cityId: string }) => {
    try {
      const result = await updateTownship(data).unwrap();
      return result.township;
    } catch (error) {
      console.error('Error updating township:', error);
      throw error;
    }
  }, [updateTownship]);

  const updateLocationHandler = useCallback(async (data: UpdateLocationData & { townshipId: string }) => {
    try {
      const result = await updateLocation(data).unwrap();
      return result.location;
    } catch (error) {
      console.error('Error updating location:', error);
      throw error;
    }
  }, [updateLocation]);

  // Delete operations
  const deleteProvinceHandler = useCallback(async (id: string) => {
    try {
      const result = await deleteProvince(id).unwrap();
      return result.province;
    } catch (error) {
      console.error('Error deleting province:', error);
      throw error;
    }
  }, [deleteProvince]);

  const deleteCityHandler = useCallback(async (provinceId: string, cityId: string) => {
    try {
      const result = await deleteCity({ provinceId, cityId }).unwrap();
      return result.city;
    } catch (error) {
      console.error('Error deleting city:', error);
      throw error;
    }
  }, [deleteCity]);

  const deleteTownshipHandler = useCallback(async (cityId: string, townshipId: string) => {
    try {
      const result = await deleteTownship({ cityId, townshipId }).unwrap();
      return result.township;
    } catch (error) {
      console.error('Error deleting township:', error);
      throw error;
    }
  }, [deleteTownship]);

  const deleteLocationHandler = useCallback(async (townshipId: string, locationId: string) => {
    try {
      const result = await deleteLocation({ townshipId, locationId }).unwrap();
      return result.location;
    } catch (error) {
      console.error('Error deleting location:', error);
      throw error;
    }
  }, [deleteLocation]);

  // Selection data object
  const selectionData: LocationSelectionData = useMemo(() => ({
    selectedProvinceId,
    selectedCityId,
    selectedTownshipId,
    selectedLocationId,
    availableProvinces: provinces,
    availableCities,
    availableTownships,
    availableLocations,
    isLoading: {
      provinces: provincesLoading,
      cities: citiesLoading,
      townships: townshipsLoading,
      locations: locationsLoading,
    },
  }), [
    selectedProvinceId,
    selectedCityId,
    selectedTownshipId,
    selectedLocationId,
    provinces,
    availableCities,
    availableTownships,
    availableLocations,
    provincesLoading,
    citiesLoading,
    townshipsLoading,
    locationsLoading,
  ]);

  // Validation
  const isSelectionComplete = useMemo(() => {
    return !!(selectedProvinceId && selectedCityId && selectedTownshipId && selectedLocationId);
  }, [selectedProvinceId, selectedCityId, selectedTownshipId, selectedLocationId]);

  const isSelectionValid = useMemo(() => {
    return isSelectionComplete && !provincesLoading && !citiesLoading && !townshipsLoading && !locationsLoading;
  }, [isSelectionComplete, provincesLoading, citiesLoading, townshipsLoading, locationsLoading]);

  return {
    // Selection state
    selectionData,
    selectedProvinceId,
    selectedCityId,
    selectedTownshipId,
    selectedLocationId,
    
    // Available options
    provinces,
    availableCities,
    availableTownships,
    availableLocations,
    
    // Loading states
    isLoading: {
      provinces: provincesLoading,
      cities: citiesLoading,
      townships: townshipsLoading,
      locations: locationsLoading,
    },
    
    // Error states
    error: provincesError,
    
    // Selection handlers
    handleProvinceChange,
    handleCityChange,
    handleTownshipChange,
    handleLocationChange,
    resetSelection,
    setSelection,
    
    // Validation
    isSelectionComplete,
    isSelectionValid,
    
    // CRUD operations
    createProvince: createProvinceHandler,
    createCity: createCityHandler,
    createTownship: createTownshipHandler,
    createLocation: createLocationHandler,
    updateProvince: updateProvinceHandler,
    updateCity: updateCityHandler,
    updateTownship: updateTownshipHandler,
    updateLocation: updateLocationHandler,
    deleteProvince: deleteProvinceHandler,
    deleteCity: deleteCityHandler,
    deleteTownship: deleteTownshipHandler,
    deleteLocation: deleteLocationHandler,
  };
}

/**
 * Hook for location search functionality
 */
export function useLocationSearch(query: string, limit: number = 10) {
  const { data, isLoading, error } = useSearchLocationsQuery(
    { query, limit },
    { skip: !query || query.length < 2 }
  );

  return {
    searchResults: data,
    isSearching: isLoading,
    searchError: error,
  };
}

/**
 * Hook for location statistics
 */
export function useLocationStats() {
  const { data, isLoading, error } = useGetLocationStatsQuery();

  return {
    stats: data?.stats,
    isLoading,
    error,
    lastUpdated: data?.timestamp,
  };
}
