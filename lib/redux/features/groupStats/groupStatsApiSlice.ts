// lib/redux/features/groupStats/groupStatsApiSlice.ts
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

// Types for group statistics
export interface GroupActivity {
  _id: string;
  type: string;
  title: string;
  description: string;
  userId: string;
  userName: string;
  metadata: Record<string, any>;
  createdAt: string;
  isPublic: boolean;
}

export interface GroupMember {
  userId: string;
  name: string;
  email: string;
  phone: string;
  totalOrders: string;
  totalSpent: string;
  joinedAt: string;
  isOnline?: boolean;
  lastSeen?: string;
}

export interface GroupStats {
  totalMembers: number;
  onlineMembers: number;
  totalOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
  activeOrders: number;
  completedOrders: number;
  pendingOrders: number;
  totalSavings: number;
  savingsTarget: number;
  savingsProgress: number;
  memberParticipationRate: number;
  orderCompletionRate: number;
  recentActivityCount: number;
  topSpenders: Array<{
    userId: string;
    name: string;
    totalSpent: number;
  }>;
  monthlyTrends: Array<{
    month: string;
    orders: number;
    revenue: number;
    members: number;
  }>;
}

export interface GroupAnalytics {
  totalOrders: number;
  averageOrderValue: number;
  totalRevenue: number;
  popularProducts: Array<{
    productId: string;
    name: string;
    totalQuantity: number;
    totalRevenue: number;
  }>;
  ordersByStatus: Record<string, number>;
  memberParticipation: Array<{
    userId: string;
    name: string;
    orderCount: number;
    totalSpent: number;
  }>;
}

export interface OnlineMembersResponse {
  success: boolean;
  data: GroupMember[];
  metadata: {
    groupId: string;
    totalMembers: number;
    onlineCount: number;
    timestamp: string;
  };
}

export interface GroupActivitiesResponse {
  success: boolean;
  data: GroupActivity[];
  summary: {
    totalActivities: number;
    filteredCount: number;
    byType: Record<string, number>;
    recentActivity: string | null;
  };
  metadata: {
    groupId: string;
    filters: Record<string, any>;
    limit: number;
    offset: number;
    timestamp: string;
  };
}

// Base query with authentication using cookies
const baseQuery = fetchBaseQuery({
  baseUrl: '/api',
  credentials: 'include', // This ensures cookies are sent with requests
  prepareHeaders: (headers) => {
    headers.set('Content-Type', 'application/json');
    // No need to manually set Authorization header since we're using HttpOnly cookies
    // The browser will automatically include the accessToken cookie
    return headers;
  },
});

export const groupStatsApiSlice = createApi({
  reducerPath: 'groupStatsApi',
  baseQuery,
  tagTypes: ['GroupStats', 'GroupActivities', 'GroupMembers', 'GroupAnalytics'],
  endpoints: (builder) => ({
    // Get group statistics
    getGroupStats: builder.query<GroupStats, string>({
      query: (groupId) => `/groups/${groupId}/stats`,
      providesTags: (result, error, groupId) => [
        { type: 'GroupStats', id: groupId },
      ],
      keepUnusedDataFor: 300, // 5 minutes
    }),

    // Get group activities
    getGroupActivities: builder.query<GroupActivitiesResponse, { 
      groupId: string; 
      limit?: number; 
      offset?: number; 
      type?: string; 
      timeRange?: string; 
    }>({
      query: ({ groupId, limit = 10, offset = 0, type, timeRange }) => {
        const params = new URLSearchParams({
          limit: limit.toString(),
          offset: offset.toString(),
        });
        if (type) params.append('type', type);
        if (timeRange) params.append('timeRange', timeRange);
        
        return `/groups/${groupId}/activities?${params.toString()}`;
      },
      providesTags: (result, error, { groupId }) => [
        { type: 'GroupActivities', id: groupId },
      ],
      keepUnusedDataFor: 60, // 1 minute for activities
    }),

    // Get group members with online status
    getGroupMembers: builder.query<GroupMember[], string>({
      query: (groupId) => `/groups/${groupId}/all-members`,
      providesTags: (result, error, groupId) => [
        { type: 'GroupMembers', id: groupId },
      ],
      keepUnusedDataFor: 300, // 5 minutes
    }),

    // Get online members
    getOnlineMembers: builder.query<OnlineMembersResponse, string>({
      query: (groupId) => `/groups/${groupId}/members/online`,
      providesTags: (result, error, groupId) => [
        { type: 'GroupMembers', id: `${groupId}_online` },
      ],
      keepUnusedDataFor: 30, // 30 seconds for online status
    }),

    // Get group analytics
    getGroupAnalytics: builder.query<GroupAnalytics, { 
      groupId: string; 
      startDate?: string; 
      endDate?: string; 
    }>({
      query: ({ groupId, startDate, endDate }) => {
        const params = new URLSearchParams({ groupId });
        if (startDate) params.append('startDate', startDate);
        if (endDate) params.append('endDate', endDate);
        
        return `/group-order/analytics?${params.toString()}`;
      },
      providesTags: (result, error, { groupId }) => [
        { type: 'GroupAnalytics', id: groupId },
      ],
      keepUnusedDataFor: 600, // 10 minutes
    }),
  }),
});

export const {
  useGetGroupStatsQuery,
  useGetGroupActivitiesQuery,
  useGetGroupMembersQuery,
  useGetOnlineMembersQuery,
  useGetGroupAnalyticsQuery,
  
  // Lazy queries
  useLazyGetGroupStatsQuery,
  useLazyGetGroupActivitiesQuery,
  useLazyGetGroupMembersQuery,
  useLazyGetOnlineMembersQuery,
  useLazyGetGroupAnalyticsQuery,
} = groupStatsApiSlice;

// Selectors
export const selectGroupStats = (groupId: string) => (state: any) =>
  groupStatsApiSlice.endpoints.getGroupStats.select(groupId)(state);

export const selectGroupActivities = (params: { groupId: string; limit?: number; offset?: number; type?: string; timeRange?: string }) => (state: any) =>
  groupStatsApiSlice.endpoints.getGroupActivities.select(params)(state);

export const selectGroupMembers = (groupId: string) => (state: any) =>
  groupStatsApiSlice.endpoints.getGroupMembers.select(groupId)(state);
