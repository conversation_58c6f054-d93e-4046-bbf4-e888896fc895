// lib/redux/features/cart/cartApiSlice.ts
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import {
  DisplayShoppingCart,
  DisplayCartItem,
  GroupOrder,
  AddToCartInput,
  UpdateCartItemInput,
  RemoveFromCartInput,
  ClearCartInput,
  CreateGroupOrderInput,
  UpdateGroupOrderStatusInput,
  DiscountCalculationResult
} from '@/types/unifiedCart';
import { transformCartResponse } from './cartTransformers';
import { setError, showNotification, setCart } from './cartSlice';

export const cartApiSlice = createApi({
  reducerPath: 'cartApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/api' }),
  tagTypes: ['Cart', 'GroupOrder'],
  endpoints: (builder) => ({
    getShoppingCart: builder.query<DisplayShoppingCart, { userId: string; groupId?: string }>({
      query: ({ userId, groupId }) => {
        console.log('getShoppingCart query for userId:', userId, 'groupId:', groupId);
        const url = `/shopping-cart/get?userId=${userId}${groupId ? `&groupId=${groupId}` : ''}`;
        console.log('getShoppingCart query URL:', url);
        return url;
      },
      providesTags: ['Cart'],
      transformResponse: transformCartResponse,
      // Add retry logic for network failures
      extraOptions: {
        maxRetries: 3,
        backoff: (attempt: number) => Math.min(1000 * Math.pow(2, attempt), 15000),
      },
    }),

    addToCart: builder.mutation<DisplayShoppingCart, AddToCartInput>({
      query: (cartItem) => ({
        url: '/shopping-cart/add-item',
        method: 'POST',
        body: cartItem,
      }),
      invalidatesTags: ['Cart'],
      transformResponse: transformCartResponse,
      // Add retry logic for network failures
      extraOptions: {
        maxRetries: 3,
        backoff: (attempt: number) => Math.min(1000 * Math.pow(2, attempt), 15000),
      },
      // Optimistic update for better UX
      async onQueryStarted(arg, { dispatch, queryFulfilled }) {
        try {
          // Log the arguments for debugging
          console.log('Adding item to cart:', arg);

          // Wait for the query to complete
          const result = await queryFulfilled;

          // Log the result for debugging
          console.log('Add to cart successful:', result);

          // Show notification on success
          dispatch(showNotification());
        } catch (error: unknown) {
          // Log the detailed error for debugging
          console.error('Failed to add item to cart:', error);

          // Extract error message if available
          // Type guard to safely access error properties
          const errorMessage =
            typeof error === 'object' && error !== null
              ? (
                  // Check if error has data.error property
                  'data' in error &&
                  typeof error.data === 'object' &&
                  error.data !== null &&
                  'error' in error.data &&
                  typeof error.data.error === 'string'
                    ? error.data.error
                    // Check if error has error.data.error property
                    : 'error' in error &&
                      typeof error.error === 'object' &&
                      error.error !== null &&
                      'data' in error.error &&
                      typeof error.error.data === 'object' &&
                      error.error.data !== null &&
                      'error' in error.error.data &&
                      typeof error.error.data.error === 'string'
                      ? error.error.data.error
                      : 'Failed to add item to cart. Please try again.'
                )
              : 'Failed to add item to cart. Please try again.';

          // Set error message in the Redux store
          dispatch(setError(errorMessage));
        }
      },
    }),

    updateCartItem: builder.mutation<DisplayShoppingCart, UpdateCartItemInput>({
      query: (input) => ({
        url: '/shopping-cart/update-quantity',
        method: 'PATCH',
        body: input,
      }),
      invalidatesTags: ['Cart'],
      transformResponse: transformCartResponse,
      // Add retry logic for network failures
      extraOptions: {
        maxRetries: 3,
        backoff: (attempt: number) => Math.min(1000 * Math.pow(2, attempt), 15000),
      },
      // Disabled optimistic updates for stability - let server be source of truth
      async onQueryStarted(arg, { dispatch, queryFulfilled }) {
        try {
          await queryFulfilled;
          dispatch(showNotification());
        } catch (error) {
          console.error('Failed to update cart item:', error);
          dispatch(setError('Failed to update quantity. Please try again.'));
        }
      },
    }),

    removeFromCart: builder.mutation<DisplayShoppingCart, RemoveFromCartInput>({
      query: (data) => ({
        url: '/shopping-cart/remove-item',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Cart'],
      transformResponse: transformCartResponse,
      // Add retry logic for network failures
      extraOptions: {
        maxRetries: 3,
        backoff: (attempt: number) => Math.min(1000 * Math.pow(2, attempt), 15000),
      },
      // Disabled optimistic updates for stability - let server be source of truth
      async onQueryStarted(arg, { dispatch, queryFulfilled }) {
        try {
          await queryFulfilled;
          dispatch(showNotification());
        } catch (error) {
          console.error('Failed to remove item from cart:', error);
          dispatch(setError('Failed to remove item. Please try again.'));
        }
      },
    }),

    clearCart: builder.mutation<void, ClearCartInput>({
      query: (data) => ({
        url: '/shopping-cart/clear',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Cart'],
      // Add retry logic for network failures
      extraOptions: {
        maxRetries: 2,
        backoff: (attempt: number) => Math.min(1000 * Math.pow(2, attempt), 10000),
      },
      // Error handling
      async onQueryStarted(_arg, { dispatch, queryFulfilled }) {
        try {
          await queryFulfilled;
        } catch (error) {
          console.error('Failed to clear cart:', error);
          dispatch(setError('Failed to clear cart. Please try again.'));
        }
      },
    }),

    createOrUpdateGroupOrder: builder.mutation<any, CreateGroupOrderInput>({
      query: (data) => ({
        url: `/groups/${data.groupId}/orders`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Cart', 'GroupOrder'],
      // After creating an order, automatically clear the cart
      async onQueryStarted(arg, { dispatch, queryFulfilled }) {
        try {
          await queryFulfilled;
          // Clear the cart after successful order creation
          if (arg.userId && arg.groupId) {
            dispatch(
              cartApiSlice.endpoints.clearCart.initiate({
                userId: arg.userId,
                groupId: arg.groupId
              })
            );
          }
        } catch (error) {
          console.error('Error creating group order:', error);
        }
      },
    }),

    getGroupOrders: builder.query<GroupOrder[], { groupId: string; limit?: number; skip?: number; populate?: boolean }>({
      query: ({ groupId, limit = 20, skip = 0, populate = false }) =>
        `/groups/${groupId}/orders?limit=${limit}&skip=${skip}&populate=${populate}`,
      providesTags: (result, error, { groupId }) => [
        { type: 'GroupOrder', id: groupId },
        { type: 'GroupOrder', id: 'LIST' }
      ],
      // Keep data fresh for 5 minutes
      keepUnusedDataFor: 300,
    }),

    getAllGroupOrders: builder.query<GroupOrder[], void>({
      query: () => `/orders/group-orders`,
      providesTags: ['GroupOrder'],
    }),

    updateGroupOrderStatus: builder.mutation<void, UpdateGroupOrderStatusInput>({
      query: (data) => ({
        url: `/group-order/update-status`,
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: ['GroupOrder'],
    }),

    calculateGroupDiscount: builder.query<DiscountCalculationResult, string>({
      query: (groupId) => `/groups/${groupId}/calculate-discount`,
    }),
  }),
});

export const {
  useGetShoppingCartQuery,
  useAddToCartMutation,
  useUpdateCartItemMutation,
  useRemoveFromCartMutation,
  useClearCartMutation,
  useCreateOrUpdateGroupOrderMutation,
  useGetGroupOrdersQuery,
  useGetAllGroupOrdersQuery,
  useUpdateGroupOrderStatusMutation,
  useCalculateGroupDiscountQuery,
} = cartApiSlice;
