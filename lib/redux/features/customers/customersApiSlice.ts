// lib/redux/features/customers/customersApiSlice.ts

import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { IUser } from '@/models/User';

// Customer Analytics Interfaces
export interface CustomerOverview {
  totalCustomers: number;
  newCustomers: number;
  activeCustomers: number;
  averageOrderValue: number;
  customersByStatus: Record<string, number>;
  totalCustomersGrowth: number;
  newCustomersGrowth: number;
  activeCustomersGrowth: number;
  avgOrderValueGrowth: number;
}

export interface CustomerTrendData {
  date: string;
  newCustomers: number;
  activeCustomers: number;
  totalRevenue: number;
}

export interface TopCustomer {
  _id: string;
  name: string;
  email: string;
  totalOrders: number;
  totalSpent: number;
  lastOrderDate?: string;
  avatar?: string;
  status: string;
}

export interface CustomerAnalytics {
  overview: CustomerOverview;
  trends: CustomerTrendData[];
  topCustomers: TopCustomer[];
  recentCustomers: IUser[];
}

export interface CustomerFilters {
  status?: string;
  dateFrom?: string;
  dateTo?: string;
  groupId?: string;
  minOrders?: number;
  minSpent?: number;
  search?: string;
}

export interface PaginatedCustomersResponse {
  customers: IUser[];
  totalCount: number;
  totalPages: number;
  currentPage: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface UpdateCustomerInput {
  customerId: string;
  name?: string;
  email?: string;
  status?: string;
  notes?: string;
}

export interface CustomerStats {
  _id: string;
  name: string;
  email: string;
  totalOrders: number;
  totalSpent: number;
  lastOrderDate?: string;
  firstOrderDate?: string;
  averageOrderValue: number;
  status: string;
  groupId?: string;
  groupName?: string;
  createdAt: string;
}

export const customersApiSlice = createApi({
  reducerPath: 'customersApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/api' }),
  tagTypes: ['Customer', 'CustomerAnalytics', 'CustomerOverview'],
  endpoints: (builder) => ({
    // Get customer analytics and overview
    getCustomerAnalytics: builder.query<CustomerAnalytics, { period?: string }>({
      query: ({ period = '30d' } = {}) => `/customers/analytics?period=${period}`,
      providesTags: ['CustomerAnalytics'],
    }),

    // Get customer overview stats
    getCustomerOverview: builder.query<CustomerOverview, void>({
      query: () => '/customers/overview',
      providesTags: ['CustomerOverview'],
    }),

    // Get all customers with pagination and filters
    getAllCustomers: builder.query<PaginatedCustomersResponse, {
      page?: number;
      limit?: number;
      filters?: CustomerFilters;
    }>({
      query: ({ page = 1, limit = 20, filters = {} }) => {
        const params = new URLSearchParams({
          page: page.toString(),
          limit: limit.toString(),
          ...Object.fromEntries(
            Object.entries(filters).filter(([_, value]) => value !== undefined && value !== '')
          ),
        });
        return `/customers/admin?${params.toString()}`;
      },
      providesTags: (result) =>
        result
          ? [
              ...result.customers.map(({ _id }) => ({ type: 'Customer' as const, id: _id })),
              { type: 'Customer', id: 'LIST' },
            ]
          : [{ type: 'Customer', id: 'LIST' }],
    }),

    // Get customer stats with order information
    getCustomersWithStats: builder.query<CustomerStats[], {
      page?: number;
      limit?: number;
      filters?: CustomerFilters;
    }>({
      query: ({ page = 1, limit = 20, filters = {} }) => {
        const params = new URLSearchParams({
          page: page.toString(),
          limit: limit.toString(),
          ...Object.fromEntries(
            Object.entries(filters).filter(([_, value]) => value !== undefined && value !== '')
          ),
        });
        return `/customers/stats?${params.toString()}`;
      },
      providesTags: ['Customer'],
    }),

    // Get single customer by ID
    getCustomerById: builder.query<IUser, string>({
      query: (customerId) => `/customers/${customerId}`,
      providesTags: (result, error, customerId) => [{ type: 'Customer', id: customerId }],
    }),

    // Get customer order history
    getCustomerOrders: builder.query<any[], { customerId: string; page?: number; limit?: number }>({
      query: ({ customerId, page = 1, limit = 10 }) => 
        `/customers/${customerId}/orders?page=${page}&limit=${limit}`,
      providesTags: (result, error, { customerId }) => [{ type: 'Customer', id: customerId }],
    }),

    // Update customer information
    updateCustomer: builder.mutation<IUser, UpdateCustomerInput>({
      query: ({ customerId, ...updateData }) => ({
        url: `/customers/${customerId}`,
        method: 'PATCH',
        body: updateData,
      }),
      invalidatesTags: (result, error, { customerId }) => [
        { type: 'Customer', id: customerId },
        { type: 'Customer', id: 'LIST' },
        'CustomerAnalytics',
        'CustomerOverview',
      ],
    }),

    // Deactivate customer
    deactivateCustomer: builder.mutation<IUser, { customerId: string; reason?: string }>({
      query: ({ customerId, reason }) => ({
        url: `/customers/${customerId}/deactivate`,
        method: 'PATCH',
        body: { reason },
      }),
      invalidatesTags: (result, error, { customerId }) => [
        { type: 'Customer', id: customerId },
        { type: 'Customer', id: 'LIST' },
        'CustomerAnalytics',
        'CustomerOverview',
      ],
    }),

    // Reactivate customer
    reactivateCustomer: builder.mutation<IUser, string>({
      query: (customerId) => ({
        url: `/customers/${customerId}/reactivate`,
        method: 'PATCH',
      }),
      invalidatesTags: (result, error, customerId) => [
        { type: 'Customer', id: customerId },
        { type: 'Customer', id: 'LIST' },
        'CustomerAnalytics',
        'CustomerOverview',
      ],
    }),

    // Get customer trends
    getCustomerTrends: builder.query<CustomerTrendData[], { period?: string }>({
      query: ({ period = '30d' } = {}) => `/customers/trends?period=${period}`,
      providesTags: ['CustomerAnalytics'],
    }),

    // Get top customers
    getTopCustomers: builder.query<TopCustomer[], { limit?: number; period?: string }>({
      query: ({ limit = 10, period = '30d' } = {}) => 
        `/customers/top?limit=${limit}&period=${period}`,
      providesTags: ['CustomerAnalytics'],
    }),

    // Search customers
    searchCustomers: builder.query<IUser[], { query: string; limit?: number }>({
      query: ({ query, limit = 10 }) => 
        `/customers/search?q=${encodeURIComponent(query)}&limit=${limit}`,
      providesTags: ['Customer'],
    }),

    // Refresh analytics
    refreshCustomerAnalytics: builder.mutation<CustomerAnalytics, void>({
      query: () => ({
        url: '/customers/analytics/refresh',
        method: 'POST',
      }),
      invalidatesTags: ['CustomerAnalytics', 'CustomerOverview'],
    }),
  }),
});

export const {
  useGetCustomerAnalyticsQuery,
  useGetCustomerOverviewQuery,
  useGetAllCustomersQuery,
  useGetCustomersWithStatsQuery,
  useGetCustomerByIdQuery,
  useGetCustomerOrdersQuery,
  useUpdateCustomerMutation,
  useDeactivateCustomerMutation,
  useReactivateCustomerMutation,
  useGetCustomerTrendsQuery,
  useGetTopCustomersQuery,
  useSearchCustomersQuery,
  useRefreshCustomerAnalyticsMutation,
} = customersApiSlice;
