// lib/redux/features/users/usersApiSlice.ts

import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

// User Analytics Interfaces
export interface UserAnalytics {
  userId: string;
  email: string;
  name: string;
  role: string;
  registrationDate: string;
  lastLoginDate?: string;
  totalOrders: number;
  totalSpent: number;
  averageOrderValue: number;
  lifetimeValue: number;
  groupsJoined: number;
  activityScore: number;
  engagementLevel: 'high' | 'medium' | 'low' | 'inactive';
  riskScore: number;
  churnProbability: number;
  behaviorSegment: string;
  acquisitionChannel: string;
  status: string;
  groupId?: string;
  groupName?: string;
  lastOrderDate?: string;
  firstOrderDate?: string;
}

export interface UserSegment {
  id: string;
  name: string;
  description: string;
  userCount: number;
  averageValue: number;
  growthRate: number;
  characteristics: {
    averageAge: number;
    averageLifetimeValue: number;
    averageOrderValue: number;
    mostPopularCategories: string[];
    retentionRate: number;
    conversionRate: number;
  };
}

export interface UserAnalyticsResponse {
  success: boolean;
  data: UserAnalytics[];
  summary: {
    totalUsers: number;
    activeUsers: number;
    highValueUsers: number;
    atRiskUsers: number;
    averageLifetimeValue: number;
    averageActivityScore: number;
    engagementDistribution: Record<string, number>;
    totalRevenue: number;
  };
  metadata: {
    totalUsers: number;
    timestamp: string;
    includeChurnPrediction: boolean;
    includeCLV: boolean;
  };
}

export interface UserSegmentsResponse {
  success: boolean;
  data: UserSegment[];
  metadata: {
    totalSegments: number;
    timestamp: string;
  };
}

export interface UserFilters {
  segment?: string;
  engagementLevel?: string;
  riskLevel?: string;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
}

export interface PaginatedUsersResponse {
  users: UserAnalytics[];
  totalCount: number;
  totalPages: number;
  currentPage: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface UpdateUserInput {
  userId: string;
  name?: string;
  email?: string;
  role?: string;
  status?: string;
  notes?: string;
  suspensionReason?: string;
}

export interface ChurnPrediction {
  userId: string;
  churnProbability: number;
  riskFactors: string[];
  recommendations: string[];
  confidence: number;
}

export interface LifetimeValueAnalysis {
  userId: string;
  currentLTV: number;
  predictedLTV: number;
  ltv12Months: number;
  ltv24Months: number;
  factors: {
    orderFrequency: number;
    averageOrderValue: number;
    retentionRate: number;
    engagementScore: number;
  };
}

export const usersApiSlice = createApi({
  reducerPath: 'usersApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api',
    prepareHeaders: (headers) => {
      // For development, we'll skip authentication
      // In production, you would get the token from your auth system
      const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['UserAnalytics', 'UserSegments', 'User'],
  endpoints: (builder) => ({
    // Get user analytics with enhanced data
    getUserAnalytics: builder.query<UserAnalyticsResponse, {
      userId?: string;
      includeChurnPrediction?: boolean;
      includeCLV?: boolean;
    }>({
      query: ({ userId, includeChurnPrediction = true, includeCLV = true } = {}) => {
        const params = new URLSearchParams();
        if (userId) params.append('userId', userId);
        if (includeChurnPrediction) params.append('includeChurnPrediction', 'true');
        if (includeCLV) params.append('includeCLV', 'true');
        return `/admin/users/analytics?${params.toString()}`;
      },
      providesTags: ['UserAnalytics'],
    }),

    // Get user segments
    getUserSegments: builder.query<UserSegmentsResponse, void>({
      query: () => '/admin/users/segments',
      providesTags: ['UserSegments'],
    }),

    // Get paginated users with filters
    getPaginatedUsers: builder.query<PaginatedUsersResponse, {
      page?: number;
      limit?: number;
      filters?: UserFilters;
    }>({
      query: ({ page = 1, limit = 20, filters = {} }) => {
        const params = new URLSearchParams({
          page: page.toString(),
          limit: limit.toString(),
          ...Object.fromEntries(
            Object.entries(filters).filter(([_, value]) => value !== undefined && value !== '')
          ),
        });
        return `/admin/users/paginated?${params.toString()}`;
      },
      providesTags: (result) =>
        result
          ? [
              ...result.users.map(({ userId }) => ({ type: 'User' as const, id: userId })),
              { type: 'User', id: 'LIST' },
            ]
          : [{ type: 'User', id: 'LIST' }],
    }),

    // Get single user analytics
    getSingleUserAnalytics: builder.query<UserAnalytics, string>({
      query: (userId) => `/admin/users/analytics?userId=${userId}`,
      providesTags: (result, error, userId) => [{ type: 'User', id: userId }],
    }),

    // Bulk churn prediction
    bulkChurnPrediction: builder.mutation<ChurnPrediction[], { userIds: string[] }>({
      query: ({ userIds }) => ({
        url: '/admin/users/analytics',
        method: 'POST',
        body: {
          action: 'bulk_churn_prediction',
          data: { userIds },
        },
      }),
      invalidatesTags: ['UserAnalytics'],
    }),

    // Bulk CLV calculation
    bulkCLVCalculation: builder.mutation<LifetimeValueAnalysis[], { userIds: string[] }>({
      query: ({ userIds }) => ({
        url: '/admin/users/analytics',
        method: 'POST',
        body: {
          action: 'bulk_clv_calculation',
          data: { userIds },
        },
      }),
      invalidatesTags: ['UserAnalytics'],
    }),

    // Export user analytics
    exportUserAnalytics: builder.mutation<any, { format: string; filters?: UserFilters }>({
      query: ({ format, filters }) => ({
        url: '/admin/users/analytics',
        method: 'POST',
        body: {
          action: 'export_analytics',
          data: { format, filters },
        },
      }),
    }),

    // Create user segment
    createUserSegment: builder.mutation<UserSegment, {
      name: string;
      description: string;
      criteria: any;
    }>({
      query: ({ name, description, criteria }) => ({
        url: '/admin/users/segments',
        method: 'POST',
        body: {
          action: 'create_segment',
          data: { name, description, criteria },
        },
      }),
      invalidatesTags: ['UserSegments'],
    }),

    // Analyze segment criteria
    analyzeSegmentCriteria: builder.mutation<any, { criteria: any }>({
      query: ({ criteria }) => ({
        url: '/admin/users/segments',
        method: 'POST',
        body: {
          action: 'analyze_segment',
          data: { criteria },
        },
      }),
    }),

    // Export segment data
    exportSegmentData: builder.mutation<any, { segmentId: string; format: string }>({
      query: ({ segmentId, format }) => ({
        url: '/admin/users/segments',
        method: 'POST',
        body: {
          action: 'export_segment',
          data: { segmentId, format },
        },
      }),
    }),

    // Refresh analytics cache
    refreshAnalytics: builder.mutation<any, void>({
      query: () => ({
        url: '/admin/users/analytics',
        method: 'POST',
        body: {
          action: 'refresh_analytics',
          data: {},
        },
      }),
      invalidatesTags: ['UserAnalytics', 'UserSegments'],
    }),

    // Refresh segments
    refreshSegments: builder.mutation<any, void>({
      query: () => ({
        url: '/admin/users/segments',
        method: 'POST',
        body: {
          action: 'refresh_segments',
          data: {},
        },
      }),
      invalidatesTags: ['UserSegments'],
    }),

    // Update user
    updateUser: builder.mutation<UserAnalytics, UpdateUserInput>({
      query: ({ userId, ...updateData }) => ({
        url: `/admin/users/${userId}`,
        method: 'PATCH',
        body: updateData,
      }),
      invalidatesTags: (result, error, { userId }) => [
        { type: 'User', id: userId },
        { type: 'User', id: 'LIST' },
        'UserAnalytics',
      ],
    }),

    // Reset user password
    resetUserPassword: builder.mutation<{ success: boolean; message: string }, string>({
      query: (userId) => ({
        url: `/admin/users/${userId}/reset-password`,
        method: 'POST',
      }),
    }),

    // Suspend user account
    suspendUserAccount: builder.mutation<UserAnalytics, { userId: string; reason: string }>({
      query: ({ userId, reason }) => ({
        url: `/admin/users/${userId}/suspend`,
        method: 'POST',
        body: { reason },
      }),
      invalidatesTags: (result, error, { userId }) => [
        { type: 'User', id: userId },
        { type: 'User', id: 'LIST' },
        'UserAnalytics',
      ],
    }),

    // Reactivate user account
    reactivateUserAccount: builder.mutation<UserAnalytics, string>({
      query: (userId) => ({
        url: `/admin/users/${userId}/reactivate`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, userId) => [
        { type: 'User', id: userId },
        { type: 'User', id: 'LIST' },
        'UserAnalytics',
      ],
    }),
  }),
});

export const {
  useGetUserAnalyticsQuery,
  useGetUserSegmentsQuery,
  useGetPaginatedUsersQuery,
  useGetSingleUserAnalyticsQuery,
  useBulkChurnPredictionMutation,
  useBulkCLVCalculationMutation,
  useExportUserAnalyticsMutation,
  useCreateUserSegmentMutation,
  useAnalyzeSegmentCriteriaMutation,
  useExportSegmentDataMutation,
  useRefreshAnalyticsMutation,
  useRefreshSegmentsMutation,
  useUpdateUserMutation,
  useResetUserPasswordMutation,
  useSuspendUserAccountMutation,
  useReactivateUserAccountMutation,
} = usersApiSlice;
