// lib/redux/features/products/productAnalyticsApiSlice.ts

import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

// Product Analytics Interfaces
export interface ProductOverview {
  totalProducts: number;
  activeProducts: number;
  outOfStockProducts: number;
  lowStockProducts: number;
  totalCategories: number;
  activeCategories: number;
}

export interface RevenueData {
  total: number;
  trend: number;
  formatted: string;
}

export interface OrdersData {
  total: number;
  trend: number;
  averageValue: number;
  formatted: string;
}

export interface TopSellingProduct {
  _id: string;
  name: string;
  totalSold: number;
  totalRevenue: number;
}

export interface SalesTrendData {
  date: string;
  sales: number;
  orders: number;
}

export interface CategoryPerformance {
  _id: string;
  name: string;
  productCount: number;
  activeProducts: number;
}

export interface ProductAnalytics {
  overview: ProductOverview;
  revenue: RevenueData;
  orders: OrdersData;
  topSellingProducts: TopSellingProduct[];
  salesTrend: SalesTrendData[];
  categoryPerformance: CategoryPerformance[];
  period: number;
  lastUpdated: string;
}

export interface ProductAnalyticsResponse {
  success: boolean;
  analytics: ProductAnalytics;
  error?: string;
}

export interface ProductAnalyticsParams {
  period?: number; // Days to look back (default: 30)
}

// Base query configuration
const baseQuery = fetchBaseQuery({
  baseUrl: '/api/admin/products/',
  prepareHeaders: (headers) => {
    headers.set('Content-Type', 'application/json');
    return headers;
  },
});

export const productAnalyticsApi = createApi({
  reducerPath: 'productAnalyticsApi',
  baseQuery,
  tagTypes: ['ProductAnalytics', 'ProductStats', 'CategoryStats'],
  endpoints: (builder) => ({
    // Get product analytics
    getProductAnalytics: builder.query<ProductAnalyticsResponse, ProductAnalyticsParams>({
      query: (params = {}) => {
        const searchParams = new URLSearchParams();
        if (params.period) searchParams.append('period', params.period.toString());
        return `analytics?${searchParams.toString()}`;
      },
      providesTags: ['ProductAnalytics'],
      // Refetch every 5 minutes
      keepUnusedDataFor: 300,
    }),

    // Refresh product analytics
    refreshProductAnalytics: builder.mutation<ProductAnalyticsResponse, ProductAnalyticsParams>({
      query: (params = {}) => {
        const searchParams = new URLSearchParams();
        if (params.period) searchParams.append('period', params.period.toString());
        return {
          url: `analytics?${searchParams.toString()}`,
          method: 'GET',
        };
      },
      invalidatesTags: ['ProductAnalytics', 'ProductStats', 'CategoryStats'],
    }),
  }),
});

export const {
  useGetProductAnalyticsQuery,
  useRefreshProductAnalyticsMutation,
  
  // Lazy queries for manual triggering
  useLazyGetProductAnalyticsQuery,
} = productAnalyticsApi;

// Selectors for accessing cached data
export const selectProductAnalytics = (state: any) => 
  productAnalyticsApi.endpoints.getProductAnalytics.select({})(state);

// Custom hooks for real-time updates
export const useRealTimeProductAnalytics = (
  params: ProductAnalyticsParams = {},
  refreshInterval = 300000 // 5 minutes default
) => {
  return useGetProductAnalyticsQuery(params, {
    pollingInterval: refreshInterval,
    refetchOnMountOrArgChange: true,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });
};

// Helper functions for data transformation
export const formatProductStats = (analytics: ProductAnalytics) => {
  return [
    {
      title: "Total Products",
      value: analytics.overview.totalProducts.toLocaleString(),
      icon: "Package",
      description: `${analytics.overview.activeProducts} active`,
      trend: "neutral",
      color: "blue"
    },
    {
      title: "Total Revenue",
      value: analytics.revenue.formatted,
      icon: "DollarSign",
      description: `${analytics.revenue.trend >= 0 ? '+' : ''}${analytics.revenue.trend.toFixed(1)}% from last period`,
      trend: analytics.revenue.trend >= 0 ? "up" : "down",
      color: "green"
    },
    {
      title: "Total Orders",
      value: analytics.orders.formatted,
      icon: "ShoppingCart",
      description: `${analytics.orders.trend >= 0 ? '+' : ''}${analytics.orders.trend.toFixed(1)}% from last period`,
      trend: analytics.orders.trend >= 0 ? "up" : "down",
      color: "purple"
    },
    {
      title: "Avg. Order Value",
      value: `R ${analytics.orders.averageValue.toLocaleString('en-ZA', { minimumFractionDigits: 2 })}`,
      icon: "TrendingUp",
      description: `${analytics.overview.outOfStockProducts} out of stock`,
      trend: "neutral",
      color: "orange"
    },
  ];
};

export const formatCategoryStats = (analytics: ProductAnalytics) => {
  const totalCategories = analytics.overview.totalCategories;
  const activeCategories = analytics.overview.activeCategories;
  const inactiveCategories = totalCategories - activeCategories;
  
  return [
    {
      title: "Total Categories",
      value: totalCategories.toLocaleString(),
      icon: "FolderOpen",
      description: `${activeCategories} active`,
      trend: "neutral",
      color: "blue"
    },
    {
      title: "Active Categories",
      value: activeCategories.toLocaleString(),
      icon: "CheckCircle",
      description: `${inactiveCategories} inactive`,
      trend: "up",
      color: "green"
    },
    {
      title: "Products per Category",
      value: totalCategories > 0 ? Math.round(analytics.overview.totalProducts / totalCategories).toString() : "0",
      icon: "BarChart3",
      description: "Average distribution",
      trend: "neutral",
      color: "purple"
    },
    {
      title: "Category Performance",
      value: `${Math.round((activeCategories / Math.max(totalCategories, 1)) * 100)}%`,
      icon: "TrendingUp",
      description: "Active category ratio",
      trend: activeCategories > inactiveCategories ? "up" : "down",
      color: "orange"
    },
  ];
};

// Error handling helper
export const getAnalyticsError = (error: any) => {
  if (error?.data?.error) {
    return error.data.error;
  }
  if (error?.message) {
    return error.message;
  }
  return "Failed to load analytics data";
};

// Loading state helper
export const isAnalyticsLoading = (query: any) => {
  return query.isLoading || query.isFetching;
};

// Data availability helper
export const hasAnalyticsData = (analytics?: ProductAnalytics) => {
  return analytics && (
    analytics.overview.totalProducts > 0 ||
    analytics.revenue.total > 0 ||
    analytics.orders.total > 0
  );
};
