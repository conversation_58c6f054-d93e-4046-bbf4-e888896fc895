// lib/redux/features/admin/adminDashboardApiSlice.ts

import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

// Dashboard Stats Interfaces
export interface DashboardStats {
  revenue: {
    value: number;
    trend: number;
    description: string;
    formatted: string;
  };
  orders: {
    value: number;
    trend: number;
    description: string;
    formatted: string;
  };
  customers: {
    value: number;
    trend: number;
    description: string;
    formatted: string;
  };
  averageSale: {
    value: number;
    trend: number;
    description: string;
    formatted: string;
  };
  totalGroups: number;
  totalProducts: number;
  period: number;
  lastUpdated: string;
}

export interface DashboardStatsResponse {
  success: boolean;
  stats: DashboardStats;
}

// Sales Chart Interfaces
export interface SalesChartDataPoint {
  day: string;
  date: string;
  sales: number;
  orders: number;
  paidOrders: number;
}

export interface SalesChartSummary {
  totalSales: number;
  totalOrders: number;
  totalPaidOrders: number;
  averageOrderValue: number;
  salesTrend: number;
  period: string;
  dateRange: {
    start: string;
    end: string;
  };
}

export interface SalesChartResponse {
  success: boolean;
  data: SalesChartDataPoint[];
  summary: SalesChartSummary;
  lastUpdated: string;
}

// Top Groups Interfaces
export interface TopGroup {
  id: string;
  name: string;
  location: string;
  admin: {
    _id: string;
    name: string;
    email: string;
  };
  totalMembers: number;
  totalRevenue: number;
  totalOrders: number;
  averageOrderValue: number;
  activeCustomers: number;
  createdAt: string;
  revenuePerMember: number;
  ordersPerMember: number;
  formattedRevenue: string;
  formattedAverageOrderValue: string;
  formattedRevenuePerMember: string;
}

export interface TopGroupsSummary {
  totalGroups: number;
  totalRevenue: number;
  totalOrders: number;
  totalMembers: number;
  averageGroupSize: number;
  period: number;
  sortBy: string;
  limit: number;
}

export interface TopGroupsResponse {
  success: boolean;
  groups: TopGroup[];
  summary: TopGroupsSummary;
  lastUpdated: string;
}

// Query Parameters
export interface DashboardStatsParams {
  period?: string; // '7', '30', '90', '365'
}

export interface SalesChartParams {
  period?: 'week' | 'month' | 'year';
}

export interface TopGroupsParams {
  period?: string;
  limit?: number;
  sortBy?: 'revenue' | 'orders' | 'members';
}

// Base query with authentication
const baseQuery = fetchBaseQuery({
  baseUrl: '/api/admin/dashboard',
  prepareHeaders: (headers, { getState }) => {
    // Get token from localStorage
    const token = typeof window !== 'undefined' ? localStorage.getItem('accessToken') : null;
    if (token) {
      headers.set('authorization', `Bearer ${token}`);
    }
    headers.set('Content-Type', 'application/json');
    return headers;
  },
});

export const adminDashboardApi = createApi({
  reducerPath: 'adminDashboardApi',
  baseQuery,
  tagTypes: ['DashboardStats', 'SalesChart', 'TopGroups'],
  endpoints: (builder) => ({
    // Get dashboard statistics
    getDashboardStats: builder.query<DashboardStatsResponse, DashboardStatsParams>({
      query: (params = {}) => {
        const searchParams = new URLSearchParams();
        if (params.period) searchParams.append('period', params.period);
        return `stats?${searchParams.toString()}`;
      },
      providesTags: ['DashboardStats'],
      // Refetch every 5 minutes
      keepUnusedDataFor: 300,
    }),

    // Get sales chart data
    getSalesChart: builder.query<SalesChartResponse, SalesChartParams>({
      query: (params = {}) => {
        const searchParams = new URLSearchParams();
        if (params.period) searchParams.append('period', params.period);
        return `sales-chart?${searchParams.toString()}`;
      },
      providesTags: ['SalesChart'],
      // Refetch every 10 minutes
      keepUnusedDataFor: 600,
    }),

    // Get top performing groups
    getTopGroups: builder.query<TopGroupsResponse, TopGroupsParams>({
      query: (params = {}) => {
        const searchParams = new URLSearchParams();
        if (params.period) searchParams.append('period', params.period);
        if (params.limit) searchParams.append('limit', params.limit.toString());
        if (params.sortBy) searchParams.append('sortBy', params.sortBy);
        return `top-groups?${searchParams.toString()}`;
      },
      providesTags: ['TopGroups'],
      // Refetch every 15 minutes
      keepUnusedDataFor: 900,
    }),

    // Refresh all dashboard data
    refreshDashboard: builder.mutation<{ success: boolean }, void>({
      query: () => ({
        url: 'refresh',
        method: 'POST',
      }),
      invalidatesTags: ['DashboardStats', 'SalesChart', 'TopGroups'],
    }),
  }),
});

export const {
  useGetDashboardStatsQuery,
  useGetSalesChartQuery,
  useGetTopGroupsQuery,
  useRefreshDashboardMutation,
  
  // Lazy queries for manual triggering
  useLazyGetDashboardStatsQuery,
  useLazyGetSalesChartQuery,
  useLazyGetTopGroupsQuery,
} = adminDashboardApi;

// Selectors for accessing cached data
export const selectDashboardStats = (state: any) => 
  adminDashboardApi.endpoints.getDashboardStats.select({})(state);

export const selectSalesChart = (state: any) => 
  adminDashboardApi.endpoints.getSalesChart.select({})(state);

export const selectTopGroups = (state: any) => 
  adminDashboardApi.endpoints.getTopGroups.select({})(state);

// Custom hooks for real-time updates
export const useRealTimeDashboard = (refreshInterval = 300000) => { // 5 minutes default
  const statsQuery = useGetDashboardStatsQuery({}, {
    pollingInterval: refreshInterval,
    refetchOnMountOrArgChange: true,
    refetchOnFocus: true,
  });

  const salesQuery = useGetSalesChartQuery({}, {
    pollingInterval: refreshInterval * 2, // 10 minutes for sales chart
    refetchOnMountOrArgChange: true,
  });

  const groupsQuery = useGetTopGroupsQuery({}, {
    pollingInterval: refreshInterval * 3, // 15 minutes for top groups
    refetchOnMountOrArgChange: true,
  });

  return {
    stats: statsQuery,
    sales: salesQuery,
    groups: groupsQuery,
    isLoading: statsQuery.isLoading || salesQuery.isLoading || groupsQuery.isLoading,
    error: statsQuery.error || salesQuery.error || groupsQuery.error,
    refetch: () => {
      statsQuery.refetch();
      salesQuery.refetch();
      groupsQuery.refetch();
    }
  };
};

// Utility functions for data transformation
export const formatCurrency = (amount: number): string => {
  if (amount >= 1000000) {
    return `R${(amount / 1000000).toFixed(1)}M`;
  } else if (amount >= 1000) {
    return `R${(amount / 1000).toFixed(1)}K`;
  } else {
    return `R${amount.toFixed(2)}`;
  }
};

export const formatTrend = (trend: number): string => {
  const sign = trend >= 0 ? '+' : '';
  return `${sign}${trend.toFixed(1)}%`;
};

export const getTrendColor = (trend: number): string => {
  if (trend > 0) return 'text-green-600';
  if (trend < 0) return 'text-red-600';
  return 'text-gray-600';
};

export const getTrendIcon = (trend: number): 'up' | 'down' | 'neutral' => {
  if (trend > 0) return 'up';
  if (trend < 0) return 'down';
  return 'neutral';
};
