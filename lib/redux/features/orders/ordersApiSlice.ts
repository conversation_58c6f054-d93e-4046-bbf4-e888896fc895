// lib/redux/features/orders/ordersApiSlice.ts

import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { IMemberOrder } from '@/models/MemberOrder';
import { IGroupOrder } from '@/models/GroupOrder';

// Order Analytics Interfaces
export interface OrderOverview {
  totalOrders: number;
  groupOrders: number;
  averageOrderValue: number;
  totalRevenue: number;
  ordersByStatus: Record<string, number>;
  revenueGrowth: number;
  ordersGrowth: number;
  avgOrderGrowth: number;
  groupOrdersGrowth: number;
}

export interface OrderTrendData {
  date: string;
  orders: number;
  revenue: number;
  groupOrders: number;
}

export interface TopSellingProduct {
  _id: string;
  name: string;
  totalSold: number;
  totalRevenue: number;
  image?: string;
}

export interface OrderAnalytics {
  overview: OrderOverview;
  trends: OrderTrendData[];
  topProducts: TopSellingProduct[];
  recentOrders: IMemberOrder[];
}

export interface OrderFilters {
  status?: string;
  dateFrom?: string;
  dateTo?: string;
  groupId?: string;
  userId?: string;
  paymentStatus?: string;
}

export interface PaginatedOrdersResponse {
  orders: IMemberOrder[];
  totalCount: number;
  totalPages: number;
  currentPage: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface UpdateOrderStatusInput {
  orderId: string;
  status: string;
  notes?: string;
}

export interface ShipOrderInput {
  orderId: string;
  trackingNumber: string;
  carrier: string;
  estimatedDelivery?: string;
}

export const ordersApiSlice = createApi({
  reducerPath: 'ordersApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/api' }),
  tagTypes: ['Order', 'OrderAnalytics', 'OrderOverview'],
  endpoints: (builder) => ({
    // Get order analytics and overview
    getOrderAnalytics: builder.query<OrderAnalytics, { period?: string }>({
      query: ({ period = '30d' } = {}) => `/orders/analytics?period=${period}`,
      providesTags: ['OrderAnalytics'],
    }),

    // Get order overview stats
    getOrderOverview: builder.query<OrderOverview, void>({
      query: () => '/orders/overview',
      providesTags: ['OrderOverview'],
    }),

    // Get all orders with pagination and filters
    getAllOrders: builder.query<PaginatedOrdersResponse, {
      page?: number;
      limit?: number;
      filters?: OrderFilters;
    }>({
      query: ({ page = 1, limit = 20, filters = {} }) => {
        const params = new URLSearchParams({
          page: page.toString(),
          limit: limit.toString(),
          ...Object.fromEntries(
            Object.entries(filters).filter(([_, value]) => value !== undefined && value !== '')
          ),
        });
        return `/orders/admin?${params.toString()}`;
      },
      providesTags: (result) =>
        result
          ? [
              ...result.orders.map(({ _id }) => ({ type: 'Order' as const, id: _id })),
              { type: 'Order', id: 'LIST' },
            ]
          : [{ type: 'Order', id: 'LIST' }],
    }),

    // Get single order by ID
    getOrderById: builder.query<IMemberOrder, string>({
      query: (orderId) => `/orders/${orderId}`,
      providesTags: (result, error, orderId) => [{ type: 'Order', id: orderId }],
    }),

    // Update order status
    updateOrderStatus: builder.mutation<IMemberOrder, UpdateOrderStatusInput>({
      query: ({ orderId, status, notes }) => ({
        url: `/orders/${orderId}/status`,
        method: 'PATCH',
        body: { status, notes },
      }),
      invalidatesTags: (result, error, { orderId }) => [
        { type: 'Order', id: orderId },
        { type: 'Order', id: 'LIST' },
        'OrderAnalytics',
        'OrderOverview',
      ],
    }),

    // Ship order
    shipOrder: builder.mutation<IMemberOrder, ShipOrderInput>({
      query: ({ orderId, trackingNumber, carrier, estimatedDelivery }) => ({
        url: `/orders/${orderId}/ship`,
        method: 'PATCH',
        body: { trackingNumber, carrier, estimatedDelivery },
      }),
      invalidatesTags: (result, error, { orderId }) => [
        { type: 'Order', id: orderId },
        { type: 'Order', id: 'LIST' },
        'OrderAnalytics',
      ],
    }),

    // Cancel order
    cancelOrder: builder.mutation<IMemberOrder, { orderId: string; reason?: string }>({
      query: ({ orderId, reason }) => ({
        url: `/orders/${orderId}/cancel`,
        method: 'PATCH',
        body: { reason },
      }),
      invalidatesTags: (result, error, { orderId }) => [
        { type: 'Order', id: orderId },
        { type: 'Order', id: 'LIST' },
        'OrderAnalytics',
        'OrderOverview',
      ],
    }),

    // Get order trends
    getOrderTrends: builder.query<OrderTrendData[], { period?: string }>({
      query: ({ period = '30d' } = {}) => `/orders/trends?period=${period}`,
      providesTags: ['OrderAnalytics'],
    }),

    // Get top selling products
    getTopSellingProducts: builder.query<TopSellingProduct[], { limit?: number; period?: string }>({
      query: ({ limit = 10, period = '30d' } = {}) => 
        `/orders/top-products?limit=${limit}&period=${period}`,
      providesTags: ['OrderAnalytics'],
    }),

    // Refresh analytics
    refreshOrderAnalytics: builder.mutation<OrderAnalytics, void>({
      query: () => ({
        url: '/orders/analytics/refresh',
        method: 'POST',
      }),
      invalidatesTags: ['OrderAnalytics', 'OrderOverview'],
    }),
  }),
});

export const {
  useGetOrderAnalyticsQuery,
  useGetOrderOverviewQuery,
  useGetAllOrdersQuery,
  useGetOrderByIdQuery,
  useUpdateOrderStatusMutation,
  useShipOrderMutation,
  useCancelOrderMutation,
  useGetOrderTrendsQuery,
  useGetTopSellingProductsQuery,
  useRefreshOrderAnalyticsMutation,
} = ordersApiSlice;
