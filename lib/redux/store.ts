// lib/redux/store.ts
import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import { groupsApi } from './features/groups/groupsApiSlice';
import { locationsApi } from './features/locations/locationsApiSlice';
import { groupRequestsApi } from './features/groupRequests/groupRequestsApiSlice';
import { adminDashboardApi } from './features/admin/adminDashboardApiSlice';
import { groupStatsApiSlice } from './features/groupStats/groupStatsApiSlice';

// Payment Module Reducers (using direct imports to avoid client/server issues)
import payFastSlice from '@/modules/payments/payfast/store/payFastSlice';
import peachSlice from '@/modules/payments/peach/store/peachSlice';
import codSlice from '@/modules/payments/cod/store/codSlice';

export const store = configureStore({
  reducer: {
    [groupsApi.reducerPath]: groupsApi.reducer,
    [locationsApi.reducerPath]: locationsApi.reducer,
    [groupRequestsApi.reducerPath]: groupRequestsApi.reducer,
    [adminDashboardApi.reducerPath]: adminDashboardApi.reducer,
    [groupStatsApiSlice.reducerPath]: groupStatsApiSlice.reducer,
    // Payment Modules
    payfast: payFastSlice,
    peach: peachSlice,
    cod: codSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(
      groupsApi.middleware,
      locationsApi.middleware,
      groupRequestsApi.middleware,
      adminDashboardApi.middleware,
      groupStatsApiSlice.middleware
    ),
});

setupListeners(store.dispatch);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
