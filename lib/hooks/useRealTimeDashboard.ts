// lib/hooks/useRealTimeDashboard.ts

import { useEffect, useState, useCallback, useRef } from 'react';
import { useRealTimeDashboard as useRTKRealTimeDashboard } from '@/lib/redux/features/admin/adminDashboardApiSlice';

export interface DashboardData {
  stats: {
    revenue: { value: string; trend: number; formatted: string };
    orders: { value: number; trend: number; formatted: string };
    customers: { value: number; trend: number; formatted: string };
    averageSale: { value: number; trend: number; formatted: string };
    totalGroups: number;
    totalProducts: number;
  };
  salesChart: {
    data: Array<{
      day: string;
      date: string;
      sales: number;
      orders: number;
      paidOrders: number;
    }>;
    summary: {
      totalSales: number;
      totalOrders: number;
      salesTrend: number;
      averageOrderValue: number;
    };
  };
  topGroups: {
    groups: Array<{
      id: string;
      name: string;
      location: string;
      totalMembers: number;
      totalRevenue: number;
      totalOrders: number;
      formattedRevenue: string;
    }>;
    summary: {
      totalGroups: number;
      totalRevenue: number;
      averageGroupSize: number;
    };
  };
}

export interface UseRealTimeDashboardOptions {
  refreshInterval?: number; // in milliseconds
  autoRefresh?: boolean;
  onError?: (error: any) => void;
  onDataUpdate?: (data: DashboardData) => void;
}

export interface UseRealTimeDashboardReturn {
  data: DashboardData | null;
  isLoading: boolean;
  error: any;
  lastUpdated: Date | null;
  refresh: () => void;
  isRefreshing: boolean;
  connectionStatus: 'connected' | 'disconnected' | 'connecting';
}

export function useRealTimeDashboard(
  options: UseRealTimeDashboardOptions = {}
): UseRealTimeDashboardReturn {
  const {
    refreshInterval = 300000, // 5 minutes default
    autoRefresh = true,
    onError,
    onDataUpdate
  } = options;

  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'connecting'>('connecting');

  // Use the RTK Query real-time dashboard hook
  const {
    stats,
    sales,
    groups,
    isLoading,
    error,
    refetch
  } = useRTKRealTimeDashboard(autoRefresh ? refreshInterval : 0);

  // Combine all data into a single object
  const data: DashboardData | null = stats.data && sales.data && groups.data ? {
    stats: {
      revenue: stats.data.stats.revenue,
      orders: stats.data.stats.orders,
      customers: stats.data.stats.customers,
      averageSale: stats.data.stats.averageSale,
      totalGroups: stats.data.stats.totalGroups,
      totalProducts: stats.data.stats.totalProducts,
    },
    salesChart: {
      data: sales.data.data,
      summary: sales.data.summary,
    },
    topGroups: {
      groups: groups.data.groups,
      summary: groups.data.summary,
    }
  } : null;

  // Manual refresh function
  const refresh = useCallback(() => {
    refetch();
  }, [refetch]);

  // Update connection status based on loading and error states
  useEffect(() => {
    if (isLoading) {
      setConnectionStatus('connecting');
    } else if (error) {
      setConnectionStatus('disconnected');
      onError?.(error);
    } else if (data) {
      setConnectionStatus('connected');
      setLastUpdated(new Date());
      onDataUpdate?.(data);
    }
  }, [isLoading, error, data, onError, onDataUpdate]);

  // Check if any query is currently fetching
  const isRefreshing = stats.isFetching || sales.isFetching || groups.isFetching;

  return {
    data,
    isLoading,
    error: stats.error || sales.error || groups.error,
    lastUpdated,
    refresh,
    isRefreshing,
    connectionStatus
  };
}

// Hook for dashboard notifications
export function useDashboardNotifications() {
  const [notifications, setNotifications] = useState<Array<{
    id: string;
    type: 'info' | 'warning' | 'error' | 'success';
    title: string;
    message: string;
    timestamp: Date;
    read: boolean;
  }>>([]);

  const addNotification = useCallback((notification: Omit<typeof notifications[0], 'id' | 'timestamp' | 'read'>) => {
    const newNotification = {
      ...notification,
      id: Math.random().toString(36).substr(2, 9),
      timestamp: new Date(),
      read: false
    };
    setNotifications(prev => [newNotification, ...prev].slice(0, 50)); // Keep only last 50
  }, []);

  const markAsRead = useCallback((id: string) => {
    setNotifications(prev => 
      prev.map(notif => 
        notif.id === id ? { ...notif, read: true } : notif
      )
    );
  }, []);

  const clearAll = useCallback(() => {
    setNotifications([]);
  }, []);

  const unreadCount = notifications.filter(n => !n.read).length;

  return {
    notifications,
    unreadCount,
    addNotification,
    markAsRead,
    clearAll
  };
}

// Hook for dashboard performance monitoring
export function useDashboardPerformance() {
  const [metrics, setMetrics] = useState({
    loadTime: 0,
    renderTime: 0,
    apiResponseTime: 0,
    errorRate: 0,
    lastMeasurement: null as Date | null
  });

  const measurePerformance = useCallback((operation: string, startTime: number) => {
    const endTime = performance.now();
    const duration = endTime - startTime;

    setMetrics(prev => ({
      ...prev,
      [operation]: duration,
      lastMeasurement: new Date()
    }));

    // Log performance metrics in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`Dashboard ${operation}: ${duration.toFixed(2)}ms`);
    }
  }, []);

  const startMeasurement = useCallback(() => {
    return performance.now();
  }, []);

  return {
    metrics,
    measurePerformance,
    startMeasurement
  };
}

// Hook for dashboard state persistence
export function useDashboardState() {
  const [preferences, setPreferences] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('dashboard-preferences');
      return saved ? JSON.parse(saved) : {
        refreshInterval: 300000,
        autoRefresh: true,
        compactMode: false,
        selectedPeriod: '30',
        collapsedSections: []
      };
    }
    return {
      refreshInterval: 300000,
      autoRefresh: true,
      compactMode: false,
      selectedPeriod: '30',
      collapsedSections: []
    };
  });

  const updatePreferences = useCallback((updates: Partial<typeof preferences>) => {
    setPreferences(prev => {
      const newPrefs = { ...prev, ...updates };
      if (typeof window !== 'undefined') {
        localStorage.setItem('dashboard-preferences', JSON.stringify(newPrefs));
      }
      return newPrefs;
    });
  }, []);

  return {
    preferences,
    updatePreferences
  };
}

// Hook for Server-Sent Events real-time updates
export function useServerSentEvents(options: { enabled?: boolean } = {}) {
  const { enabled = true } = options;
  const [data, setData] = useState<any>(null);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('disconnected');
  const [error, setError] = useState<string | null>(null);
  const eventSourceRef = useRef<EventSource | null>(null);

  const connect = useCallback(() => {
    if (!enabled || eventSourceRef.current) return;

    const token = typeof window !== 'undefined' ? localStorage.getItem('accessToken') : null;
    if (!token) {
      setError('No authentication token available');
      return;
    }

    setConnectionStatus('connecting');
    setError(null);

    try {
      const eventSource = new EventSource('/api/ws', {
        // Note: EventSource doesn't support custom headers, so we'll use a different approach
      });

      eventSource.onopen = () => {
        setConnectionStatus('connected');
        setError(null);
      };

      eventSource.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          if (message.type === 'dashboard_update') {
            setData(message.data);
          }
        } catch (err) {
          console.error('Error parsing SSE message:', err);
        }
      };

      eventSource.onerror = () => {
        setConnectionStatus('disconnected');
        setError('Connection lost');
        eventSource.close();
        eventSourceRef.current = null;

        // Attempt to reconnect after 5 seconds
        setTimeout(() => {
          if (enabled) connect();
        }, 5000);
      };

      eventSourceRef.current = eventSource;
    } catch (err) {
      setError('Failed to establish connection');
      setConnectionStatus('disconnected');
    }
  }, [enabled]);

  const disconnect = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
    setConnectionStatus('disconnected');
  }, []);

  useEffect(() => {
    if (enabled) {
      connect();
    } else {
      disconnect();
    }

    return () => {
      disconnect();
    };
  }, [enabled, connect, disconnect]);

  return {
    data,
    connectionStatus,
    error,
    connect,
    disconnect
  };
}

// Enhanced real-time dashboard hook with fallback polling
export function useEnhancedRealTimeDashboard(options: UseRealTimeDashboardOptions = {}) {
  const {
    refreshInterval = 300000, // 5 minutes default
    autoRefresh = true,
    onError,
    onDataUpdate
  } = options;

  // Primary RTK Query hook
  const rtkDashboard = useRealTimeDashboard({
    refreshInterval: autoRefresh ? refreshInterval : 0,
    autoRefresh,
    onError,
    onDataUpdate
  });

  // Server-Sent Events for real-time updates
  const sseData = useServerSentEvents({ enabled: autoRefresh });

  // Combine data sources - prefer SSE data if available and recent
  const combinedData = sseData.data && sseData.connectionStatus === 'connected'
    ? sseData.data
    : rtkDashboard.data;

  const combinedConnectionStatus = sseData.connectionStatus === 'connected'
    ? 'connected'
    : rtkDashboard.connectionStatus;

  return {
    ...rtkDashboard,
    data: combinedData,
    connectionStatus: combinedConnectionStatus,
    sseStatus: sseData.connectionStatus,
    sseError: sseData.error,
    hasRealTimeConnection: sseData.connectionStatus === 'connected'
  };
}
