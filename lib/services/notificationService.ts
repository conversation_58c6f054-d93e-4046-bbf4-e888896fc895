// lib/services/notificationService.ts

import { connectToDatabase } from "@/lib/dbconnect";
import { User } from "@/models/User";
import { GroupRequest } from "@/models/GroupRequest";

export interface NotificationData {
  userId: string;
  type: 'group_request_submitted' | 'group_request_approved' | 'group_request_rejected';
  title: string;
  message: string;
  data?: Record<string, any>;
}

export class NotificationService {
  /**
   * Send notification when a new group request is submitted
   */
  static async notifyGroupRequestSubmitted(groupRequestId: string) {
    try {
      await connectToDatabase();
      
      const groupRequest = await GroupRequest.findById(groupRequestId)
        .populate('userId', 'name email');
      
      if (!groupRequest) {
        console.error('Group request not found:', groupRequestId);
        return;
      }

      // Get all admin users
      const adminUsers = await User.find({ role: 'admin' }, 'name email');
      
      // For now, we'll just log the notification
      // In a real implementation, you would send emails, push notifications, etc.
      console.log('📧 New Group Request Notification:', {
        type: 'group_request_submitted',
        groupRequest: {
          id: groupRequest._id,
          groupName: groupRequest.requestedGroupName,
          requester: groupRequest.userName,
          location: groupRequest.fullLocationPath
        },
        notifyAdmins: adminUsers.map(admin => ({
          name: admin.name,
          email: admin.email
        }))
      });

      // TODO: Implement actual notification delivery
      // - Send email to admins
      // - Create in-app notifications
      // - Send push notifications if implemented
      
    } catch (error) {
      console.error('Error sending group request notification:', error);
    }
  }

  /**
   * Send notification when a group request is approved
   */
  static async notifyGroupRequestApproved(groupRequestId: string, createdGroupId?: string) {
    try {
      await connectToDatabase();
      
      const groupRequest = await GroupRequest.findById(groupRequestId)
        .populate('userId', 'name email')
        .populate('reviewedBy', 'name email')
        .populate('createdGroupId', 'name _id');
      
      if (!groupRequest) {
        console.error('Group request not found:', groupRequestId);
        return;
      }

      const user = groupRequest.userId as any;
      const reviewer = groupRequest.reviewedBy as any;
      const createdGroup = groupRequest.createdGroupId as any;

      console.log('✅ Group Request Approved Notification:', {
        type: 'group_request_approved',
        recipient: {
          name: user.name,
          email: user.email
        },
        groupRequest: {
          id: groupRequest._id,
          groupName: groupRequest.requestedGroupName,
          location: groupRequest.fullLocationPath,
          reviewedBy: reviewer?.name,
          reviewNotes: groupRequest.reviewNotes
        },
        createdGroup: createdGroup ? {
          id: createdGroup._id,
          name: createdGroup.name
        } : null
      });

      // TODO: Implement actual notification delivery
      // - Send congratulatory email to user
      // - Create in-app notification
      // - Send push notification if implemented
      
    } catch (error) {
      console.error('Error sending approval notification:', error);
    }
  }

  /**
   * Send notification when a group request is rejected
   */
  static async notifyGroupRequestRejected(groupRequestId: string) {
    try {
      await connectToDatabase();
      
      const groupRequest = await GroupRequest.findById(groupRequestId)
        .populate('userId', 'name email')
        .populate('reviewedBy', 'name email');
      
      if (!groupRequest) {
        console.error('Group request not found:', groupRequestId);
        return;
      }

      const user = groupRequest.userId as any;
      const reviewer = groupRequest.reviewedBy as any;

      console.log('❌ Group Request Rejected Notification:', {
        type: 'group_request_rejected',
        recipient: {
          name: user.name,
          email: user.email
        },
        groupRequest: {
          id: groupRequest._id,
          groupName: groupRequest.requestedGroupName,
          location: groupRequest.fullLocationPath,
          reviewedBy: reviewer?.name,
          reviewNotes: groupRequest.reviewNotes
        }
      });

      // TODO: Implement actual notification delivery
      // - Send rejection email to user with feedback
      // - Create in-app notification
      // - Send push notification if implemented
      
    } catch (error) {
      console.error('Error sending rejection notification:', error);
    }
  }

  /**
   * Create in-app notification (placeholder for future implementation)
   */
  static async createInAppNotification(notificationData: NotificationData) {
    // TODO: Implement in-app notification storage
    // This would typically save to a notifications collection in the database
    console.log('📱 In-App Notification:', notificationData);
  }

  /**
   * Send email notification (placeholder for future implementation)
   */
  static async sendEmailNotification(
    to: string, 
    subject: string, 
    htmlContent: string, 
    textContent?: string
  ) {
    // TODO: Implement email sending using your preferred service
    // (SendGrid, AWS SES, Nodemailer, etc.)
    console.log('📧 Email Notification:', {
      to,
      subject,
      htmlContent: htmlContent.substring(0, 100) + '...',
      textContent: textContent?.substring(0, 100) + '...'
    });
  }

  /**
   * Send push notification (placeholder for future implementation)
   */
  static async sendPushNotification(
    userId: string,
    title: string,
    body: string,
    data?: Record<string, any>
  ) {
    // TODO: Implement push notifications using Firebase, OneSignal, etc.
    console.log('🔔 Push Notification:', {
      userId,
      title,
      body,
      data
    });
  }

  /**
   * Get notification templates
   */
  static getEmailTemplates() {
    return {
      groupRequestSubmitted: {
        subject: 'New Group Request Submitted - StokvelMarket',
        getHtml: (data: any) => `
          <h2>New Group Request Submitted</h2>
          <p>A new group creation request has been submitted and requires your review.</p>
          <h3>Request Details:</h3>
          <ul>
            <li><strong>Group Name:</strong> ${data.groupName}</li>
            <li><strong>Requester:</strong> ${data.requesterName} (${data.requesterEmail})</li>
            <li><strong>Location:</strong> ${data.location}</li>
            <li><strong>Request Date:</strong> ${data.requestDate}</li>
          </ul>
          <p>Please log in to the admin panel to review and approve/reject this request.</p>
        `,
        getText: (data: any) => `
          New Group Request Submitted
          
          A new group creation request has been submitted and requires your review.
          
          Request Details:
          - Group Name: ${data.groupName}
          - Requester: ${data.requesterName} (${data.requesterEmail})
          - Location: ${data.location}
          - Request Date: ${data.requestDate}
          
          Please log in to the admin panel to review and approve/reject this request.
        `
      },
      groupRequestApproved: {
        subject: 'Your Group Request Has Been Approved! - StokvelMarket',
        getHtml: (data: any) => `
          <h2>Congratulations! Your Group Request Has Been Approved</h2>
          <p>Great news! Your request to create a new Stokvel group has been approved.</p>
          <h3>Group Details:</h3>
          <ul>
            <li><strong>Group Name:</strong> ${data.groupName}</li>
            <li><strong>Location:</strong> ${data.location}</li>
            <li><strong>You are now the Group Admin</strong></li>
          </ul>
          ${data.reviewNotes ? `<p><strong>Admin Notes:</strong> ${data.reviewNotes}</p>` : ''}
          <p>You can now start inviting members to your group and begin your Stokvel journey!</p>
        `,
        getText: (data: any) => `
          Congratulations! Your Group Request Has Been Approved
          
          Great news! Your request to create a new Stokvel group has been approved.
          
          Group Details:
          - Group Name: ${data.groupName}
          - Location: ${data.location}
          - You are now the Group Admin
          
          ${data.reviewNotes ? `Admin Notes: ${data.reviewNotes}` : ''}
          
          You can now start inviting members to your group and begin your Stokvel journey!
        `
      },
      groupRequestRejected: {
        subject: 'Group Request Update - StokvelMarket',
        getHtml: (data: any) => `
          <h2>Group Request Update</h2>
          <p>Thank you for your interest in creating a Stokvel group. After review, we're unable to approve your request at this time.</p>
          <h3>Request Details:</h3>
          <ul>
            <li><strong>Group Name:</strong> ${data.groupName}</li>
            <li><strong>Location:</strong> ${data.location}</li>
          </ul>
          ${data.reviewNotes ? `<p><strong>Reason:</strong> ${data.reviewNotes}</p>` : ''}
          <p>You're welcome to submit a new request with any necessary adjustments. If you have questions, please contact our support team.</p>
        `,
        getText: (data: any) => `
          Group Request Update
          
          Thank you for your interest in creating a Stokvel group. After review, we're unable to approve your request at this time.
          
          Request Details:
          - Group Name: ${data.groupName}
          - Location: ${data.location}
          
          ${data.reviewNotes ? `Reason: ${data.reviewNotes}` : ''}
          
          You're welcome to submit a new request with any necessary adjustments. If you have questions, please contact our support team.
        `
      }
    };
  }
}
