// lib/services/backend-error-service.ts

import { NextResponse } from "next/server";

// Error types enum
export enum ErrorType {
  AUTHENTICATION_REQUIRED = 'AUTHENTICATION_REQUIRED',
  INVALID_TOKEN = 'INVALID_TOKEN',
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  NOT_FOUND = 'NOT_FOUND',
  DATABASE_ERROR = 'DATABASE_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  SERVER_ERROR = 'SERVER_ERROR',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE'
}

// Error severity levels
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// Standard error response interface
export interface StandardErrorResponse {
  success: false;
  error: {
    type: ErrorType;
    code: string;
    message: string;
    details?: any;
    severity: ErrorSeverity;
    timestamp: string;
    requestId?: string;
  };
  fallbackData?: any;
  retryable: boolean;
  retryAfter?: number; // seconds
}

// Success response interface
export interface StandardSuccessResponse<T = any> {
  success: true;
  data: T;
  meta?: {
    timestamp: string;
    requestId?: string;
    cached?: boolean;
    source?: string;
  };
}

// Combined response type
export type StandardResponse<T = any> = StandardSuccessResponse<T> | StandardErrorResponse;

// Error configuration
interface ErrorConfig {
  httpStatus: number;
  severity: ErrorSeverity;
  retryable: boolean;
  defaultMessage: string;
  fallbackDataGenerator?: () => any;
}

const ERROR_CONFIGS: Record<ErrorType, ErrorConfig> = {
  [ErrorType.AUTHENTICATION_REQUIRED]: {
    httpStatus: 401,
    severity: ErrorSeverity.MEDIUM,
    retryable: false,
    defaultMessage: 'Authentication is required to access this resource'
  },
  [ErrorType.INVALID_TOKEN]: {
    httpStatus: 401,
    severity: ErrorSeverity.MEDIUM,
    retryable: false,
    defaultMessage: 'The provided authentication token is invalid or expired'
  },
  [ErrorType.INSUFFICIENT_PERMISSIONS]: {
    httpStatus: 403,
    severity: ErrorSeverity.MEDIUM,
    retryable: false,
    defaultMessage: 'You do not have sufficient permissions to access this resource'
  },
  [ErrorType.VALIDATION_ERROR]: {
    httpStatus: 400,
    severity: ErrorSeverity.LOW,
    retryable: false,
    defaultMessage: 'The request data is invalid or incomplete'
  },
  [ErrorType.NOT_FOUND]: {
    httpStatus: 404,
    severity: ErrorSeverity.LOW,
    retryable: false,
    defaultMessage: 'The requested resource was not found'
  },
  [ErrorType.DATABASE_ERROR]: {
    httpStatus: 500,
    severity: ErrorSeverity.HIGH,
    retryable: true,
    defaultMessage: 'A database error occurred while processing your request'
  },
  [ErrorType.NETWORK_ERROR]: {
    httpStatus: 503,
    severity: ErrorSeverity.MEDIUM,
    retryable: true,
    defaultMessage: 'A network error occurred while processing your request'
  },
  [ErrorType.RATE_LIMIT_EXCEEDED]: {
    httpStatus: 429,
    severity: ErrorSeverity.MEDIUM,
    retryable: true,
    defaultMessage: 'Rate limit exceeded. Please try again later'
  },
  [ErrorType.SERVER_ERROR]: {
    httpStatus: 500,
    severity: ErrorSeverity.HIGH,
    retryable: true,
    defaultMessage: 'An internal server error occurred'
  },
  [ErrorType.SERVICE_UNAVAILABLE]: {
    httpStatus: 503,
    severity: ErrorSeverity.HIGH,
    retryable: true,
    defaultMessage: 'The service is temporarily unavailable'
  }
};

// Fallback data generators for different data types
export const FallbackDataGenerators = {
  dashboardStats: () => ({
    revenue: {
      value: 0,
      trend: 0,
      description: "No data available",
      formatted: "R0"
    },
    orders: {
      value: 0,
      trend: 0,
      description: "No orders found",
      formatted: "0"
    },
    customers: {
      value: 0,
      trend: 0,
      description: "No customers found",
      formatted: "0%"
    },
    averageSale: {
      value: 0,
      trend: 0,
      description: "No sales data",
      formatted: "R0"
    },
    totalGroups: 0,
    totalProducts: 0,
    period: 30,
    lastUpdated: new Date().toISOString()
  }),

  salesChart: () => ({
    data: [],
    summary: {
      totalSales: 0,
      totalOrders: 0,
      totalPaidOrders: 0,
      averageOrderValue: 0,
      salesTrend: 0,
      period: 'week',
      dateRange: {
        start: new Date().toISOString(),
        end: new Date().toISOString()
      }
    },
    lastUpdated: new Date().toISOString()
  }),

  topGroups: () => ({
    groups: [],
    summary: {
      totalGroups: 0,
      totalRevenue: 0,
      totalOrders: 0,
      totalMembers: 0,
      averageGroupSize: 0,
      period: 30,
      sortBy: 'revenue',
      limit: 10
    },
    lastUpdated: new Date().toISOString()
  }),

  emptyList: () => ([]),
  
  emptyObject: () => ({}),
  
  defaultPagination: () => ({
    items: [],
    pagination: {
      page: 1,
      limit: 10,
      total: 0,
      totalPages: 0,
      hasNext: false,
      hasPrev: false
    }
  })
};

// Backend Error Service Class
export class BackendErrorService {
  private static requestId = 0;

  // Generate unique request ID
  private static generateRequestId(): string {
    return `req_${Date.now()}_${++this.requestId}`;
  }

  // Create standardized error response
  static createErrorResponse(
    errorType: ErrorType,
    customMessage?: string,
    details?: any,
    fallbackData?: any,
    requestId?: string
  ): StandardErrorResponse {
    const config = ERROR_CONFIGS[errorType];
    const id = requestId || this.generateRequestId();

    return {
      success: false,
      error: {
        type: errorType,
        code: errorType,
        message: customMessage || config.defaultMessage,
        details,
        severity: config.severity,
        timestamp: new Date().toISOString(),
        requestId: id
      },
      fallbackData: fallbackData || config.fallbackDataGenerator?.(),
      retryable: config.retryable,
      retryAfter: config.retryable ? this.calculateRetryAfter(config.severity) : undefined
    };
  }

  // Create standardized success response
  static createSuccessResponse<T>(
    data: T,
    cached = false,
    source = 'database',
    requestId?: string
  ): StandardSuccessResponse<T> {
    return {
      success: true,
      data,
      meta: {
        timestamp: new Date().toISOString(),
        requestId: requestId || this.generateRequestId(),
        cached,
        source
      }
    };
  }

  // Create Next.js response with proper headers
  static createNextResponse(
    response: StandardResponse,
    corsHeaders?: Record<string, string>
  ): NextResponse {
    const isError = !response.success;
    const httpStatus = isError 
      ? ERROR_CONFIGS[response.error.type]?.httpStatus || 500 
      : 200;

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...corsHeaders
    };

    // Add retry headers for retryable errors
    if (isError && response.retryable && response.retryAfter) {
      headers['Retry-After'] = response.retryAfter.toString();
    }

    // Add request ID header
    if (response.success && response.meta?.requestId) {
      headers['X-Request-ID'] = response.meta.requestId;
    } else if (!response.success && response.error.requestId) {
      headers['X-Request-ID'] = response.error.requestId;
    }

    return NextResponse.json(response, {
      status: httpStatus,
      headers
    });
  }

  // Calculate retry delay based on severity
  private static calculateRetryAfter(severity: ErrorSeverity): number {
    switch (severity) {
      case ErrorSeverity.LOW: return 5;
      case ErrorSeverity.MEDIUM: return 30;
      case ErrorSeverity.HIGH: return 60;
      case ErrorSeverity.CRITICAL: return 300;
      default: return 30;
    }
  }

  // Handle authentication errors with fallback data
  static handleAuthError(fallbackDataType?: keyof typeof FallbackDataGenerators): NextResponse {
    const fallbackData = fallbackDataType 
      ? FallbackDataGenerators[fallbackDataType]() 
      : undefined;

    const errorResponse = this.createErrorResponse(
      ErrorType.AUTHENTICATION_REQUIRED,
      'Please log in to access this resource',
      undefined,
      fallbackData
    );

    return this.createNextResponse(errorResponse);
  }

  // Handle permission errors with fallback data
  static handlePermissionError(
    requiredRole: string,
    fallbackDataType?: keyof typeof FallbackDataGenerators
  ): NextResponse {
    const fallbackData = fallbackDataType 
      ? FallbackDataGenerators[fallbackDataType]() 
      : undefined;

    const errorResponse = this.createErrorResponse(
      ErrorType.INSUFFICIENT_PERMISSIONS,
      `${requiredRole} access required`,
      { requiredRole },
      fallbackData
    );

    return this.createNextResponse(errorResponse);
  }

  // Handle database errors with fallback data
  static handleDatabaseError(
    error: any,
    fallbackDataType?: keyof typeof FallbackDataGenerators
  ): NextResponse {
    const fallbackData = fallbackDataType 
      ? FallbackDataGenerators[fallbackDataType]() 
      : undefined;

    const errorResponse = this.createErrorResponse(
      ErrorType.DATABASE_ERROR,
      'Database temporarily unavailable',
      process.env.NODE_ENV === 'development' ? error.message : undefined,
      fallbackData
    );

    return this.createNextResponse(errorResponse);
  }

  // Handle validation errors
  static handleValidationError(validationErrors: any): NextResponse {
    const errorResponse = this.createErrorResponse(
      ErrorType.VALIDATION_ERROR,
      'Request validation failed',
      validationErrors
    );

    return this.createNextResponse(errorResponse);
  }

  // Handle not found errors with fallback data
  static handleNotFoundError(
    resource: string,
    fallbackDataType?: keyof typeof FallbackDataGenerators
  ): NextResponse {
    const fallbackData = fallbackDataType 
      ? FallbackDataGenerators[fallbackDataType]() 
      : undefined;

    const errorResponse = this.createErrorResponse(
      ErrorType.NOT_FOUND,
      `${resource} not found`,
      { resource },
      fallbackData
    );

    return this.createNextResponse(errorResponse);
  }

  // Log errors for monitoring
  static logError(error: StandardErrorResponse, context?: any): void {
    const logData = {
      ...error,
      context,
      environment: process.env.NODE_ENV
    };

    if (error.error.severity === ErrorSeverity.CRITICAL) {
      console.error('CRITICAL ERROR:', logData);
    } else if (error.error.severity === ErrorSeverity.HIGH) {
      console.error('HIGH SEVERITY ERROR:', logData);
    } else {
      console.warn('ERROR:', logData);
    }

    // In production, you would send this to your monitoring service
    // e.g., Sentry, DataDog, CloudWatch, etc.
  }
}
