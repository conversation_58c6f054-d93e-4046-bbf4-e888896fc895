// lib/services/aiChatService.ts
import { connectToDatabase } from "@/lib/dbconnect";
import { User } from "@/models/User";
import { Product } from "@/models/Product";
import { StokvelGroup } from "@/models/StokvelGroup";
import { MemberOrder } from "@/models/MemberOrder";
import { ShoppingCart } from "@/models/ShoppingCart";

export interface ChatContext {
  userId?: string;
  userRole?: string;
  currentPage?: string;
  groupId?: string;
  sessionId: string;
}

export interface AIResponse {
  success: boolean;
  message: string;
  data?: any;
  suggestions?: string[];
  actions?: {
    type: string;
    label: string;
    url?: string;
    action?: string;
  }[];
}

export class AIChatService {
  
  /**
   * Get personalized quick actions based on user context
   */
  static async getQuickActions(context: ChatContext): Promise<AIResponse> {
    try {
      await connectToDatabase();
      
      const actions = [];
      
      if (context.userId) {
        // Get user's recent activity to suggest relevant actions
        const user = await User.findById(context.userId).lean();
        const recentOrders = await MemberOrder.find({ userId: context.userId })
          .sort({ createdAt: -1 })
          .limit(3)
          .lean();
        
        if (recentOrders.length > 0) {
          actions.push({
            type: "track_order",
            label: "Track Recent Order",
            url: "/orders",
            action: "track_order"
          });
        }
        
        // Check if user has items in cart
        const cart = await ShoppingCart.findOne({ user: context.userId }).lean();
        if (cart && cart.items.length > 0) {
          actions.push({
            type: "view_cart",
            label: "Complete Your Purchase",
            url: "/cart",
            action: "view_cart"
          });
        }
        
        // Check if user is in groups
        if (user?.stokvelGroups && user.stokvelGroups.length > 0) {
          actions.push({
            type: "group_orders",
            label: "Check Group Orders",
            url: "/groups",
            action: "view_groups"
          });
        } else {
          actions.push({
            type: "join_group",
            label: "Find Groups Near You",
            url: "/groups",
            action: "find_groups"
          });
        }
      } else {
        // Guest user actions
        actions.push(
          {
            type: "browse_products",
            label: "Browse Products",
            url: "/products",
            action: "browse_products"
          },
          {
            type: "learn_about_groups",
            label: "Learn About Group Buying",
            url: "/groups",
            action: "learn_groups"
          },
          {
            type: "sign_up",
            label: "Create Account",
            url: "/auth/register",
            action: "sign_up"
          }
        );
      }
      
      return {
        success: true,
        message: "Here are some things I can help you with:",
        actions
      };
    } catch (error) {
      console.error('Error getting quick actions:', error);
      return {
        success: false,
        message: "I'm having trouble loading personalized suggestions right now, but I'm still here to help with any questions!"
      };
    }
  }
  
  /**
   * Get contextual help based on current page
   */
  static async getPageHelp(currentPage: string): Promise<AIResponse> {
    const pageHelp = {
      '/': {
        message: "Welcome to Stokvel Market! I can help you find products, learn about group buying, or get started with your account.",
        suggestions: [
          "How does group buying work?",
          "What products are available?",
          "How do I join a group?",
          "What are the benefits of group buying?"
        ]
      },
      '/products': {
        message: "Looking for products? I can help you find what you need, explain bulk pricing, or show you how to add items to your cart.",
        suggestions: [
          "Show me popular products",
          "How do bulk discounts work?",
          "Can I filter products by category?",
          "How do I add items to my cart?"
        ]
      },
      '/groups': {
        message: "Interested in group buying? I can help you find groups in your area, explain how to join, or show you the benefits.",
        suggestions: [
          "Find groups near me",
          "How do I join a group?",
          "What are the benefits of joining a group?",
          "Can I create my own group?"
        ]
      },
      '/cart': {
        message: "Ready to checkout? I can help you review your items, apply discounts, or explain payment options.",
        suggestions: [
          "How do I apply a discount code?",
          "What payment methods are available?",
          "Can I checkout with my group?",
          "How does delivery work?"
        ]
      },
      '/orders': {
        message: "Need help with your orders? I can help you track deliveries, understand order status, or handle returns.",
        suggestions: [
          "Track my recent order",
          "What do order statuses mean?",
          "How do I return an item?",
          "When will my order arrive?"
        ]
      },
      '/profile': {
        message: "Managing your account? I can help you update your information, manage addresses, or explain account features.",
        suggestions: [
          "How do I update my profile?",
          "How do I add a delivery address?",
          "Where is my referral code?",
          "How do I change my password?"
        ]
      }
    };
    
    const help = pageHelp[currentPage as keyof typeof pageHelp] || {
      message: "I'm here to help you with anything related to Stokvel Market. What would you like to know?",
      suggestions: [
        "How does the platform work?",
        "What can I buy here?",
        "How do I get started?",
        "What are the benefits?"
      ]
    };
    
    return {
      success: true,
      message: help.message,
      suggestions: help.suggestions
    };
  }
  
  /**
   * Search for products based on user query
   */
  static async searchProducts(query: string, limit: number = 5): Promise<AIResponse> {
    try {
      await connectToDatabase();
      
      const products = await Product.find({
        $or: [
          { name: { $regex: query, $options: 'i' } },
          { description: { $regex: query, $options: 'i' } }
        ]
      })
      .populate('category', 'name')
      .limit(limit)
      .lean();
      
      if (products.length === 0) {
        return {
          success: true,
          message: `I couldn't find any products matching "${query}". Would you like me to show you our popular products instead?`,
          suggestions: [
            "Show popular products",
            "Browse all categories",
            "Help me find something specific"
          ]
        };
      }
      
      const productList = products.map(product => 
        `• ${product.name} - R${product.price} (${product.stock > 0 ? 'In stock' : 'Out of stock'})`
      ).join('\n');
      
      return {
        success: true,
        message: `I found ${products.length} product${products.length > 1 ? 's' : ''} matching "${query}":\n\n${productList}\n\nWould you like more details about any of these products?`,
        data: products,
        actions: [{
          type: "view_products",
          label: "View All Results",
          url: `/products?search=${encodeURIComponent(query)}`,
          action: "view_search_results"
        }]
      };
    } catch (error) {
      console.error('Error searching products:', error);
      return {
        success: false,
        message: "I'm having trouble searching for products right now. Please try again in a moment."
      };
    }
  }
  
  /**
   * Get user's order status
   */
  static async getOrderStatus(userId: string, orderNumber?: string): Promise<AIResponse> {
    try {
      await connectToDatabase();
      
      let query: any = { userId };
      if (orderNumber) {
        query.orderNumber = orderNumber;
      }
      
      const orders = await MemberOrder.find(query)
        .populate('items.product', 'name')
        .populate('groupId', 'name')
        .sort({ createdAt: -1 })
        .limit(orderNumber ? 1 : 5)
        .lean();
      
      if (orders.length === 0) {
        return {
          success: true,
          message: orderNumber 
            ? `I couldn't find an order with number "${orderNumber}". Please check the order number and try again.`
            : "You don't have any orders yet. Would you like to start shopping?",
          actions: orderNumber ? [] : [{
            type: "browse_products",
            label: "Start Shopping",
            url: "/products",
            action: "browse_products"
          }]
        };
      }
      
      if (orderNumber && orders.length === 1) {
        const order = orders[0];
        return {
          success: true,
          message: `Here's the status of order ${order.orderNumber}:\n\n` +
                  `Status: ${order.status}\n` +
                  `Payment: ${order.paymentInfo.status}\n` +
                  `Total: R${order.totalAmount}\n` +
                  `Items: ${order.items.length}\n` +
                  `Group: ${order.groupId?.name || 'Individual order'}\n\n` +
                  `${order.shippingInfo?.trackingNumber ? `Tracking: ${order.shippingInfo.trackingNumber}` : 'Tracking info will be available once shipped.'}`,
          data: order
        };
      }
      
      const orderList = orders.map(order => 
        `• Order ${order.orderNumber} - ${order.status} - R${order.totalAmount}`
      ).join('\n');
      
      return {
        success: true,
        message: `Here are your recent orders:\n\n${orderList}\n\nWould you like details about any specific order?`,
        data: orders
      };
    } catch (error) {
      console.error('Error getting order status:', error);
      return {
        success: false,
        message: "I'm having trouble accessing your order information right now. Please try again in a moment."
      };
    }
  }
  
  /**
   * Get group information for user
   */
  static async getGroupInfo(userId: string): Promise<AIResponse> {
    try {
      await connectToDatabase();
      
      const user = await User.findById(userId)
        .populate('stokvelGroups', 'name description memberCount totalSales')
        .lean();
      
      if (!user?.stokvelGroups || user.stokvelGroups.length === 0) {
        return {
          success: true,
          message: "You're not currently a member of any Stokvel groups. Joining a group gives you access to bulk discounts and community buying power!",
          suggestions: [
            "Find groups near me",
            "How do groups work?",
            "What are the benefits?",
            "Can I create a group?"
          ],
          actions: [{
            type: "find_groups",
            label: "Find Groups",
            url: "/groups",
            action: "find_groups"
          }]
        };
      }
      
      const groupList = user.stokvelGroups.map((group: any) => 
        `• ${group.name} - ${group.memberCount || 0} members - R${group.totalSales || 0} total sales`
      ).join('\n');
      
      return {
        success: true,
        message: `You're a member of ${user.stokvelGroups.length} group${user.stokvelGroups.length > 1 ? 's' : ''}:\n\n${groupList}\n\nWould you like to see group orders or activity?`,
        data: user.stokvelGroups,
        actions: [{
          type: "view_groups",
          label: "View Group Details",
          url: "/groups",
          action: "view_groups"
        }]
      };
    } catch (error) {
      console.error('Error getting group info:', error);
      return {
        success: false,
        message: "I'm having trouble accessing your group information right now. Please try again in a moment."
      };
    }
  }
}
