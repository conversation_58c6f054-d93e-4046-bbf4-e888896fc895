// lib/services/rtk-error-service.ts

import { FetchBaseQueryError } from '@reduxjs/toolkit/query';
import { SerializedError } from '@reduxjs/toolkit';
import { FallbackDataGenerators } from './backend-error-service';

// RTK Query error types
export interface RTKQueryError {
  status?: number;
  data?: any;
  error?: string;
}

// Enhanced error information
export interface ProcessedError {
  type: 'network' | 'authentication' | 'permission' | 'validation' | 'server' | 'unknown';
  message: string;
  userMessage: string;
  retryable: boolean;
  retryAfter?: number;
  fallbackData?: any;
  severity: 'low' | 'medium' | 'high' | 'critical';
  code?: string;
  details?: any;
}

// Error message mappings
const ERROR_MESSAGES = {
  network: {
    user: 'Connection problem. Please check your internet connection.',
    technical: 'Network request failed'
  },
  authentication: {
    user: 'Please log in to continue.',
    technical: 'Authentication required'
  },
  permission: {
    user: 'You don\'t have permission to access this feature.',
    technical: 'Insufficient permissions'
  },
  validation: {
    user: 'Please check your input and try again.',
    technical: 'Validation error'
  },
  server: {
    user: 'Something went wrong on our end. Please try again.',
    technical: 'Server error'
  },
  unknown: {
    user: 'An unexpected error occurred. Please try again.',
    technical: 'Unknown error'
  }
};

export class RTKErrorService {
  // Process RTK Query errors and return standardized error info
  static processError(
    error: FetchBaseQueryError | SerializedError | undefined,
    fallbackDataType?: keyof typeof FallbackDataGenerators
  ): ProcessedError {
    if (!error) {
      return this.createProcessedError('unknown', 'No error information available');
    }

    // Handle FetchBaseQueryError (API errors)
    if ('status' in error) {
      return this.processFetchBaseQueryError(error, fallbackDataType);
    }

    // Handle SerializedError (network/parsing errors)
    if ('message' in error) {
      return this.processSerializedError(error, fallbackDataType);
    }

    return this.createProcessedError('unknown', 'Unrecognized error format');
  }

  // Process FetchBaseQueryError
  private static processFetchBaseQueryError(
    error: FetchBaseQueryError,
    fallbackDataType?: keyof typeof FallbackDataGenerators
  ): ProcessedError {
    const status = error.status;
    const data = error.data as any;

    // Handle standardized backend error responses
    if (data && !data.success && data.error) {
      return {
        type: this.mapErrorTypeFromBackend(data.error.type),
        message: data.error.message,
        userMessage: this.getUserMessage(data.error.type, data.error.message),
        retryable: data.retryable || false,
        retryAfter: data.retryAfter,
        fallbackData: data.fallbackData || this.getFallbackData(fallbackDataType),
        severity: data.error.severity || 'medium',
        code: data.error.code,
        details: data.error.details
      };
    }

    // Handle HTTP status codes
    switch (status) {
      case 401:
        return this.createProcessedError(
          'authentication',
          'Authentication required',
          fallbackDataType,
          false
        );
      case 403:
        return this.createProcessedError(
          'permission',
          'Access denied',
          fallbackDataType,
          false
        );
      case 404:
        return this.createProcessedError(
          'validation',
          'Resource not found',
          fallbackDataType,
          false
        );
      case 422:
        return this.createProcessedError(
          'validation',
          'Invalid request data',
          fallbackDataType,
          false
        );
      case 429:
        return this.createProcessedError(
          'server',
          'Too many requests',
          fallbackDataType,
          true,
          60
        );
      case 500:
      case 502:
      case 503:
      case 504:
        return this.createProcessedError(
          'server',
          'Server error',
          fallbackDataType,
          true
        );
      default:
        return this.createProcessedError(
          'unknown',
          `HTTP ${status}: ${data?.message || 'Unknown error'}`,
          fallbackDataType
        );
    }
  }

  // Process SerializedError
  private static processSerializedError(
    error: SerializedError,
    fallbackDataType?: keyof typeof FallbackDataGenerators
  ): ProcessedError {
    const message = error.message || 'Unknown error';

    // Check for network errors
    if (message.includes('fetch') || message.includes('network') || message.includes('NetworkError')) {
      return this.createProcessedError('network', message, fallbackDataType, true);
    }

    // Check for parsing errors
    if (message.includes('JSON') || message.includes('parse')) {
      return this.createProcessedError('server', 'Invalid server response', fallbackDataType, true);
    }

    return this.createProcessedError('unknown', message, fallbackDataType);
  }

  // Create standardized processed error
  private static createProcessedError(
    type: ProcessedError['type'],
    technicalMessage: string,
    fallbackDataType?: keyof typeof FallbackDataGenerators,
    retryable = false,
    retryAfter?: number
  ): ProcessedError {
    const messages = ERROR_MESSAGES[type];
    
    return {
      type,
      message: technicalMessage,
      userMessage: messages.user,
      retryable,
      retryAfter,
      fallbackData: this.getFallbackData(fallbackDataType),
      severity: this.getSeverityForType(type),
      code: type.toUpperCase()
    };
  }

  // Map backend error types to frontend types
  private static mapErrorTypeFromBackend(backendType: string): ProcessedError['type'] {
    switch (backendType) {
      case 'AUTHENTICATION_REQUIRED':
      case 'INVALID_TOKEN':
        return 'authentication';
      case 'INSUFFICIENT_PERMISSIONS':
        return 'permission';
      case 'VALIDATION_ERROR':
      case 'NOT_FOUND':
        return 'validation';
      case 'DATABASE_ERROR':
      case 'SERVER_ERROR':
      case 'SERVICE_UNAVAILABLE':
        return 'server';
      case 'NETWORK_ERROR':
        return 'network';
      default:
        return 'unknown';
    }
  }

  // Get user-friendly message
  private static getUserMessage(errorType: string, technicalMessage: string): string {
    const type = this.mapErrorTypeFromBackend(errorType);
    const baseMessage = ERROR_MESSAGES[type].user;

    // Add specific context for certain errors
    switch (errorType) {
      case 'AUTHENTICATION_REQUIRED':
        return 'Please log in to view this information.';
      case 'INSUFFICIENT_PERMISSIONS':
        return 'You need admin access to view this dashboard.';
      case 'RATE_LIMIT_EXCEEDED':
        return 'Too many requests. Please wait a moment and try again.';
      default:
        return baseMessage;
    }
  }

  // Get severity for error type
  private static getSeverityForType(type: ProcessedError['type']): ProcessedError['severity'] {
    switch (type) {
      case 'network':
      case 'server':
        return 'high';
      case 'authentication':
      case 'permission':
        return 'medium';
      case 'validation':
        return 'low';
      default:
        return 'medium';
    }
  }

  // Get fallback data
  private static getFallbackData(fallbackDataType?: keyof typeof FallbackDataGenerators): any {
    if (!fallbackDataType) return null;
    return FallbackDataGenerators[fallbackDataType]?.() || null;
  }

  // Check if error should trigger retry
  static shouldRetry(error: ProcessedError, attemptCount: number): boolean {
    if (!error.retryable || attemptCount >= 3) return false;

    // Don't retry authentication or permission errors
    if (error.type === 'authentication' || error.type === 'permission') return false;

    // Don't retry validation errors
    if (error.type === 'validation') return false;

    return true;
  }

  // Calculate retry delay with exponential backoff
  static calculateRetryDelay(attemptCount: number, baseDelay = 1000): number {
    return Math.min(baseDelay * Math.pow(2, attemptCount), 30000); // Max 30 seconds
  }

  // Create error message for UI display
  static createDisplayMessage(error: ProcessedError, showTechnical = false): string {
    let message = error.userMessage;

    if (showTechnical && process.env.NODE_ENV === 'development') {
      message += ` (${error.message})`;
    }

    if (error.retryable && error.retryAfter) {
      message += ` Please try again in ${error.retryAfter} seconds.`;
    } else if (error.retryable) {
      message += ' Please try again.';
    }

    return message;
  }

  // Log error for debugging
  static logError(error: ProcessedError, context?: any): void {
    const logData = {
      ...error,
      context,
      timestamp: new Date().toISOString()
    };

    if (error.severity === 'critical' || error.severity === 'high') {
      console.error('RTK Query Error:', logData);
    } else {
      console.warn('RTK Query Warning:', logData);
    }
  }
}

// Hook for handling RTK Query errors in components
export function useRTKErrorHandler() {
  const handleError = (
    error: FetchBaseQueryError | SerializedError | undefined,
    fallbackDataType?: keyof typeof FallbackDataGenerators
  ) => {
    const processedError = RTKErrorService.processError(error, fallbackDataType);
    RTKErrorService.logError(processedError);
    return processedError;
  };

  const getDisplayMessage = (
    error: FetchBaseQueryError | SerializedError | undefined,
    showTechnical = false
  ) => {
    const processedError = RTKErrorService.processError(error);
    return RTKErrorService.createDisplayMessage(processedError, showTechnical);
  };

  const shouldShowRetry = (error: FetchBaseQueryError | SerializedError | undefined) => {
    const processedError = RTKErrorService.processError(error);
    return processedError.retryable;
  };

  return {
    handleError,
    getDisplayMessage,
    shouldShowRetry
  };
}
