// lib/services/websocketService.ts

export interface WebSocketMessage {
  type: 'dashboard_update' | 'order_update' | 'user_update' | 'group_update' | 'system_status';
  data: any;
  timestamp: string;
}

export interface WebSocketServiceOptions {
  url?: string;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  heartbeatInterval?: number;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onMessage?: (message: WebSocketMessage) => void;
  onError?: (error: Event) => void;
}

export class WebSocketService {
  private ws: WebSocket | null = null;
  private url: string;
  private reconnectInterval: number;
  private maxReconnectAttempts: number;
  private heartbeatInterval: number;
  private reconnectAttempts = 0;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private isConnecting = false;
  private isManualClose = false;

  private listeners: {
    onConnect?: () => void;
    onDisconnect?: () => void;
    onMessage?: (message: WebSocketMessage) => void;
    onError?: (error: Event) => void;
  } = {};

  constructor(options: WebSocketServiceOptions = {}) {
    this.url = options.url || this.getWebSocketUrl();
    this.reconnectInterval = options.reconnectInterval || 5000;
    this.maxReconnectAttempts = options.maxReconnectAttempts || 10;
    this.heartbeatInterval = options.heartbeatInterval || 30000;
    
    this.listeners = {
      onConnect: options.onConnect,
      onDisconnect: options.onDisconnect,
      onMessage: options.onMessage,
      onError: options.onError,
    };
  }

  private getWebSocketUrl(): string {
    if (typeof window === 'undefined') return '';
    
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;
    return `${protocol}//${host}/api/ws`;
  }

  public connect(): void {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.CONNECTING)) {
      return;
    }

    this.isConnecting = true;
    this.isManualClose = false;

    try {
      this.ws = new WebSocket(this.url);
      this.setupEventListeners();
    } catch (error) {
      console.error('WebSocket connection error:', error);
      this.isConnecting = false;
      this.scheduleReconnect();
    }
  }

  public disconnect(): void {
    this.isManualClose = true;
    this.clearTimers();
    
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  public send(message: Partial<WebSocketMessage>): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const fullMessage: WebSocketMessage = {
        type: message.type || 'dashboard_update',
        data: message.data || {},
        timestamp: new Date().toISOString(),
        ...message
      };
      
      this.ws.send(JSON.stringify(fullMessage));
    } else {
      console.warn('WebSocket is not connected. Message not sent:', message);
    }
  }

  public getConnectionState(): 'connecting' | 'open' | 'closing' | 'closed' {
    if (!this.ws) return 'closed';
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING: return 'connecting';
      case WebSocket.OPEN: return 'open';
      case WebSocket.CLOSING: return 'closing';
      case WebSocket.CLOSED: return 'closed';
      default: return 'closed';
    }
  }

  public isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  private setupEventListeners(): void {
    if (!this.ws) return;

    this.ws.onopen = () => {
      console.log('WebSocket connected');
      this.isConnecting = false;
      this.reconnectAttempts = 0;
      this.startHeartbeat();
      this.listeners.onConnect?.();
    };

    this.ws.onclose = (event) => {
      console.log('WebSocket disconnected:', event.code, event.reason);
      this.isConnecting = false;
      this.clearTimers();
      this.listeners.onDisconnect?.();
      
      if (!this.isManualClose) {
        this.scheduleReconnect();
      }
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error);
      this.listeners.onError?.(error);
    };

    this.ws.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data);
        this.handleMessage(message);
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };
  }

  private handleMessage(message: WebSocketMessage): void {
    // Handle heartbeat responses
    if (message.type === 'heartbeat') {
      return;
    }

    this.listeners.onMessage?.(message);
  }

  private startHeartbeat(): void {
    this.clearHeartbeat();
    
    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected()) {
        this.send({ type: 'heartbeat' as any, data: { timestamp: Date.now() } });
      }
    }, this.heartbeatInterval);
  }

  private clearHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  private scheduleReconnect(): void {
    if (this.isManualClose || this.reconnectAttempts >= this.maxReconnectAttempts) {
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1), 30000);
    
    console.log(`Scheduling WebSocket reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);
    
    this.reconnectTimer = setTimeout(() => {
      this.connect();
    }, delay);
  }

  private clearTimers(): void {
    this.clearHeartbeat();
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }
}

// Singleton instance
let wsService: WebSocketService | null = null;

export function getWebSocketService(options?: WebSocketServiceOptions): WebSocketService {
  if (!wsService) {
    wsService = new WebSocketService(options);
  }
  return wsService;
}

// React hook for WebSocket
export function useWebSocket(options: WebSocketServiceOptions = {}) {
  const [connectionState, setConnectionState] = useState<'connecting' | 'open' | 'closing' | 'closed'>('closed');
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null);
  const [error, setError] = useState<Event | null>(null);

  const wsService = getWebSocketService({
    ...options,
    onConnect: () => {
      setConnectionState('open');
      setError(null);
      options.onConnect?.();
    },
    onDisconnect: () => {
      setConnectionState('closed');
      options.onDisconnect?.();
    },
    onMessage: (message) => {
      setLastMessage(message);
      options.onMessage?.(message);
    },
    onError: (error) => {
      setError(error);
      options.onError?.(error);
    }
  });

  useEffect(() => {
    wsService.connect();
    
    const updateConnectionState = () => {
      setConnectionState(wsService.getConnectionState());
    };
    
    const interval = setInterval(updateConnectionState, 1000);
    
    return () => {
      clearInterval(interval);
      wsService.disconnect();
    };
  }, []);

  return {
    connectionState,
    lastMessage,
    error,
    send: wsService.send.bind(wsService),
    isConnected: wsService.isConnected.bind(wsService),
    connect: wsService.connect.bind(wsService),
    disconnect: wsService.disconnect.bind(wsService)
  };
}

// Hook specifically for dashboard real-time updates
export function useDashboardWebSocket() {
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [notifications, setNotifications] = useState<any[]>([]);

  const { connectionState, lastMessage, send } = useWebSocket({
    onMessage: (message) => {
      switch (message.type) {
        case 'dashboard_update':
          setDashboardData(message.data);
          break;
        case 'order_update':
        case 'user_update':
        case 'group_update':
          // Trigger dashboard refresh
          send({ type: 'request_dashboard_update' as any });
          break;
        case 'system_status':
          setNotifications(prev => [...prev, {
            id: Date.now().toString(),
            type: 'info',
            message: message.data.message,
            timestamp: new Date()
          }]);
          break;
      }
    }
  });

  // Request initial dashboard data when connected
  useEffect(() => {
    if (connectionState === 'open') {
      send({ type: 'request_dashboard_update' as any });
    }
  }, [connectionState, send]);

  return {
    connectionState,
    dashboardData,
    notifications,
    requestUpdate: () => send({ type: 'request_dashboard_update' as any })
  };
}
