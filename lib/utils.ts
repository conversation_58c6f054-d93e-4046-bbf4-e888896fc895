import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function isValidEmail(email: string): boolean {
  // More comprehensive email validation that explicitly supports common domains
  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/

  // Basic format check
  if (!emailRegex.test(email)) {
    return false
  }

  // Additional checks for common issues
  const parts = email.split('@')
  if (parts.length !== 2) return false

  const [localPart, domain] = parts

  // Check local part length (before @)
  if (localPart.length === 0 || localPart.length > 64) return false

  // Check domain part length (after @)
  if (domain.length === 0 || domain.length > 253) return false

  // Ensure domain has at least one dot and valid TLD
  const domainParts = domain.split('.')
  if (domainParts.length < 2) return false

  // Check that TLD is at least 2 characters (supports .org, .com, .co.za, etc.)
  const tld = domainParts[domainParts.length - 1]
  if (tld.length < 2) return false

  return true
}

export function formatCurrency(amount: number, currency: string = 'ZAR'): string {
  return new Intl.NumberFormat('en-ZA', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount)
}
