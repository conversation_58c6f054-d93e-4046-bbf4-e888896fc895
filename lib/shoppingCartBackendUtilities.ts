// lib/shoppingCartBackendUtilities.ts
// This module is server‑only. Use "use server" to ensure it's only imported in server contexts.
"use server";

import mongoose, { Types } from "mongoose";
import { ShoppingCart, type IShoppingCart, type ICartItem } from "@/models/ShoppingCart";
import  {GroupOrder, type IGroupOrder } from "@/models/GroupOrder";
import { MemberOrder, type IMemberOrder } from "@/models/MemberOrder";
import {IProduct, Product} from "@/models/Product";
import { User } from "@/models/User";
import {
  BULK_DISCOUNT_TIERS,
  DEFAULT_MILESTONES,
  GroupOrderStatus,
  type IGroupOrderMilestone,
} from "@/types/shoppingCartConstants";
import {
  // Only import what we need
  DiscountTier,
  DiscountCalculationResult
} from "@/types/unifiedCart";

// Commented out unused types
// import type { DbShoppingCart } from "@/types/unifiedCart";
// import type { GroupOrder as GroupOrderType } from "@/types/unifiedCart";
// import type { GroupOrderItem } from "@/types/unifiedCart";
// import type { UserContribution } from "@/types/unifiedCart";




// Base interfaces without Document methods
interface IOrderItem {
  product: IProduct | Types.ObjectId;
  quantity: number;
  userId: Types.ObjectId;
  unitPrice: number;
  subtotal: number;
}

interface IUserContribution {
  userId: Types.ObjectId;
  userName: string;
  totalSpent: number;
  items: IOrderItem[];
  contributionPercentage: number;
}

// Transformed interfaces for frontend use
interface TransformedOrderItem {
  product: {
    _id: string;
    name: string;
    price: number;
    image?: string;
  } | string;
  quantity: number;
  userId: string;
  unitPrice: number;
  subtotal: number;
}

interface TransformedUserContribution {
  userId: string;
  userName: string;
  totalSpent: number;
  items: TransformedOrderItem[];
  contributionPercentage: number;
}

// Base group order interface without Document methods
export interface IBaseGroupOrder {
  _id: Types.ObjectId;
  groupId: Types.ObjectId;
  orderItems: IOrderItem[];
  userContributions: IUserContribution[];
  totalOrderValue: number;
  status: string;
  statusHistory: Array<{ status: string; timestamp: Date }>;
  milestones: Array<{
    targetAmount: number;
    isReached: boolean;
    reachedAt?: Date;
  }>;
  bulkDiscountTiers: Array<{
    threshold: number;
    discountPercentage: number;
  }>;
  orderPlacedAt: Date;
  lastUpdatedAt: Date;
  paymentStatus: string;
  groupOrderNotes?: string;
}

// Transformed group order interface for frontend
export interface TransformedGroupOrder {
  _id: string;
  groupId: string;
  orderItems: TransformedOrderItem[];
  userContributions: TransformedUserContribution[];
  totalOrderValue: number;
  status: string;
  statusHistory: Array<{ status: string; timestamp: Date }>;
  milestones: Array<{
    targetAmount: number;
    isReached: boolean;
    reachedAt?: Date;
  }>;
  bulkDiscountTiers: Array<{
    threshold: number;
    discountPercentage: number;
  }>;
  orderPlacedAt: Date;
  lastUpdatedAt: Date;
  paymentStatus: string;
  groupOrderNotes?: string;
}



// Add these interfaces if not already defined
interface IOrderItem {
  product: IProduct | Types.ObjectId;
  quantity: number;
  userId: Types.ObjectId;
  unitPrice: number;
  subtotal: number;
}

// interface IUserContribution {
//   userId: Types.ObjectId;
//   userName: string;
//   totalSpent: number;
//   items: IOrderItem[];
//   contributionPercentage: number;
// }

// Add interfaces for the transformed data
interface TransformedOrderItem {
  product: {
    _id: string;
    name: string;
    price: number;
    image?: string;
  } | string;
  quantity: number;
  userId: string;
  unitPrice: number;
  subtotal: number;
}

interface TransformedUserContribution {
  userId: string;
  userName: string;
  totalSpent: number;
  items: TransformedOrderItem[];
  contributionPercentage: number;
}



export interface CustomerInfo {
  name: string;
  email: string;
  address: string;
  city: string;
  country: string;
  postalCode: string;
}

/* CREATE A NEW SHOPPING CART */
export async function createShoppingCart(
  user: mongoose.Types.ObjectId,
  items: ICartItem[] = [],
  total: number = 0,
  groupId?: mongoose.Types.ObjectId
): Promise<IShoppingCart> {
  const newCart = new ShoppingCart({
    user,
    items,
    total,
    groupId,
    groupOrderId: undefined,
    isFinalized: false,
  });
  return newCart.save();
}

// lib/shoppingCartBackendUtilities.ts
export async function addItemToCart(
  userId: string,
  productId: string,
  quantity: number,
  groupId: string
): Promise<IShoppingCart | null> {
  if (
    !mongoose.Types.ObjectId.isValid(userId) ||
    !mongoose.Types.ObjectId.isValid(productId) ||
    !mongoose.Types.ObjectId.isValid(groupId)
  ) {
    return null;
  }
  try {
    const user = await User.findById(userId);
    const product = await Product.findById(productId);
    if (!user || !product) return null;

    let cart = await ShoppingCart.findOne({ user: userId, groupId });
    if (!cart) {
      cart = new ShoppingCart({
        user: new mongoose.Types.ObjectId(userId),
        groupId: new mongoose.Types.ObjectId(groupId),
        items: [],
        total: 0,
        groupOrderId: undefined,
        isFinalized: false,
      });
    }

    // Generate a unique _id for the cart item
    const cartItemId = new mongoose.Types.ObjectId();

    const index = cart.items.findIndex(
      (item: ICartItem) => item.product.toString() === productId
    );

    if (index >= 0) {
      // If the item already exists in the cart, set the quantity to the specified amount
      // This ensures we're setting exactly the quantity the user requested
      cart.items[index].quantity = quantity;
    } else {
      // If the item doesn't exist, add it with the specified quantity
      cart.items.push({
        _id: cartItemId.toString(), // Convert ObjectId to string
        product: new mongoose.Types.ObjectId(productId),
        quantity,
        price: product.price || 0
      });
    }

    cart.total = await calculateCartTotal(cart);
    await cart.save();

    // Populate the cart with product details before returning
    const populatedCart = await ShoppingCart.findById(cart._id)
      .populate({
        path: 'items.product',
        select: 'name price image description'  // Added description to get more product details
      })
      .populate({
        path: 'user',
        select: 'name email'
      });

    await createOrUpdateGroupOrder(userId, groupId, cart);
    return populatedCart;
  } catch (error) {
    console.error("Error adding item to cart:", error);
    return null;
  }
}

/* REMOVE AN ITEM FROM THE CART */
export async function removeItemFromCart(
  userId: string,
  productId: string,
  groupId: string
): Promise<IShoppingCart | null> {
  if (
    !mongoose.Types.ObjectId.isValid(userId) ||
    !mongoose.Types.ObjectId.isValid(productId) ||
    !mongoose.Types.ObjectId.isValid(groupId)
  ) {
    console.error("Invalid ObjectId:", { userId, productId, groupId });
    return null;
  }
  try {
    console.log(`Finding cart for user ${userId} in group ${groupId}`);
    const cart = await ShoppingCart.findOne({ user: userId, groupId });

    if (!cart) {
      console.error("Cart not found for user:", userId);
      return null;
    }

    console.log(`Found cart with ${cart.items.length} items`);
    console.log(`Removing product ${productId} from cart`);

    // Log all items before removal
    cart.items.forEach((item, index) => {
      console.log(`Item ${index}: product=${item.product.toString()}, quantity=${item.quantity}`);
    });

    // Remove the item
    const initialItemCount = cart.items.length;
    cart.items = cart.items.filter(
      (item: ICartItem) => item.product.toString() !== productId
    );

    // Check if any item was actually removed
    if (cart.items.length === initialItemCount) {
      console.error(`Product ${productId} not found in cart items`);
      // Try a different approach - maybe the product ID is stored differently
      cart.items = cart.items.filter(
        (item: ICartItem) => item.product.toString() !== new mongoose.Types.ObjectId(productId).toString()
      );

      // Check again if any item was removed
      if (cart.items.length === initialItemCount) {
        console.error(`Product ${productId} still not found after ObjectId conversion`);
        // Return the original cart without changes
        return cart;
      }
    }

    console.log(`Removed product ${productId}, ${cart.items.length} items remaining`);

    // Recalculate total and save
    cart.total = await calculateCartTotal(cart);
    await cart.save();

    // Update group order
    await createOrUpdateGroupOrder(userId, groupId, cart);

    // Populate the cart with product details before returning
    const populatedCart = await ShoppingCart.findById(cart._id)
      .populate({
        path: 'items.product',
        select: 'name price image description'
      })
      .populate({
        path: 'user',
        select: 'name email'
      });

    console.log('Populated cart after removal:', JSON.stringify(populatedCart, null, 2));
    return populatedCart;
  } catch (error) {
    console.error("Error removing item from cart:", error);
    return null;
  }
}

/* UPDATE THE QUANTITY OF A CART ITEM */
export async function updateCartItemQuantity(
  userId: string,
  productId: string,
  quantity: number,
  groupId: string
): Promise<IShoppingCart | null> {
  if (
    !mongoose.Types.ObjectId.isValid(userId) ||
    !mongoose.Types.ObjectId.isValid(productId) ||
    !mongoose.Types.ObjectId.isValid(groupId)
  ) {
    return null;
  }
  try {
    const cart = await ShoppingCart.findOne({ user: userId, groupId });
    if (!cart) return null;

    const index = cart.items.findIndex(
      (item: ICartItem) => item.product.toString() === productId
    );
    if (index === -1) return null;

    cart.items[index].quantity = quantity;
    cart.total = await calculateCartTotal(cart);
    await cart.save();
    await createOrUpdateGroupOrder(userId, groupId, cart);

    // Populate the cart with product details before returning
    const populatedCart = await ShoppingCart.findById(cart._id)
      .populate({
        path: 'items.product',
        select: 'name price image'
      })
      .populate({
        path: 'user',
        select: 'name email'
      });

    return populatedCart;
  } catch (error) {
    console.error("Error updating cart item quantity:", error);
    return null;
  }
}

/* CLEAR THE SHOPPING CART */
export async function clearShoppingCart(
  userId: string,
  groupId: string
): Promise<IShoppingCart | null> {
  if (
    !mongoose.Types.ObjectId.isValid(userId) ||
    !mongoose.Types.ObjectId.isValid(groupId)
  ) {
    return null;
  }
  try {
    const cart = await ShoppingCart.findOne({ user: userId, groupId });
    if (!cart) return null;

    cart.items = [];
    cart.total = 0;
    await cart.save();
    await createOrUpdateGroupOrder(userId, groupId, cart);

    // Populate the cart with product details before returning
    const populatedCart = await ShoppingCart.findById(cart._id)
      .populate({
        path: 'items.product',
        select: 'name price image'
      })
      .populate({
        path: 'user',
        select: 'name email'
      });

    return populatedCart;
  } catch (error) {
    console.error("Error clearing shopping cart:", error);
    return null;
  }
}

/* GET THE SHOPPING CART FOR A USER (OPTIONALLY FILTER BY GROUP) */
export async function getShoppingCartByUserId(
  userId: string,
  groupId?: string
): Promise<IShoppingCart | null> {
  console.log(`getShoppingCartByUserId - userId: ${userId}, groupId: ${groupId || 'undefined'}`);

  if (!mongoose.Types.ObjectId.isValid(userId)) {
    console.error("Invalid userId:", userId);
    return null;
  }

  const query: { user: mongoose.Types.ObjectId; groupId?: mongoose.Types.ObjectId } = {
    user: new mongoose.Types.ObjectId(userId)
  };

  if (groupId && mongoose.Types.ObjectId.isValid(groupId)) {
    query.groupId = new mongoose.Types.ObjectId(groupId);
    console.log(`getShoppingCartByUserId - Adding groupId to query: ${groupId}`);
  }

  try {
    console.log(`getShoppingCartByUserId - Searching for cart with query:`, JSON.stringify(query));

    // Find the cart and populate the product details
    const cart = await ShoppingCart.findOne(query)
      .populate({
        path: 'items.product',
        select: 'name price image description'
      })
      .populate({
        path: 'user',
        select: 'name email'
      });

    console.log(`getShoppingCartByUserId - Cart found:`, cart ? 'Yes' : 'No');

    // If no cart exists, create an empty one
    if (!cart && mongoose.Types.ObjectId.isValid(userId)) {
      console.log(`getShoppingCartByUserId - Creating new cart for user ${userId}`);

      const newCart = new ShoppingCart({
        user: new mongoose.Types.ObjectId(userId),
        items: [],
        total: 0,
        groupId: groupId && mongoose.Types.ObjectId.isValid(groupId)
          ? new mongoose.Types.ObjectId(groupId)
          : undefined,
        isFinalized: false
      });

      await newCart.save();
      console.log(`getShoppingCartByUserId - New cart created with ID: ${newCart._id}`);
      return newCart;
    }

    if (cart) {
      console.log(`getShoppingCartByUserId - Returning cart with ${cart.items.length} items`);
      // Log the first few items for debugging
      if (cart.items.length > 0) {
        console.log(`getShoppingCartByUserId - First item:`, JSON.stringify(cart.items[0], null, 2));
      }
    }

    return cart;
  } catch (error) {
    console.error("Error getting shopping cart:", error);
    return null;
  }
}

/* CALCULATE THE TOTAL COST OF THE CART */
export async function calculateCartTotal(cart: IShoppingCart): Promise<number> {
  if (!cart || !cart.items || cart.items.length === 0) return 0;
  const totals = await Promise.all(
    cart.items.map(async (item: ICartItem) => {
      const product = await Product.findById(item.product);
      return (product?.price || 0) * item.quantity;
    })
  );
  return totals.reduce((sum, val) => sum + val, 0);
}

/* CALCULATE THE TOTAL VALUE OF A GROUP ORDER */
export async function calculateGroupOrderTotal(groupOrder: IGroupOrder): Promise<number> {
  if (!groupOrder || !groupOrder.orderItems) return 0;
  const totals = await Promise.all(
    groupOrder.orderItems.map(async (item) => {
      const product = await Product.findById(item.product);
      const price = product?.price || item.unitPrice || 0;
      return price * item.quantity;
    })
  );
  return totals.reduce((sum, val) => sum + val, 0);
}

/* CREATE MEMBER ORDER AND UPDATE GROUP ORDER */
export async function createMemberOrderAndUpdateGroup(
  userId: string,
  groupId: string,
  cart: IShoppingCart,
  customerInfo: CustomerInfo,
  paymentMethod: string
): Promise<{ memberOrder: IMemberOrder | null; groupOrder: IGroupOrder | null }> {
  try {
    // First create or update the group order
    const groupOrder = await createOrUpdateGroupOrder(userId, groupId, cart, customerInfo, paymentMethod);
    if (!groupOrder) {
      return { memberOrder: null, groupOrder: null };
    }

    // Create member order
    const memberOrder = new MemberOrder({
      userId: new mongoose.Types.ObjectId(userId),
      groupId: new mongoose.Types.ObjectId(groupId),
      groupOrderId: groupOrder._id,
      items: await Promise.all(
        cart.items.map(async (item: ICartItem) => {
          const product = await Product.findById(item.product);
          const unitPrice = product?.price || 0;
          return {
            product: item.product,
            quantity: item.quantity,
            unitPrice,
            subtotal: unitPrice * item.quantity
          };
        })
      ),
      totalAmount: await calculateCartTotal(cart),
      status: 'pending',
      statusHistory: [{
        status: 'pending',
        timestamp: new Date(),
        notes: 'Order created from cart checkout'
      }],
      customerInfo,
      paymentInfo: {
        method: paymentMethod,
        status: 'pending'
      }
    });

    await memberOrder.save();

    // Update group order status if this is the first member order
    if (groupOrder.status === 'draft') {
      groupOrder.status = 'active';
      groupOrder.statusHistory.push({
        status: 'active',
        timestamp: new Date()
      });
      await groupOrder.save();
    }

    return { memberOrder, groupOrder };
  } catch (error) {
    console.error('Error creating member order and updating group:', error);
    return { memberOrder: null, groupOrder: null };
  }
}

/* CREATE OR UPDATE A GROUP ORDER BASED ON THE CURRENT CART */
export async function createOrUpdateGroupOrder(
  userId: string,
  groupId: string,
  cart: IShoppingCart,
  customerInfo?: CustomerInfo,
  paymentMethod?: string
): Promise<IGroupOrder | null> {
  try {
    const user = await User.findById(userId);
    if (!user) return null;

    // Validate groupId before using it
    if (!groupId || !mongoose.Types.ObjectId.isValid(groupId)) {
      console.log('createOrUpdateGroupOrder - Invalid groupId:', groupId);
      return null;
    }

    let groupOrder = await GroupOrder.findOne({
      groupId: new mongoose.Types.ObjectId(groupId),
    });

    if (!groupOrder) {
      groupOrder = new GroupOrder({
        groupId: new mongoose.Types.ObjectId(groupId),
        orderItems: [],
        totalOrderValue: 0,
        userContributions: [],
        status: GroupOrderStatus.DRAFT,
        milestones: DEFAULT_MILESTONES,
        bulkDiscountTiers: BULK_DISCOUNT_TIERS,
        orderPlacedAt: new Date(),
        lastUpdatedAt: new Date(),
        paymentStatus: "unpaid",
      });
    }

    const cartTotal = await calculateCartTotal(cart);
    const contribIndex = groupOrder.userContributions.findIndex(
      (contrib: IUserContribution) => contrib.userId.toString() === userId
    );
    if (contribIndex !== -1) {
      groupOrder.userContributions[contribIndex].totalSpent = cartTotal;
      groupOrder.userContributions[contribIndex].contributionPercentage =
        groupOrder.totalOrderValue ? (cartTotal / groupOrder.totalOrderValue) * 100 : 0;
    } else {
      groupOrder.userContributions.push({
        userId: new mongoose.Types.ObjectId(userId),
        userName: user.name,
        totalSpent: cartTotal,
        items: [],
        contributionPercentage: groupOrder.totalOrderValue
          ? (cartTotal / (groupOrder.totalOrderValue + cartTotal)) * 100
          : 100,
      });
    }

    const orderItemsWithPrices = await Promise.all(
      cart.items.map(async (item: ICartItem) => {
        const product = await Product.findById(item.product);
        const unitPrice = product?.price || 0;
        return {
          product: item.product,
          quantity: item.quantity,
          userId: new mongoose.Types.ObjectId(userId),
          unitPrice,
          subtotal: unitPrice * item.quantity,
        };
      })
    );
    groupOrder.orderItems = orderItemsWithPrices;
    groupOrder.totalOrderValue = await calculateGroupOrderTotal(groupOrder);

    groupOrder.milestones.forEach((milestone: IGroupOrderMilestone) => {
      if (groupOrder.totalOrderValue >= milestone.targetAmount) {
        milestone.isReached = true;
        milestone.reachedAt = new Date();
        milestone.currentAmount = groupOrder.totalOrderValue;
      } else {
        milestone.isReached = false;
        milestone.reachedAt = undefined;
      }
    });

    if (customerInfo) {
      groupOrder.groupOrderNotes = JSON.stringify(customerInfo);
    }
    if (paymentMethod) {
      groupOrder.paymentStatus = "partially_paid";
    }
    groupOrder.lastUpdatedAt = new Date();

    await groupOrder.save();
    return groupOrder;
  } catch (err) {
    console.error("Error creating/updating group order:", err instanceof Error ? err.message : String(err));
    return null;
  }
}

/* UPDATE GROUP ORDER STATUS */
export async function updateGroupOrderStatus(
  orderId: string,
  status: GroupOrderStatus,
  userId: string
): Promise<IGroupOrder | null> {
  if (!mongoose.Types.ObjectId.isValid(orderId) || !userId) {
    return null;
  }

  try {
    const groupOrder = await GroupOrder.findById(orderId);
    if (!groupOrder) return null;

    // Add entry to status history
    groupOrder.statusHistory.push({
      status,
      timestamp: new Date()
    });

    // Update the status
    groupOrder.status = status;
    groupOrder.lastUpdatedAt = new Date();

    // Special handling for certain statuses
    if (status === GroupOrderStatus.COMPLETED) {
      groupOrder.paymentStatus = "paid";
    } else if (status === GroupOrderStatus.CANCELLED) {
      groupOrder.paymentStatus = "cancelled";
    }

    await groupOrder.save();
    return groupOrder;
  } catch (error) {
    console.error("Error updating group order status:", error);
    return null;
  }
}

/* BULK DISCOUNT CALCULATION */
export async function calculateBulkDiscount(
  totalOrderValue: number,
  discountTiers: DiscountTier[]
): Promise<DiscountCalculationResult> {
  const sortedTiers = [...discountTiers].sort((a, b) => b.threshold - a.threshold);
  const applicableTier = sortedTiers.find((tier) => totalOrderValue >= tier.threshold);
  if (!applicableTier) {
    return {
      discountPercentage: 0,
      discountAmount: 0,
      finalOrderValue: totalOrderValue,
    };
  }
  const discountAmount = totalOrderValue * (applicableTier.discountPercentage / 100);
  const finalOrderValue = totalOrderValue - discountAmount;
  return {
    discountPercentage: applicableTier.discountPercentage,
    discountAmount,
    finalOrderValue,
    appliedTier: applicableTier
  };
}

/* DYNAMIC DISCOUNT TIER GENERATOR */
export async function generateDynamicDiscountTiers(
  baseThresholds: number[] = [5000, 10000, 20000],
  baseDiscounts: number[] = [10, 15, 20]
): Promise<DiscountTier[]> {
  return baseThresholds.map((threshold, index) => ({
    threshold,
    discountPercentage: baseDiscounts[index],
  }));
}

/* CONTEXT-AWARE DISCOUNT CALCULATION */
export async function calculateContextualDiscount(
  groupOrder: IGroupOrder,
  dynamicFactors?: { seasonalBonus?: number; loyaltyMultiplier?: number }
): Promise<DiscountCalculationResult & {
  dynamicFactors?: { seasonalBonus?: number; loyaltyMultiplier?: number };
}> {
  const baseDiscountTiers = await generateDynamicDiscountTiers();
  const bulkDiscountResult = await calculateBulkDiscount(groupOrder.totalOrderValue, baseDiscountTiers);
  let finalDiscountPercentage = bulkDiscountResult.discountPercentage;
  if (dynamicFactors) {
    if (dynamicFactors.seasonalBonus) {
      finalDiscountPercentage += dynamicFactors.seasonalBonus;
    }
    if (dynamicFactors.loyaltyMultiplier) {
      finalDiscountPercentage *= dynamicFactors.loyaltyMultiplier;
    }
  }
  const discountAmount = groupOrder.totalOrderValue * (finalDiscountPercentage / 100);
  const finalOrderValue = groupOrder.totalOrderValue - discountAmount;
  return {
    discountPercentage: finalDiscountPercentage,
    discountAmount,
    finalOrderValue,
    appliedTier: bulkDiscountResult.appliedTier,
    dynamicFactors,
  };
}

/* GET GROUP ORDERS BY GROUP ID OR USER ID */
export async function getGroupOrders(
  groupId?: string | null,
  userId?: string | null,
  options?: {
    limit?: number;
    skip?: number;
    lean?: boolean;
    populateProducts?: boolean;
  }
): Promise<IGroupOrder[]> {
  try {
    const query: Record<string, unknown> = {};
    const { limit = 50, skip = 0, lean = true, populateProducts = false } = options || {};

    if (groupId && mongoose.Types.ObjectId.isValid(groupId)) {
      query.groupId = new mongoose.Types.ObjectId(groupId);
    }

    if (userId && mongoose.Types.ObjectId.isValid(userId)) {
      query["userContributions.userId"] = new mongoose.Types.ObjectId(userId);
    }

    let queryBuilder = GroupOrder.find(query)
      .sort({ orderPlacedAt: -1 })
      .limit(limit)
      .skip(skip);

    // Use lean queries for better performance unless we need full documents
    if (lean) {
      queryBuilder = queryBuilder.lean();
    }

    // Only populate products if specifically requested
    if (populateProducts) {
      queryBuilder = queryBuilder.populate('orderItems.product', 'name price image');
    }

    const orders = await queryBuilder.exec();
    return orders;
  } catch (err) {
    console.error('Error fetching group orders:', err);
    throw new Error(`Error fetching group orders: ${err instanceof Error ? err.message : String(err)}`);
  }
}

/* GENERATE GROUP ORDER ANALYTICS */
export async function generateGroupOrderAnalytics(
  groupId?: string,
  startDate?: Date,
  endDate?: Date
): Promise<{
  totalOrders: number;
  averageOrderValue: number;
  totalRevenue: number;
  popularProducts: {
    productId: string;
    name: string;
    quantity: number;
  }[];
  ordersByStatus: {
    [key: string]: number;
  };
  monthlySales?: {
    month: string;
    revenue: number;
  }[];
  memberContributions?: {
    userName: string;
    totalSpent: number;
  }[];
}> {
  try {
    // Build query with proper type for MongoDB date range queries
    interface DateRangeQuery {
      $gte?: Date;
      $lte?: Date;
    }

    interface MongoQuery {
      groupId?: mongoose.Types.ObjectId;
      orderPlacedAt?: DateRangeQuery;
      [key: string]: unknown; // Allow for other query parameters with unknown type
    }

    const query: MongoQuery = {};
    if (groupId) {
      query.groupId = new mongoose.Types.ObjectId(groupId);
    }

    if (startDate || endDate) {
      query.orderPlacedAt = {};
      if (startDate) {
        query.orderPlacedAt.$gte = startDate;
      }
      if (endDate) {
        query.orderPlacedAt.$lte = endDate;
      }
    }

    // Fetch orders
    const orders = await GroupOrder.find(query)
      .populate('orderItems.product')
      .sort({ orderPlacedAt: 1 });

    if (!orders || orders.length === 0) {
      return {
        totalOrders: 0,
        averageOrderValue: 0,
        totalRevenue: 0,
        popularProducts: [],
        ordersByStatus: {},
      };
    }

    // Calculate basic metrics
    const totalOrders = orders.length;
    const totalRevenue = orders.reduce((sum, order) => sum + order.totalOrderValue, 0);
    const averageOrderValue = totalRevenue / totalOrders;

    // Count orders by status
    const ordersByStatus = orders.reduce((acc, order) => {
      acc[order.status] = (acc[order.status] || 0) + 1;
      return acc;
    }, {} as { [key: string]: number });

    // Calculate popular products
    const productMap = new Map<string, { name: string; quantity: number }>();
    orders.forEach(order => {
      order.orderItems.forEach((item: {
        product: IProduct | mongoose.Types.ObjectId | string;
        quantity: number;
      }) => {
        let productId = '';
        let productName = 'Product';

        if (item.product) {
          if (typeof item.product === 'object') {
            // Handle both IProduct and ObjectId types
            if ('name' in item.product && 'id' in item.product) {
              // It's an IProduct
              productId = item.product.id;
              productName = item.product.name;
            } else if (item.product instanceof mongoose.Types.ObjectId) {
              // It's an ObjectId
              productId = item.product.toString();
            } else {
              // Fallback for other object types
              productId = String(item.product);
            }
          } else {
            // Handle string or other primitive types
            productId = String(item.product);
          }
        }

        if (productMap.has(productId)) {
          const currentProduct = productMap.get(productId)!;
          currentProduct.quantity += item.quantity;
          productMap.set(productId, currentProduct);
        } else {
          productMap.set(productId, { name: productName, quantity: item.quantity });
        }
      });
    });

    const popularProducts = Array.from(productMap.entries())
      .map(([productId, data]) => ({
        productId,
        name: data.name,
        quantity: data.quantity
      }))
      .sort((a, b) => b.quantity - a.quantity);

    // Generate monthly sales data
    const monthlyData = new Map<string, number>();
    orders.forEach(order => {
      const date = new Date(order.orderPlacedAt);
      const monthYear = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
      const currentValue = monthlyData.get(monthYear) || 0;
      monthlyData.set(monthYear, currentValue + order.totalOrderValue);
    });

    const monthlySales = Array.from(monthlyData.entries())
      .map(([month, revenue]) => ({ month, revenue }))
      .sort((a, b) => a.month.localeCompare(b.month));

    // Calculate member contributions
    const memberMap = new Map<string, number>();
    orders.forEach(order => {
      order.userContributions.forEach((contrib: {
        userName: string;
        totalSpent: number;
      }) => {
        const userName = contrib.userName;
        const currentTotal = memberMap.get(userName) || 0;
        memberMap.set(userName, currentTotal + contrib.totalSpent);
      });
    });

    const memberContributions = Array.from(memberMap.entries())
      .map(([userName, totalSpent]) => ({ userName, totalSpent }))
      .sort((a, b) => b.totalSpent - a.totalSpent);

    return {
      totalOrders,
      averageOrderValue,
      totalRevenue,
      popularProducts,
      ordersByStatus,
      monthlySales,
      memberContributions
    };
  } catch (error) {
    console.error('Error generating group order analytics:', error);
    throw new Error('Failed to generate analytics');
  }
}
