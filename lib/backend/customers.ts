// lib/backend/customers.ts

import { connectToDatabase } from '@/lib/dbconnect';
import { User } from '@/models/User';
import { MemberOrder } from '@/models/MemberOrder';
import { StokvelGroup } from '@/models/StokvelGroup';
import { CustomerOverview, CustomerTrendData, TopCustomer, CustomerFilters, PaginatedCustomersResponse, CustomerStats } from '@/lib/redux/features/customers/customersApiSlice';

// Get customer overview statistics
export async function getCustomerOverview(): Promise<CustomerOverview> {
  try {
    await connectToDatabase();

    const now = new Date();
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());

    // Get current period stats
    const [
      totalCustomers,
      newCustomers,
      customersByStatus,
      activeCustomersResult,
      avgOrderValueResult
    ] = await Promise.all([
      User.countDocuments({ role: 'user' }),
      User.countDocuments({ 
        role: 'user',
        createdAt: { $gte: lastMonth }
      }),
      User.aggregate([
        { $match: { role: 'user' } },
        { $group: { _id: '$status', count: { $sum: 1 } } }
      ]),
      User.aggregate([
        { $match: { role: 'user', status: 'active' } },
        { $count: 'activeCustomers' }
      ]),
      MemberOrder.aggregate([
        {
          $lookup: {
            from: 'users',
            localField: 'userId',
            foreignField: '_id',
            as: 'user'
          }
        },
        { $unwind: '$user' },
        { $match: { 'user.role': 'user' } },
        { $group: { _id: null, avg: { $avg: '$totalAmount' } } }
      ])
    ]);

    // Get previous period stats for growth calculation
    const [
      prevTotalCustomers,
      prevNewCustomers,
      prevActiveCustomersResult,
      prevAvgOrderValueResult
    ] = await Promise.all([
      User.countDocuments({ 
        role: 'user',
        createdAt: { $lt: lastMonth }
      }),
      User.countDocuments({ 
        role: 'user',
        createdAt: { 
          $gte: new Date(lastMonth.getFullYear(), lastMonth.getMonth() - 1, lastMonth.getDate()),
          $lt: lastMonth
        }
      }),
      User.aggregate([
        { 
          $match: { 
            role: 'user', 
            status: 'active',
            createdAt: { $lt: lastMonth }
          } 
        },
        { $count: 'activeCustomers' }
      ]),
      MemberOrder.aggregate([
        { $match: { createdAt: { $lt: lastMonth } } },
        {
          $lookup: {
            from: 'users',
            localField: 'userId',
            foreignField: '_id',
            as: 'user'
          }
        },
        { $unwind: '$user' },
        { $match: { 'user.role': 'user' } },
        { $group: { _id: null, avg: { $avg: '$totalAmount' } } }
      ])
    ]);

    const activeCustomers = activeCustomersResult[0]?.activeCustomers || 0;
    const averageOrderValue = avgOrderValueResult[0]?.avg || 0;
    const prevActiveCustomers = prevActiveCustomersResult[0]?.activeCustomers || 0;
    const prevAverageOrderValue = prevAvgOrderValueResult[0]?.avg || 0;

    // Calculate growth percentages
    const totalCustomersGrowth = prevTotalCustomers > 0 ? ((totalCustomers - prevTotalCustomers) / prevTotalCustomers) * 100 : 0;
    const newCustomersGrowth = prevNewCustomers > 0 ? ((newCustomers - prevNewCustomers) / prevNewCustomers) * 100 : 0;
    const activeCustomersGrowth = prevActiveCustomers > 0 ? ((activeCustomers - prevActiveCustomers) / prevActiveCustomers) * 100 : 0;
    const avgOrderValueGrowth = prevAverageOrderValue > 0 ? ((averageOrderValue - prevAverageOrderValue) / prevAverageOrderValue) * 100 : 0;

    // Format customers by status
    const statusMap: Record<string, number> = {};
    customersByStatus.forEach((item: any) => {
      statusMap[item._id] = item.count;
    });

    return {
      totalCustomers,
      newCustomers,
      activeCustomers,
      averageOrderValue,
      customersByStatus: statusMap,
      totalCustomersGrowth,
      newCustomersGrowth,
      activeCustomersGrowth,
      avgOrderValueGrowth,
    };
  } catch (error) {
    console.error('Error getting customer overview:', error);
    throw new Error('Failed to get customer overview');
  }
}

// Get paginated customers with filters
export async function getAllCustomersPaginated(
  page: number = 1,
  limit: number = 20,
  filters: CustomerFilters = {}
): Promise<PaginatedCustomersResponse> {
  try {
    await connectToDatabase();

    // Build filter query
    const query: any = { role: 'user' };
    
    if (filters.status) {
      query.status = filters.status;
    }
    
    if (filters.dateFrom || filters.dateTo) {
      query.createdAt = {};
      if (filters.dateFrom) {
        query.createdAt.$gte = new Date(filters.dateFrom);
      }
      if (filters.dateTo) {
        query.createdAt.$lte = new Date(filters.dateTo);
      }
    }
    
    if (filters.search) {
      query.$or = [
        { name: { $regex: filters.search, $options: 'i' } },
        { email: { $regex: filters.search, $options: 'i' } }
      ];
    }

    const skip = (page - 1) * limit;

    const [customers, totalCount] = await Promise.all([
      User.find(query)
        .select('-password -refreshToken')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      User.countDocuments(query)
    ]);

    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return {
      customers: customers as any[],
      totalCount,
      totalPages,
      currentPage: page,
      hasNextPage,
      hasPrevPage,
    };
  } catch (error) {
    console.error('Error getting paginated customers:', error);
    throw new Error('Failed to get customers');
  }
}

// Get customers with order statistics
export async function getCustomersWithStats(
  page: number = 1,
  limit: number = 20,
  filters: CustomerFilters = {}
): Promise<CustomerStats[]> {
  try {
    await connectToDatabase();

    // Build match query
    const matchQuery: any = { role: 'user' };
    
    if (filters.status) {
      matchQuery.status = filters.status;
    }
    
    if (filters.search) {
      matchQuery.$or = [
        { name: { $regex: filters.search, $options: 'i' } },
        { email: { $regex: filters.search, $options: 'i' } }
      ];
    }

    const skip = (page - 1) * limit;

    const customersWithStats = await User.aggregate([
      { $match: matchQuery },
      {
        $lookup: {
          from: 'memberorders',
          localField: '_id',
          foreignField: 'userId',
          as: 'orders'
        }
      },
      {
        $lookup: {
          from: 'stokvelgroups',
          localField: 'groupId',
          foreignField: '_id',
          as: 'group'
        }
      },
      {
        $addFields: {
          totalOrders: { $size: '$orders' },
          totalSpent: { $sum: '$orders.totalAmount' },
          averageOrderValue: {
            $cond: {
              if: { $gt: [{ $size: '$orders' }, 0] },
              then: { $divide: [{ $sum: '$orders.totalAmount' }, { $size: '$orders' }] },
              else: 0
            }
          },
          lastOrderDate: { $max: '$orders.createdAt' },
          firstOrderDate: { $min: '$orders.createdAt' },
          groupName: { $arrayElemAt: ['$group.name', 0] }
        }
      },
      {
        $project: {
          name: 1,
          email: 1,
          status: 1,
          createdAt: 1,
          groupId: 1,
          groupName: 1,
          totalOrders: 1,
          totalSpent: 1,
          averageOrderValue: 1,
          lastOrderDate: 1,
          firstOrderDate: 1
        }
      },
      { $sort: { createdAt: -1 } },
      { $skip: skip },
      { $limit: limit }
    ]);

    return customersWithStats;
  } catch (error) {
    console.error('Error getting customers with stats:', error);
    throw new Error('Failed to get customers with stats');
  }
}

// Get customer trends data
export async function getCustomerTrends(period: string = '30d'): Promise<CustomerTrendData[]> {
  try {
    await connectToDatabase();

    let days = 30;
    if (period === '7d') days = 7;
    else if (period === '90d') days = 90;
    else if (period === '1y') days = 365;

    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const trends = await User.aggregate([
      {
        $match: {
          role: 'user',
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: {
              format: '%Y-%m-%d',
              date: '$createdAt'
            }
          },
          newCustomers: { $sum: 1 }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);

    // Get active customers and revenue data
    const revenueData = await MemberOrder.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate }
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'userId',
          foreignField: '_id',
          as: 'user'
        }
      },
      { $unwind: '$user' },
      { $match: { 'user.role': 'user' } },
      {
        $group: {
          _id: {
            $dateToString: {
              format: '%Y-%m-%d',
              date: '$createdAt'
            }
          },
          totalRevenue: { $sum: '$totalAmount' },
          activeCustomers: { $addToSet: '$userId' }
        }
      },
      {
        $addFields: {
          activeCustomers: { $size: '$activeCustomers' }
        }
      }
    ]);

    // Merge the data
    const trendMap = new Map();
    trends.forEach((trend: any) => {
      trendMap.set(trend._id, {
        date: trend._id,
        newCustomers: trend.newCustomers,
        activeCustomers: 0,
        totalRevenue: 0
      });
    });

    revenueData.forEach((data: any) => {
      const existing = trendMap.get(data._id);
      if (existing) {
        existing.activeCustomers = data.activeCustomers;
        existing.totalRevenue = data.totalRevenue;
      } else {
        trendMap.set(data._id, {
          date: data._id,
          newCustomers: 0,
          activeCustomers: data.activeCustomers,
          totalRevenue: data.totalRevenue
        });
      }
    });

    return Array.from(trendMap.values()).sort((a, b) => a.date.localeCompare(b.date));
  } catch (error) {
    console.error('Error getting customer trends:', error);
    throw new Error('Failed to get customer trends');
  }
}

// Get top customers
export async function getTopCustomers(limit: number = 10, period: string = '30d'): Promise<TopCustomer[]> {
  try {
    await connectToDatabase();

    let days = 30;
    if (period === '7d') days = 7;
    else if (period === '90d') days = 90;
    else if (period === '1y') days = 365;

    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const topCustomers = await User.aggregate([
      { $match: { role: 'user' } },
      {
        $lookup: {
          from: 'memberorders',
          let: { userId: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: { $eq: ['$userId', '$$userId'] },
                createdAt: { $gte: startDate },
                status: { $ne: 'cancelled' }
              }
            }
          ],
          as: 'orders'
        }
      },
      {
        $addFields: {
          totalOrders: { $size: '$orders' },
          totalSpent: { $sum: '$orders.totalAmount' },
          lastOrderDate: { $max: '$orders.createdAt' }
        }
      },
      {
        $match: {
          totalOrders: { $gt: 0 }
        }
      },
      {
        $project: {
          name: 1,
          email: 1,
          status: 1,
          totalOrders: 1,
          totalSpent: 1,
          lastOrderDate: 1
        }
      },
      { $sort: { totalSpent: -1 } },
      { $limit: limit }
    ]);

    return topCustomers;
  } catch (error) {
    console.error('Error getting top customers:', error);
    throw new Error('Failed to get top customers');
  }
}

// Update customer
export async function updateCustomer(customerId: string, updateData: any) {
  try {
    await connectToDatabase();

    const customer = await User.findByIdAndUpdate(
      customerId,
      { ...updateData, updatedAt: new Date() },
      { new: true }
    ).select('-password -refreshToken');

    if (!customer) {
      throw new Error('Customer not found');
    }

    return customer;
  } catch (error) {
    console.error('Error updating customer:', error);
    throw new Error('Failed to update customer');
  }
}
