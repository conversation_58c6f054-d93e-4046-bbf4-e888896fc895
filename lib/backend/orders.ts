// lib/backend/orders.ts

import { connectToDatabase } from '@/lib/dbconnect';
import { MemberOrder } from '@/models/MemberOrder';
import { GroupOrder } from '@/models/GroupOrder';
import { Product } from '@/models/Product';
import { User } from '@/models/User';
import { StokvelGroup } from '@/models/StokvelGroup';
import { OrderOverview, OrderTrendData, TopSellingProduct, OrderFilters, PaginatedOrdersResponse } from '@/lib/redux/features/orders/ordersApiSlice';

// Get order overview statistics
export async function getOrderOverview(): Promise<OrderOverview> {
  try {
    await connectToDatabase();

    const now = new Date();
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());

    // Get current period stats
    const [
      totalOrders,
      groupOrders,
      ordersByStatus,
      revenueResult,
      avgOrderResult
    ] = await Promise.all([
      MemberOrder.countDocuments(),
      GroupOrder.countDocuments(),
      MemberOrder.aggregate([
        { $group: { _id: '$status', count: { $sum: 1 } } }
      ]),
      MemberOrder.aggregate([
        { $group: { _id: null, total: { $sum: '$totalAmount' } } }
      ]),
      MemberOrder.aggregate([
        { $group: { _id: null, avg: { $avg: '$totalAmount' } } }
      ])
    ]);

    // Get previous period stats for growth calculation
    const [
      prevTotalOrders,
      prevGroupOrders,
      prevRevenueResult,
      prevAvgOrderResult
    ] = await Promise.all([
      MemberOrder.countDocuments({ createdAt: { $lt: lastMonth } }),
      GroupOrder.countDocuments({ createdAt: { $lt: lastMonth } }),
      MemberOrder.aggregate([
        { $match: { createdAt: { $lt: lastMonth } } },
        { $group: { _id: null, total: { $sum: '$totalAmount' } } }
      ]),
      MemberOrder.aggregate([
        { $match: { createdAt: { $lt: lastMonth } } },
        { $group: { _id: null, avg: { $avg: '$totalAmount' } } }
      ])
    ]);

    const totalRevenue = revenueResult[0]?.total || 0;
    const averageOrderValue = avgOrderResult[0]?.avg || 0;
    const prevTotalRevenue = prevRevenueResult[0]?.total || 0;
    const prevAverageOrderValue = prevAvgOrderResult[0]?.avg || 0;

    // Calculate growth percentages
    const ordersGrowth = prevTotalOrders > 0 ? ((totalOrders - prevTotalOrders) / prevTotalOrders) * 100 : 0;
    const groupOrdersGrowth = prevGroupOrders > 0 ? ((groupOrders - prevGroupOrders) / prevGroupOrders) * 100 : 0;
    const revenueGrowth = prevTotalRevenue > 0 ? ((totalRevenue - prevTotalRevenue) / prevTotalRevenue) * 100 : 0;
    const avgOrderGrowth = prevAverageOrderValue > 0 ? ((averageOrderValue - prevAverageOrderValue) / prevAverageOrderValue) * 100 : 0;

    // Format orders by status
    const statusMap: Record<string, number> = {};
    ordersByStatus.forEach((item: any) => {
      statusMap[item._id] = item.count;
    });

    return {
      totalOrders,
      groupOrders,
      averageOrderValue,
      totalRevenue,
      ordersByStatus: statusMap,
      revenueGrowth,
      ordersGrowth,
      avgOrderGrowth,
      groupOrdersGrowth,
    };
  } catch (error) {
    console.error('Error getting order overview:', error);
    throw new Error('Failed to get order overview');
  }
}

// Get paginated orders with filters
export async function getAllOrdersPaginated(
  page: number = 1,
  limit: number = 20,
  filters: OrderFilters = {}
): Promise<PaginatedOrdersResponse> {
  try {
    await connectToDatabase();

    // Build filter query
    const query: any = {};
    
    if (filters.status) {
      query.status = filters.status;
    }
    
    if (filters.paymentStatus) {
      query.paymentStatus = filters.paymentStatus;
    }
    
    if (filters.dateFrom || filters.dateTo) {
      query.createdAt = {};
      if (filters.dateFrom) {
        query.createdAt.$gte = new Date(filters.dateFrom);
      }
      if (filters.dateTo) {
        query.createdAt.$lte = new Date(filters.dateTo);
      }
    }
    
    if (filters.groupId) {
      query.groupId = filters.groupId;
    }
    
    if (filters.userId) {
      query.userId = filters.userId;
    }

    const skip = (page - 1) * limit;

    const [orders, totalCount] = await Promise.all([
      MemberOrder.find(query)
        .populate('userId', 'name email')
        .populate('groupId', 'name')
        .populate('items.productId', 'name price images')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      MemberOrder.countDocuments(query)
    ]);

    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return {
      orders: orders as any[],
      totalCount,
      totalPages,
      currentPage: page,
      hasNextPage,
      hasPrevPage,
    };
  } catch (error) {
    console.error('Error getting paginated orders:', error);
    throw new Error('Failed to get orders');
  }
}

// Get order trends data
export async function getOrderTrends(period: string = '30d'): Promise<OrderTrendData[]> {
  try {
    await connectToDatabase();

    let days = 30;
    if (period === '7d') days = 7;
    else if (period === '90d') days = 90;
    else if (period === '1y') days = 365;

    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const trends = await MemberOrder.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: {
              format: '%Y-%m-%d',
              date: '$createdAt'
            }
          },
          orders: { $sum: 1 },
          revenue: { $sum: '$totalAmount' }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);

    const groupOrderTrends = await GroupOrder.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: {
              format: '%Y-%m-%d',
              date: '$createdAt'
            }
          },
          groupOrders: { $sum: 1 }
        }
      }
    ]);

    // Merge the data
    const trendMap = new Map();
    trends.forEach((trend: any) => {
      trendMap.set(trend._id, {
        date: trend._id,
        orders: trend.orders,
        revenue: trend.revenue,
        groupOrders: 0
      });
    });

    groupOrderTrends.forEach((trend: any) => {
      const existing = trendMap.get(trend._id);
      if (existing) {
        existing.groupOrders = trend.groupOrders;
      } else {
        trendMap.set(trend._id, {
          date: trend._id,
          orders: 0,
          revenue: 0,
          groupOrders: trend.groupOrders
        });
      }
    });

    return Array.from(trendMap.values()).sort((a, b) => a.date.localeCompare(b.date));
  } catch (error) {
    console.error('Error getting order trends:', error);
    throw new Error('Failed to get order trends');
  }
}

// Get top selling products
export async function getTopSellingProducts(limit: number = 10, period: string = '30d'): Promise<TopSellingProduct[]> {
  try {
    await connectToDatabase();

    let days = 30;
    if (period === '7d') days = 7;
    else if (period === '90d') days = 90;
    else if (period === '1y') days = 365;

    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const topProducts = await MemberOrder.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate },
          status: { $ne: 'cancelled' }
        }
      },
      { $unwind: '$items' },
      {
        $group: {
          _id: '$items.productId',
          totalSold: { $sum: '$items.quantity' },
          totalRevenue: { $sum: { $multiply: ['$items.quantity', '$items.price'] } }
        }
      },
      {
        $lookup: {
          from: 'products',
          localField: '_id',
          foreignField: '_id',
          as: 'product'
        }
      },
      { $unwind: '$product' },
      {
        $project: {
          _id: 1,
          name: '$product.name',
          totalSold: 1,
          totalRevenue: 1,
          image: { $arrayElemAt: ['$product.images', 0] }
        }
      },
      { $sort: { totalSold: -1 } },
      { $limit: limit }
    ]);

    return topProducts;
  } catch (error) {
    console.error('Error getting top selling products:', error);
    throw new Error('Failed to get top selling products');
  }
}

// Update order status
export async function updateOrderStatus(orderId: string, status: string, notes?: string) {
  try {
    await connectToDatabase();

    const updateData: any = { 
      status,
      updatedAt: new Date()
    };

    if (notes) {
      updateData.adminNotes = notes;
    }

    const order = await MemberOrder.findByIdAndUpdate(
      orderId,
      updateData,
      { new: true }
    ).populate('userId', 'name email')
     .populate('groupId', 'name')
     .populate('items.productId', 'name price images');

    if (!order) {
      throw new Error('Order not found');
    }

    return order;
  } catch (error) {
    console.error('Error updating order status:', error);
    throw new Error('Failed to update order status');
  }
}

// Ship order
export async function shipOrder(orderId: string, trackingNumber: string, carrier: string, estimatedDelivery?: string) {
  try {
    await connectToDatabase();

    const updateData: any = {
      status: 'shipped',
      trackingNumber,
      carrier,
      shippedAt: new Date(),
      updatedAt: new Date()
    };

    if (estimatedDelivery) {
      updateData.estimatedDelivery = new Date(estimatedDelivery);
    }

    const order = await MemberOrder.findByIdAndUpdate(
      orderId,
      updateData,
      { new: true }
    ).populate('userId', 'name email')
     .populate('groupId', 'name')
     .populate('items.productId', 'name price images');

    if (!order) {
      throw new Error('Order not found');
    }

    return order;
  } catch (error) {
    console.error('Error shipping order:', error);
    throw new Error('Failed to ship order');
  }
}
