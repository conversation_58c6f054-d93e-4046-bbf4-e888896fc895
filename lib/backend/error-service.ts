// lib/backend/error-service.ts

import { NextResponse } from 'next/server';

export interface ApiError {
  message: string;
  status: number;
  code?: string;
  details?: any;
}

export class CustomApiError extends Error {
  status: number;
  code?: string;
  details?: any;

  constructor(message: string, status: number = 500, code?: string, details?: any) {
    super(message);
    this.name = 'CustomApiError';
    this.status = status;
    this.code = code;
    this.details = details;
  }
}

export function handleApiError(error: unknown, defaultMessage: string = 'An error occurred'): NextResponse {
  console.error('API Error:', error);

  // Handle custom API errors
  if (error instanceof CustomApiError) {
    return NextResponse.json(
      {
        error: error.message,
        code: error.code,
        details: error.details,
      },
      { status: error.status }
    );
  }

  // Handle MongoDB/Mongoose errors
  if (error && typeof error === 'object' && 'name' in error) {
    const mongoError = error as any;

    // Duplicate key error
    if (mongoError.code === 11000) {
      const field = Object.keys(mongoError.keyPattern || {})[0] || 'field';
      return NextResponse.json(
        {
          error: `${field.charAt(0).toUpperCase() + field.slice(1)} already exists`,
          code: 'DUPLICATE_KEY',
        },
        { status: 409 }
      );
    }

    // Validation error
    if (mongoError.name === 'ValidationError') {
      const validationErrors = Object.values(mongoError.errors || {}).map(
        (err: any) => err.message
      );
      return NextResponse.json(
        {
          error: 'Validation failed',
          code: 'VALIDATION_ERROR',
          details: validationErrors,
        },
        { status: 400 }
      );
    }

    // Cast error (invalid ObjectId)
    if (mongoError.name === 'CastError') {
      return NextResponse.json(
        {
          error: 'Invalid ID format',
          code: 'INVALID_ID',
        },
        { status: 400 }
      );
    }
  }

  // Handle standard JavaScript errors
  if (error instanceof Error) {
    // Check for specific error messages that indicate client errors
    if (error.message.includes('not found') || error.message.includes('Not found')) {
      return NextResponse.json(
        {
          error: error.message,
          code: 'NOT_FOUND',
        },
        { status: 404 }
      );
    }

    if (error.message.includes('unauthorized') || error.message.includes('Unauthorized')) {
      return NextResponse.json(
        {
          error: error.message,
          code: 'UNAUTHORIZED',
        },
        { status: 401 }
      );
    }

    if (error.message.includes('forbidden') || error.message.includes('Forbidden')) {
      return NextResponse.json(
        {
          error: error.message,
          code: 'FORBIDDEN',
        },
        { status: 403 }
      );
    }

    // Return the error message for other known errors
    return NextResponse.json(
      {
        error: error.message,
        code: 'INTERNAL_ERROR',
      },
      { status: 500 }
    );
  }

  // Fallback for unknown errors
  return NextResponse.json(
    {
      error: defaultMessage,
      code: 'UNKNOWN_ERROR',
    },
    { status: 500 }
  );
}

// Helper function to create custom API errors
export function createApiError(message: string, status: number = 500, code?: string, details?: any): CustomApiError {
  return new CustomApiError(message, status, code, details);
}

// Common error creators
export const ApiErrors = {
  notFound: (resource: string = 'Resource') => 
    createApiError(`${resource} not found`, 404, 'NOT_FOUND'),
  
  unauthorized: (message: string = 'Unauthorized access') => 
    createApiError(message, 401, 'UNAUTHORIZED'),
  
  forbidden: (message: string = 'Access forbidden') => 
    createApiError(message, 403, 'FORBIDDEN'),
  
  badRequest: (message: string = 'Bad request') => 
    createApiError(message, 400, 'BAD_REQUEST'),
  
  conflict: (message: string = 'Resource conflict') => 
    createApiError(message, 409, 'CONFLICT'),
  
  validation: (message: string = 'Validation failed', details?: any) => 
    createApiError(message, 400, 'VALIDATION_ERROR', details),
  
  internal: (message: string = 'Internal server error') => 
    createApiError(message, 500, 'INTERNAL_ERROR'),
  
  database: (message: string = 'Database error') => 
    createApiError(message, 500, 'DATABASE_ERROR'),
};

// Middleware for handling async route errors
export function withErrorHandling(handler: Function) {
  return async (...args: any[]) => {
    try {
      return await handler(...args);
    } catch (error) {
      return handleApiError(error);
    }
  };
}

// Type guard to check if error is an API error
export function isApiError(error: unknown): error is CustomApiError {
  return error instanceof CustomApiError;
}

// Helper to extract error message from various error types
export function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  if (error && typeof error === 'object' && 'message' in error) {
    return String((error as any).message);
  }
  return 'An unknown error occurred';
}

// Helper to log errors with context
export function logError(error: unknown, context?: string) {
  const message = getErrorMessage(error);
  const timestamp = new Date().toISOString();
  const logMessage = context 
    ? `[${timestamp}] ${context}: ${message}`
    : `[${timestamp}] ${message}`;
  
  console.error(logMessage);
  
  if (error instanceof Error && error.stack) {
    console.error('Stack trace:', error.stack);
  }
}
