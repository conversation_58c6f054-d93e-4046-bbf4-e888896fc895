// lib/utils/aiErrorHandler.ts
// Utility to handle AI service errors and determine appropriate responses

export interface AIErrorInfo {
  isQuotaError: boolean;
  isBillingError: boolean;
  isTemporaryError: boolean;
  shouldUseFallback: boolean;
  userMessage: string;
  logMessage: string;
}

export function analyzeAIError(error: any): AIErrorInfo {
  const errorMessage = error?.message?.toLowerCase() || '';
  const errorCode = error?.code || '';
  const errorType = error?.type || '';
  
  // OpenAI specific error patterns
  const quotaPatterns = [
    'quota',
    'insufficientquotaerror',
    'exceeded your current quota',
    'billing',
    'payment required'
  ];
  
  const billingPatterns = [
    'billing',
    'payment',
    'subscription',
    'account suspended',
    'payment method'
  ];
  
  const temporaryPatterns = [
    'rate limit',
    'too many requests',
    'server error',
    'timeout',
    'network',
    'connection'
  ];
  
  // Check for quota/billing issues
  const isQuotaError = quotaPatterns.some(pattern => 
    errorMessage.includes(pattern) || 
    errorCode.toString().includes('429') ||
    errorType.includes('insufficient_quota')
  );
  
  const isBillingError = billingPatterns.some(pattern => 
    errorMessage.includes(pattern)
  );
  
  const isTemporaryError = temporaryPatterns.some(pattern => 
    errorMessage.includes(pattern)
  );
  
  // Determine if we should use fallback
  const shouldUseFallback = isQuotaError || isBillingError;
  
  // Generate appropriate user messages
  let userMessage = '';
  let logMessage = '';
  
  if (isQuotaError) {
    userMessage = "I'm currently running in limited mode due to API quota limits. I can still help you with basic questions using my knowledge base.";
    logMessage = `OpenAI quota exceeded: ${error.message}`;
  } else if (isBillingError) {
    userMessage = "I'm temporarily running in basic mode due to billing configuration. I can still provide helpful information from my knowledge base.";
    logMessage = `OpenAI billing issue: ${error.message}`;
  } else if (isTemporaryError) {
    userMessage = "I'm experiencing temporary connectivity issues. Please try again in a moment.";
    logMessage = `Temporary AI service error: ${error.message}`;
  } else {
    userMessage = "I'm experiencing technical difficulties. Please try again or contact support if the issue persists.";
    logMessage = `Unexpected AI service error: ${error.message}`;
  }
  
  return {
    isQuotaError,
    isBillingError,
    isTemporaryError,
    shouldUseFallback,
    userMessage,
    logMessage
  };
}

export function getErrorRecoveryMessage(errorInfo: AIErrorInfo): string {
  if (errorInfo.isQuotaError || errorInfo.isBillingError) {
    return `
💡 **To restore full AI capabilities:**
• Check your OpenAI account billing settings
• Ensure you have sufficient API quota
• Consider upgrading your OpenAI plan if needed

In the meantime, I can still help you with:
• Platform navigation and features
• Product information and shopping guidance  
• Group joining and management help
• Order tracking and account assistance
`;
  }
  
  if (errorInfo.isTemporaryError) {
    return `
🔄 **This is usually temporary. You can:**
• Wait a moment and try again
• Refresh the page if needed
• Contact support if issues persist
`;
  }
  
  return `
🛠️ **If this continues:**
• Try refreshing the page
• Clear your browser cache
• Contact our support team for assistance
`;
}

// Log AI errors with appropriate level
export function logAIError(errorInfo: AIErrorInfo, context?: any) {
  const logData = {
    timestamp: new Date().toISOString(),
    error: errorInfo.logMessage,
    context: context || {},
    errorType: {
      quota: errorInfo.isQuotaError,
      billing: errorInfo.isBillingError,
      temporary: errorInfo.isTemporaryError
    }
  };
  
  if (errorInfo.isQuotaError || errorInfo.isBillingError) {
    console.warn('AI Service - Quota/Billing Issue:', logData);
  } else if (errorInfo.isTemporaryError) {
    console.warn('AI Service - Temporary Error:', logData);
  } else {
    console.error('AI Service - Unexpected Error:', logData);
  }
}

// Check if AI service is likely to be available
export async function checkAIServiceHealth(): Promise<{
  available: boolean;
  reason?: string;
  recommendation?: string;
}> {
  // Basic environment checks
  if (!process.env.OPENAI_API_KEY) {
    return {
      available: false,
      reason: 'OpenAI API key not configured',
      recommendation: 'Set OPENAI_API_KEY environment variable'
    };
  }
  
  if (process.env.OPENAI_API_KEY.length < 50) {
    return {
      available: false,
      reason: 'OpenAI API key appears invalid',
      recommendation: 'Verify OPENAI_API_KEY is correct and complete'
    };
  }
  
  // Could add more sophisticated checks here
  // like making a test API call with minimal tokens
  
  return {
    available: true
  };
}
