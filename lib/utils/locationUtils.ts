// lib/utils/locationUtils.ts

import type { LocationSelectionResult } from "@/components/modals/LocationSelectionModal";

/**
 * Utility function to format location for display
 */
export function formatLocationDisplay(
  location: LocationSelectionResult | null,
  format: "full" | "short" | "location-only" = "full"
): string {
  if (!location) return "";

  switch (format) {
    case "location-only":
      return location.locationName;
    case "short":
      return `${location.locationName}, ${location.cityName}`;
    case "full":
    default:
      return location.fullPath;
  }
}

/**
 * Utility function to check if two locations are the same
 */
export function isSameLocation(
  a: LocationSelectionResult | null,
  b: LocationSelectionResult | null
): boolean {
  if (!a || !b) return a === b;
  return a.locationId === b.locationId;
}

/**
 * Utility function to extract location IDs from LocationSelectionResult
 */
export function extractLocationIds(location: LocationSelectionResult | null) {
  if (!location) {
    return {
      provinceId: "",
      cityId: "",
      townshipId: "",
      locationId: ""
    };
  }

  return {
    provinceId: location.provinceId,
    cityId: location.cityId,
    townshipId: location.townshipId,
    locationId: location.locationId
  };
}

/**
 * Utility function to create LocationSelectionResult from individual IDs and names
 */
export function createLocationResult(data: {
  provinceId: string;
  provinceName: string;
  cityId: string;
  cityName: string;
  townshipId: string;
  townshipName: string;
  locationId: string;
  locationName: string;
}): LocationSelectionResult {
  return {
    ...data,
    fullPath: `${data.locationName}, ${data.townshipName}, ${data.cityName}, ${data.provinceName}`
  };
}

/**
 * Utility function to validate LocationSelectionResult
 */
export function isValidLocationSelection(location: LocationSelectionResult | null): boolean {
  if (!location) return false;
  
  return !!(
    location.provinceId &&
    location.provinceName &&
    location.cityId &&
    location.cityName &&
    location.townshipId &&
    location.townshipName &&
    location.locationId &&
    location.locationName
  );
}

/**
 * Utility function to get location breadcrumb array
 */
export function getLocationBreadcrumb(location: LocationSelectionResult | null): string[] {
  if (!location) return [];
  
  return [
    location.provinceName,
    location.cityName,
    location.townshipName,
    location.locationName
  ].filter(Boolean);
}

/**
 * Utility function to get short location description
 */
export function getShortLocationDescription(location: LocationSelectionResult | null): string {
  if (!location) return "";
  
  return `${location.locationName}, ${location.cityName}`;
}

/**
 * Utility function to check if location is in specific province
 */
export function isLocationInProvince(
  location: LocationSelectionResult | null,
  provinceId: string
): boolean {
  return location?.provinceId === provinceId;
}

/**
 * Utility function to check if location is in specific city
 */
export function isLocationInCity(
  location: LocationSelectionResult | null,
  cityId: string
): boolean {
  return location?.cityId === cityId;
}

/**
 * Utility function to check if location is in specific township
 */
export function isLocationInTownship(
  location: LocationSelectionResult | null,
  townshipId: string
): boolean {
  return location?.townshipId === townshipId;
}
