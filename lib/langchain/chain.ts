// lib/langchain/chain.ts
import { ChatOpenAI } from "@langchain/openai";
import { ConversationalRetrievalQAChain } from "langchain/chains";
import { MemoryVectorStore } from "langchain/vectorstores/memory";
import { OpenAIEmbeddings } from "@langchain/openai";
import { loadDocumentsFromDB } from "./documents";
import { BufferMemory } from "langchain/memory";
import { DatabaseQueryTool } from "./tools/databaseQuery";
import { PlatformKnowledgeTool } from "./tools/platformKnowledge";
import { UserContextTool } from "./tools/userContext";

export async function createChatChain(memory: BufferMemory, userContext?: {
  userId?: string;
  userRole?: string;
  currentPage?: string;
  groupId?: string;
}) {
  // Load documents and create vector store
  const documents = await loadDocumentsFromDB();
  const vectorStore = await MemoryVectorStore.fromDocuments(
    documents,
    new OpenAIEmbeddings()
  );

  const model = new ChatOpenAI({
    modelName: "gpt-4",
    temperature: 0.3,
    streaming: true,
  });

  // Create tools for database queries and platform knowledge
  const tools = [
    new DatabaseQueryTool(),
    new PlatformKnowledgeTool(),
    new UserContextTool(userContext)
  ];

  return ConversationalRetrievalQAChain.fromLLM(
    model,
    vectorStore.asRetriever(),
    {
      memory,
      returnSourceDocuments: true,
      questionGeneratorTemplate: `Given the following conversation and a follow up question, rephrase the follow up question to be a standalone question that captures all relevant context from the conversation.

Chat History:
{chat_history}
Follow Up Input: {question}
Standalone question:`,
      qaTemplate: `You are Sarah, a friendly and knowledgeable customer service representative for the Stokvel Market platform. You help users with group buying, product inquiries, order management, and platform navigation.

PERSONALITY & TONE:
- Speak naturally like a helpful human representative
- Be warm, professional, and empathetic
- Use conversational language, not robotic responses
- Show genuine interest in helping users succeed
- Acknowledge user emotions and concerns

CAPABILITIES:
- Access real-time data about products, orders, groups, and users
- Help with order tracking and status updates
- Provide product recommendations based on user preferences
- Assist with group management and member coordination
- Explain platform features and policies
- Troubleshoot common issues
- Guide users through processes step-by-step

CONTEXT AWARENESS:
- Consider the user's role (admin, member, customer)
- Reference their current page or activity when relevant
- Personalize responses based on their history and preferences
- Understand their group membership and order history

Use the following context to provide accurate, helpful responses:
{context}

Question: {question}

Remember: Always respond as Sarah, a human customer service representative. Be conversational, helpful, and show that you care about the user's success on the platform.`,
    }
  );
}

