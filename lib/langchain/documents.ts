// lib/langchain/documents.ts
import { Document } from "@langchain/core/documents";
import { StokvelGroup as StokvelGroupModel } from "@/models/StokvelGroup";
import type { StokvelGroup } from "@/types/stokvelgroup";
import { Product } from "@/models/Product";
import { ProductCategory } from "@/models/ProductCategory";
import { connectToDatabase } from "@/lib/dbconnect";

export async function loadDocumentsFromDB() {
  await connectToDatabase();
  const documents: Document[] = [];

  // Load products with categories
  const products = await Product.find()
    .populate('category', 'name')
    .lean();

  products.forEach(product => {
    documents.push(
      new Document({
        pageContent: `Product: ${product.name}. Category: ${product.category?.name || 'Uncategorized'}. Price: R${product.price}. Description: ${product.description}. Stock available: ${product.stock} units. ${product.stock > 0 ? 'In stock' : 'Out of stock'}.`,
        metadata: {
          type: 'product',
          id: product._id.toString(),
          category: product.category?.name,
          price: product.price,
          inStock: product.stock > 0
        }
      })
    );
  });

  // Load stokvel groups with enhanced information
  const groups = await StokvelGroupModel.find()
    .populate('admin', 'name')
    .populate('locationId', 'name province city township')
    .lean();

  groups.forEach(group => {
    const locationInfo = group.locationId ?
      `${group.locationId.township}, ${group.locationId.city}, ${group.locationId.province}` :
      group.geolocation || 'Location not specified';

    documents.push(
      new Document({
        pageContent: `Stokvel Group: ${group.name}. Description: ${group.description}. Location: ${locationInfo}. Members: ${group.members.length}. Admin: ${group.admin?.name || 'Unknown'}. Total sales: R${group.totalSales}. Average order value: R${group.avgOrderValue}. Bulk order threshold: R${group.bulkOrderThreshold}. Current pending orders: R${group.pendingOrderAmount}. Delivery status: ${group.deliveryStatus}.`,
        metadata: {
          type: 'group',
          id: group._id.toString(),
          memberCount: group.members.length,
          location: locationInfo,
          totalSales: group.totalSales
        }
      })
    );
  });

  // Load product categories
  const categories = await ProductCategory.find().lean();
  categories.forEach(category => {
    documents.push(
      new Document({
        pageContent: `Product Category: ${category.name}. Available for browsing and filtering products.`,
        metadata: {
          type: 'category',
          id: category._id.toString(),
          name: category.name
        }
      })
    );
  });

  // Add comprehensive platform information
  const platformInfo = [
    {
      content: "Stokvel Market is a group buying platform that enables communities to purchase products together for bulk discounts. Users can join Stokvel groups in their area to access better prices through collective purchasing power.",
      metadata: { type: 'platform_info', topic: 'overview' }
    },
    {
      content: "Bulk ordering works by groups reaching minimum order thresholds (typically R1000+) to unlock discount tiers: 5% for R1000-R2499, 10% for R2500-R4999, and 15% for R5000+. Orders are coordinated by group admins and delivered together.",
      metadata: { type: 'platform_info', topic: 'bulk_ordering' }
    },
    {
      content: "Payment methods include credit/debit cards, bank transfers, digital wallets, and cash on delivery (COD) for orders under R5000 in major cities. All payments are secure and encrypted.",
      metadata: { type: 'platform_info', topic: 'payments' }
    },
    {
      content: "Delivery is available in Cape Town, Johannesburg, Durban, Pretoria, and Port Elizabeth. Standard delivery takes 3-5 business days with free delivery on orders over R500. Group orders benefit from shared delivery costs.",
      metadata: { type: 'platform_info', topic: 'delivery' }
    },
    {
      content: "The referral program rewards users with R50 credit for each successful referral. Users can share their unique referral code to earn rewards when friends sign up and make their first purchase.",
      metadata: { type: 'platform_info', topic: 'referrals' }
    }
  ];

  platformInfo.forEach(info => {
    documents.push(
      new Document({
        pageContent: info.content,
        metadata: info.metadata
      })
    );
  });

  return documents;
}









