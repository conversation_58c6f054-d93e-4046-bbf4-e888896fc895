// lib/langchain/tools/platformKnowledge.ts
import { Tool } from "@langchain/core/tools";

export class PlatformKnowledgeTool extends Tool {
  name = "platform_knowledge";
  description = `Get information about Stokvel Market platform features, policies, and common questions.
  
  Input should be a topic or question category:
  - "how_to_join_group" - How to join a Stokvel group
  - "bulk_ordering" - How bulk ordering works
  - "payment_methods" - Available payment options
  - "delivery_info" - Delivery and shipping information
  - "group_management" - Managing Stokvel groups
  - "referral_program" - Referral system details
  - "troubleshooting" - Common issues and solutions
  - "account_management" - Account settings and profile
  - "product_categories" - Available product types
  - "order_tracking" - How to track orders
  - "returns_refunds" - Return and refund policy
  - "platform_benefits" - Benefits of group buying`;

  async _call(input: string): Promise<string> {
    const topic = input.toLowerCase().trim();
    
    const knowledgeBase = {
      "how_to_join_group": {
        title: "How to Join a Stokvel Group",
        content: `To join a Stokvel group:
        1. Browse available groups in your area
        2. Click "Join Group" on the group page
        3. Wait for admin approval (if required)
        4. Start shopping with group benefits
        
        Benefits of joining:
        - Access to bulk pricing discounts
        - Shared delivery costs
        - Community support and recommendations
        - Group buying power for better deals`,
        relatedTopics: ["group_management", "bulk_ordering"]
      },
      
      "bulk_ordering": {
        title: "How Bulk Ordering Works",
        content: `Bulk ordering on Stokvel Market:
        
        1. **Group Shopping**: Members add items to their individual carts
        2. **Bulk Thresholds**: Groups have minimum order amounts (usually R1000+)
        3. **Discount Tiers**: Higher group totals unlock better discounts
        4. **Order Coordination**: Group admin coordinates final order
        5. **Shared Benefits**: Everyone gets bulk pricing and shared delivery
        
        Discount Structure:
        - R1000-R2499: 5% discount
        - R2500-R4999: 10% discount  
        - R5000+: 15% discount
        
        Timeline: Orders typically close weekly and deliver within 3-5 days.`,
        relatedTopics: ["group_management", "delivery_info"]
      },
      
      "payment_methods": {
        title: "Payment Methods",
        content: `Available payment options:
        
        **Online Payments:**
        - Credit/Debit Cards (Visa, Mastercard)
        - Bank Transfers (EFT)
        - Digital Wallets
        
        **Cash on Delivery (COD):**
        - Available in major cities
        - R50 delivery fee applies
        - Maximum order value: R5000
        - Payment due upon delivery
        
        **Group Payment Options:**
        - Individual payments per member
        - Group admin can coordinate payments
        - Split payment options available
        
        All payments are secure and encrypted.`,
        relatedTopics: ["delivery_info", "order_tracking"]
      },
      
      "delivery_info": {
        title: "Delivery Information",
        content: `Delivery Details:
        
        **Standard Delivery:**
        - 3-5 business days
        - Free delivery on orders over R500
        - R50 delivery fee for smaller orders
        
        **Group Delivery:**
        - Shared delivery costs among group members
        - Delivery to central pickup point or individual addresses
        - Bulk delivery discounts apply
        
        **Coverage Areas:**
        - Cape Town, Johannesburg, Durban, Pretoria, Port Elizabeth
        - Expanding to more areas regularly
        
        **Tracking:**
        - SMS and email notifications
        - Real-time tracking available
        - Delivery confirmation required`,
        relatedTopics: ["order_tracking", "payment_methods"]
      },
      
      "group_management": {
        title: "Managing Stokvel Groups",
        content: `Group Management Features:
        
        **For Group Admins:**
        - Add/remove members
        - Set bulk order thresholds
        - Coordinate group orders
        - Manage delivery preferences
        - View group analytics
        - Send group announcements
        
        **For Members:**
        - View group activity
        - Chat with other members
        - Track group order progress
        - Share product recommendations
        - Participate in group decisions
        
        **Group Settings:**
        - Private or public groups
        - Approval requirements for new members
        - Custom group rules and guidelines
        - Location-based organization`,
        relatedTopics: ["how_to_join_group", "bulk_ordering"]
      },
      
      "referral_program": {
        title: "Referral Program",
        content: `Earn rewards by referring friends:
        
        **How it Works:**
        1. Share your unique referral code
        2. Friends sign up using your code
        3. Both you and your friend get rewards
        
        **Rewards:**
        - R50 credit for each successful referral
        - Bonus rewards for multiple referrals
        - Special group referral bonuses
        
        **Referral Benefits:**
        - Credits can be used for any purchase
        - No expiration on referral credits
        - Track your referrals in your account
        
        Find your referral code in your profile settings.`,
        relatedTopics: ["account_management"]
      },
      
      "troubleshooting": {
        title: "Common Issues & Solutions",
        content: `Common Problems and Solutions:
        
        **Login Issues:**
        - Reset password using "Forgot Password"
        - Clear browser cache and cookies
        - Try different browser or device
        
        **Order Problems:**
        - Check order status in "My Orders"
        - Contact group admin for group orders
        - Use order tracking for delivery updates
        
        **Payment Issues:**
        - Verify card details and billing address
        - Check bank account for sufficient funds
        - Try alternative payment method
        
        **Group Issues:**
        - Contact group admin for membership questions
        - Check group requirements and rules
        - Ensure you meet minimum order requirements
        
        Still need help? Contact our support team!`,
        relatedTopics: ["order_tracking", "account_management"]
      },
      
      "account_management": {
        title: "Account Settings & Profile",
        content: `Managing Your Account:
        
        **Profile Information:**
        - Update personal details
        - Change password
        - Add/update phone number
        - Manage email preferences
        
        **Address Book:**
        - Save multiple delivery addresses
        - Set default delivery location
        - Update billing information
        
        **Preferences:**
        - Notification settings
        - Privacy preferences
        - Communication preferences
        
        **Security:**
        - Two-factor authentication
        - Login history
        - Device management
        
        Access all settings from your profile menu.`,
        relatedTopics: ["referral_program", "troubleshooting"]
      },
      
      "order_tracking": {
        title: "Order Tracking",
        content: `Track Your Orders:
        
        **Order Status:**
        - Pending: Order received, processing
        - Confirmed: Payment confirmed, preparing
        - Shipped: Order dispatched, in transit
        - Delivered: Order completed
        
        **Tracking Methods:**
        - SMS notifications for status updates
        - Email confirmations and tracking
        - Real-time tracking in your account
        - WhatsApp updates (optional)
        
        **Group Orders:**
        - Track individual items within group order
        - Group admin receives consolidated updates
        - Member-specific delivery notifications
        
        **Delivery Confirmation:**
        - Photo confirmation upon delivery
        - Digital signature required
        - Immediate notification to customer`,
        relatedTopics: ["delivery_info", "troubleshooting"]
      },
      
      "platform_benefits": {
        title: "Benefits of Group Buying",
        content: `Why Choose Stokvel Market:
        
        **Cost Savings:**
        - Bulk pricing discounts up to 15%
        - Shared delivery costs
        - Group negotiated deals
        - Seasonal bulk promotions
        
        **Community Benefits:**
        - Connect with neighbors
        - Share product recommendations
        - Coordinate purchases
        - Build local relationships
        
        **Convenience:**
        - One-stop shopping platform
        - Coordinated deliveries
        - Group chat and communication
        - Simplified bulk ordering
        
        **Quality Assurance:**
        - Vetted suppliers and products
        - Group reviews and ratings
        - Quality guarantees
        - Easy returns and refunds
        
        Join a group today and start saving!`,
        relatedTopics: ["how_to_join_group", "bulk_ordering"]
      }
    };

    const knowledge = knowledgeBase[topic as keyof typeof knowledgeBase];
    
    if (knowledge) {
      return JSON.stringify({
        success: true,
        data: knowledge
      });
    }
    
    // If exact topic not found, search for partial matches
    const partialMatches = Object.entries(knowledgeBase).filter(([key, value]) => 
      key.includes(topic) || 
      value.title.toLowerCase().includes(topic) ||
      value.content.toLowerCase().includes(topic)
    );
    
    if (partialMatches.length > 0) {
      return JSON.stringify({
        success: true,
        data: {
          title: "Related Information",
          content: "I found some related information that might help:",
          matches: partialMatches.map(([key, value]) => ({
            topic: key,
            title: value.title,
            summary: value.content.substring(0, 200) + "..."
          }))
        }
      });
    }
    
    return JSON.stringify({
      success: false,
      message: "I don't have specific information about that topic. Let me help you with something else or connect you with a human representative."
    });
  }
}
