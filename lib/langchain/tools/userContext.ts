// lib/langchain/tools/userContext.ts
import { Tool } from "@langchain/core/tools";

interface UserContext {
  userId?: string;
  userRole?: string;
  currentPage?: string;
  groupId?: string;
}

export class UserContextTool extends Tool {
  name = "user_context";
  description = `Get contextual information about the current user's session and provide personalized assistance.
  
  This tool provides context about:
  - User's current page/location in the app
  - User's role (admin, member, customer, guest)
  - Current group context (if applicable)
  - Personalized recommendations based on context
  
  Input should be a context query like:
  - "current_context" - Get full user context
  - "page_help" - Get help for current page
  - "role_capabilities" - What user can do based on their role
  - "personalized_suggestions" - Suggestions based on user context`;

  private userContext: UserContext;

  constructor(userContext?: UserContext) {
    super();
    this.userContext = userContext || {};
  }

  async _call(input: string): Promise<string> {
    const query = input.toLowerCase().trim();
    
    switch (query) {
      case 'current_context':
        return this.getCurrentContext();
      
      case 'page_help':
        return this.getPageHelp();
      
      case 'role_capabilities':
        return this.getRoleCapabilities();
      
      case 'personalized_suggestions':
        return this.getPersonalizedSuggestions();
      
      default:
        return JSON.stringify({
          success: true,
          data: {
            message: "I can help you with context-specific information. What would you like to know about your current session?"
          }
        });
    }
  }

  private getCurrentContext(): string {
    const context = {
      isLoggedIn: !!this.userContext.userId,
      userRole: this.userContext.userRole || 'guest',
      currentPage: this.userContext.currentPage || 'unknown',
      inGroup: !!this.userContext.groupId,
      groupId: this.userContext.groupId
    };

    let contextDescription = "";
    
    if (context.isLoggedIn) {
      contextDescription = `You're logged in as a ${context.userRole}`;
      if (context.inGroup) {
        contextDescription += ` and you're currently viewing content related to your group`;
      }
    } else {
      contextDescription = "You're browsing as a guest";
    }

    if (context.currentPage && context.currentPage !== 'unknown') {
      contextDescription += `. You're currently on the ${context.currentPage} page`;
    }

    return JSON.stringify({
      success: true,
      data: {
        context,
        description: contextDescription,
        capabilities: this.getCapabilitiesForRole(context.userRole)
      }
    });
  }

  private getPageHelp(): string {
    const currentPage = this.userContext.currentPage?.toLowerCase() || 'unknown';
    
    const pageHelp = {
      'home': {
        title: "Home Page Help",
        description: "Welcome to Stokvel Market! Here you can browse featured products, find groups in your area, and discover great deals.",
        actions: [
          "Browse featured products and categories",
          "Search for products using the search bar",
          "Find and join Stokvel groups near you",
          "View current promotions and bulk deals",
          "Access your account from the top menu"
        ]
      },
      'products': {
        title: "Products Page Help",
        description: "Browse our full product catalog with advanced filtering and search options.",
        actions: [
          "Use filters to narrow down products by category, price, or availability",
          "Click on products to view detailed information",
          "Add products to your cart or wishlist",
          "Compare products and read reviews",
          "Check bulk pricing for group orders"
        ]
      },
      'groups': {
        title: "Groups Page Help",
        description: "Discover and manage Stokvel groups for collaborative shopping.",
        actions: [
          "Browse available groups in your area",
          "Join groups that match your shopping needs",
          "Create your own group if you're eligible",
          "View group activity and member discussions",
          "Participate in group orders for bulk discounts"
        ]
      },
      'cart': {
        title: "Shopping Cart Help",
        description: "Review and manage items in your shopping cart.",
        actions: [
          "Review all items and quantities",
          "Update quantities or remove items",
          "Apply discount codes or coupons",
          "Choose individual or group checkout",
          "Proceed to secure payment"
        ]
      },
      'orders': {
        title: "Orders Page Help",
        description: "Track and manage your order history.",
        actions: [
          "View all your past and current orders",
          "Track order status and delivery progress",
          "Download invoices and receipts",
          "Request returns or refunds if needed",
          "Rate and review purchased products"
        ]
      },
      'profile': {
        title: "Profile Page Help",
        description: "Manage your account settings and personal information.",
        actions: [
          "Update your personal information",
          "Manage delivery addresses",
          "View and share your referral code",
          "Adjust notification preferences",
          "Update payment methods"
        ]
      }
    };

    const help = pageHelp[currentPage as keyof typeof pageHelp];
    
    if (help) {
      return JSON.stringify({
        success: true,
        data: help
      });
    }

    return JSON.stringify({
      success: true,
      data: {
        title: "General Help",
        description: "I can help you navigate the platform and find what you're looking for.",
        suggestion: "Let me know what specific task you'd like help with, or ask me about any feature you'd like to understand better."
      }
    });
  }

  private getRoleCapabilities(): string {
    const role = this.userContext.userRole || 'guest';
    const capabilities = this.getCapabilitiesForRole(role);
    
    return JSON.stringify({
      success: true,
      data: {
        role,
        capabilities,
        description: this.getRoleDescription(role)
      }
    });
  }

  private getCapabilitiesForRole(role: string): string[] {
    const capabilities = {
      'admin': [
        "Manage all platform users and groups",
        "Access admin dashboard and analytics",
        "Manage product catalog and inventory",
        "Process orders and handle customer service",
        "Configure platform settings and policies",
        "Generate reports and insights"
      ],
      'member': [
        "Participate in group orders with bulk discounts",
        "Manage group membership and settings",
        "Coordinate group purchases as admin",
        "Access group chat and communication tools",
        "View group analytics and order history",
        "Invite new members to groups"
      ],
      'customer': [
        "Browse and purchase products individually",
        "Join existing Stokvel groups",
        "Manage personal shopping cart and wishlist",
        "Track orders and delivery status",
        "Rate and review products",
        "Refer friends and earn rewards"
      ],
      'guest': [
        "Browse products and view prices",
        "Search for products and categories",
        "View group information (limited)",
        "Create account to unlock full features",
        "Contact customer support",
        "Learn about platform benefits"
      ]
    };

    return capabilities[role as keyof typeof capabilities] || capabilities['guest'];
  }

  private getRoleDescription(role: string): string {
    const descriptions = {
      'admin': "As an admin, you have full access to manage the platform, users, and business operations.",
      'member': "As a group member, you can participate in collaborative shopping with bulk discounts and group benefits.",
      'customer': "As a customer, you can shop individually and join groups to access bulk pricing and community benefits.",
      'guest': "As a guest, you can browse products and learn about our platform. Sign up to unlock full shopping features!"
    };

    return descriptions[role as keyof typeof descriptions] || descriptions['guest'];
  }

  private getPersonalizedSuggestions(): string {
    const suggestions = [];
    const role = this.userContext.userRole || 'guest';
    const currentPage = this.userContext.currentPage?.toLowerCase();
    const inGroup = !!this.userContext.groupId;

    // Role-based suggestions
    if (role === 'guest') {
      suggestions.push({
        type: "account",
        title: "Create Your Account",
        description: "Sign up to access group buying, track orders, and earn referral rewards",
        action: "Sign up now"
      });
    }

    if (role === 'customer' && !inGroup) {
      suggestions.push({
        type: "group",
        title: "Join a Stokvel Group",
        description: "Save money with bulk pricing and connect with your community",
        action: "Find groups near you"
      });
    }

    if (role === 'member' || role === 'customer') {
      suggestions.push({
        type: "referral",
        title: "Earn Referral Rewards",
        description: "Invite friends and earn R50 credit for each successful referral",
        action: "Share your referral code"
      });
    }

    // Page-based suggestions
    if (currentPage === 'products') {
      suggestions.push({
        type: "shopping",
        title: "Check Group Pricing",
        description: "See how much you could save with bulk group orders",
        action: "View bulk discounts"
      });
    }

    if (currentPage === 'cart' && inGroup) {
      suggestions.push({
        type: "group_order",
        title: "Coordinate Group Order",
        description: "Combine your cart with group members for bulk discounts",
        action: "Start group checkout"
      });
    }

    // Default suggestions if none specific
    if (suggestions.length === 0) {
      suggestions.push({
        type: "explore",
        title: "Explore Popular Products",
        description: "Check out what other customers are buying",
        action: "View trending products"
      });
    }

    return JSON.stringify({
      success: true,
      data: {
        suggestions,
        context: {
          role,
          currentPage,
          inGroup
        }
      }
    });
  }
}
