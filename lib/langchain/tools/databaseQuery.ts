// lib/langchain/tools/databaseQuery.ts
import { Tool } from "@langchain/core/tools";
import { connectToDatabase } from "@/lib/dbconnect";
import { User } from "@/models/User";
import { Product } from "@/models/Product";
import { StokvelGroup } from "@/models/StokvelGroup";
import { GroupOrder } from "@/models/GroupOrder";
import { MemberOrder } from "@/models/MemberOrder";
import { ShoppingCart } from "@/models/ShoppingCart";
import { ProductCategory } from "@/models/ProductCategory";

export class DatabaseQueryTool extends Tool {
  name = "database_query";
  description = `Query the database for real-time information about:
  - Users and their profiles
  - Products and inventory
  - Stokvel groups and memberships
  - Orders and their status
  - Shopping carts
  - Product categories
  
  Input should be a JSON string with 'type' and 'params' fields.
  Example: {"type": "user_orders", "params": {"userId": "123"}}
  
  Available query types:
  - user_profile: Get user information
  - user_orders: Get user's order history
  - product_search: Search products by name or category
  - product_details: Get specific product information
  - group_info: Get group details and members
  - group_orders: Get group order history
  - order_status: Get specific order status
  - cart_contents: Get user's shopping cart
  - popular_products: Get trending/popular products
  - group_members: Get group member list`;

  async _call(input: string): Promise<string> {
    try {
      await connectToDatabase();
      
      const query = JSON.parse(input);
      const { type, params } = query;

      switch (type) {
        case 'user_profile':
          return await this.getUserProfile(params.userId);
        
        case 'user_orders':
          return await this.getUserOrders(params.userId);
        
        case 'product_search':
          return await this.searchProducts(params.query, params.category);
        
        case 'product_details':
          return await this.getProductDetails(params.productId);
        
        case 'group_info':
          return await this.getGroupInfo(params.groupId);
        
        case 'group_orders':
          return await this.getGroupOrders(params.groupId);
        
        case 'order_status':
          return await this.getOrderStatus(params.orderId);
        
        case 'cart_contents':
          return await this.getCartContents(params.userId);
        
        case 'popular_products':
          return await this.getPopularProducts(params.limit || 10);
        
        case 'group_members':
          return await this.getGroupMembers(params.groupId);
        
        default:
          return JSON.stringify({ error: "Unknown query type" });
      }
    } catch (error) {
      console.error('Database query error:', error);
      return JSON.stringify({ error: "Failed to execute database query" });
    }
  }

  private async getUserProfile(userId: string): Promise<string> {
    try {
      const user = await User.findById(userId)
        .select('-password -refreshTokenHash')
        .populate('stokvelGroups', 'name description')
        .lean();
      
      if (!user) {
        return JSON.stringify({ error: "User not found" });
      }

      return JSON.stringify({
        success: true,
        data: {
          name: user.name,
          email: user.email,
          phone: user.phone,
          role: user.role,
          referralCode: user.referralCode,
          groupCount: user.stokvelGroups?.length || 0,
          groups: user.stokvelGroups,
          memberSince: user.createdAt
        }
      });
    } catch (error) {
      return JSON.stringify({ error: "Failed to fetch user profile" });
    }
  }

  private async getUserOrders(userId: string): Promise<string> {
    try {
      const orders = await MemberOrder.find({ userId })
        .populate('items.product', 'name price image')
        .populate('groupId', 'name')
        .sort({ createdAt: -1 })
        .limit(20)
        .lean();

      return JSON.stringify({
        success: true,
        data: orders.map(order => ({
          orderNumber: order.orderNumber,
          status: order.status,
          totalAmount: order.totalAmount,
          groupName: order.groupId?.name,
          itemCount: order.items.length,
          createdAt: order.createdAt,
          paymentStatus: order.paymentInfo.status
        }))
      });
    } catch (error) {
      return JSON.stringify({ error: "Failed to fetch user orders" });
    }
  }

  private async searchProducts(query?: string, category?: string): Promise<string> {
    try {
      let filter: any = {};
      
      if (query) {
        filter.$or = [
          { name: { $regex: query, $options: 'i' } },
          { description: { $regex: query, $options: 'i' } }
        ];
      }
      
      if (category) {
        const categoryDoc = await ProductCategory.findOne({ name: { $regex: category, $options: 'i' } });
        if (categoryDoc) {
          filter.category = categoryDoc._id;
        }
      }

      const products = await Product.find(filter)
        .populate('category', 'name')
        .limit(20)
        .lean();

      return JSON.stringify({
        success: true,
        data: products.map(product => ({
          id: product._id,
          name: product.name,
          price: product.price,
          stock: product.stock,
          category: product.category?.name,
          image: product.image,
          description: product.description.substring(0, 200)
        }))
      });
    } catch (error) {
      return JSON.stringify({ error: "Failed to search products" });
    }
  }

  private async getProductDetails(productId: string): Promise<string> {
    try {
      const product = await Product.findById(productId)
        .populate('category', 'name')
        .lean();

      if (!product) {
        return JSON.stringify({ error: "Product not found" });
      }

      return JSON.stringify({
        success: true,
        data: {
          id: product._id,
          name: product.name,
          description: product.description,
          price: product.price,
          stock: product.stock,
          category: product.category?.name,
          image: product.image,
          available: product.stock > 0
        }
      });
    } catch (error) {
      return JSON.stringify({ error: "Failed to fetch product details" });
    }
  }

  private async getGroupInfo(groupId: string): Promise<string> {
    try {
      const group = await StokvelGroup.findById(groupId)
        .populate('members', 'name email')
        .populate('admin', 'name email')
        .lean();

      if (!group) {
        return JSON.stringify({ error: "Group not found" });
      }

      return JSON.stringify({
        success: true,
        data: {
          name: group.name,
          description: group.description,
          memberCount: group.members?.length || 0,
          adminName: group.admin?.name,
          totalSales: group.totalSales,
          avgOrderValue: group.avgOrderValue,
          activeOrders: group.activeOrders,
          bulkOrderThreshold: group.bulkOrderThreshold,
          pendingOrderAmount: group.pendingOrderAmount,
          deliveryStatus: group.deliveryStatus,
          createdAt: group.createdAt
        }
      });
    } catch (error) {
      return JSON.stringify({ error: "Failed to fetch group information" });
    }
  }

  private async getGroupOrders(groupId: string): Promise<string> {
    try {
      const orders = await GroupOrder.find({ groupId })
        .populate('orderItems.product', 'name price')
        .sort({ createdAt: -1 })
        .limit(10)
        .lean();

      return JSON.stringify({
        success: true,
        data: orders.map(order => ({
          id: order._id,
          status: order.status,
          totalOrderValue: order.totalOrderValue,
          itemCount: order.orderItems.length,
          paymentStatus: order.paymentStatus,
          estimatedDeliveryDate: order.estimatedDeliveryDate,
          createdAt: order.createdAt
        }))
      });
    } catch (error) {
      return JSON.stringify({ error: "Failed to fetch group orders" });
    }
  }

  private async getOrderStatus(orderId: string): Promise<string> {
    try {
      const order = await MemberOrder.findById(orderId)
        .populate('items.product', 'name')
        .populate('groupId', 'name')
        .lean();

      if (!order) {
        return JSON.stringify({ error: "Order not found" });
      }

      return JSON.stringify({
        success: true,
        data: {
          orderNumber: order.orderNumber,
          status: order.status,
          paymentStatus: order.paymentInfo.status,
          totalAmount: order.totalAmount,
          groupName: order.groupId?.name,
          trackingNumber: order.shippingInfo?.trackingNumber,
          estimatedDelivery: order.shippingInfo?.estimatedDelivery,
          createdAt: order.createdAt,
          statusHistory: order.statusHistory
        }
      });
    } catch (error) {
      return JSON.stringify({ error: "Failed to fetch order status" });
    }
  }

  private async getCartContents(userId: string): Promise<string> {
    try {
      const cart = await ShoppingCart.findOne({ user: userId })
        .populate('items.product', 'name price image stock')
        .populate('groupId', 'name')
        .lean();

      if (!cart) {
        return JSON.stringify({ success: true, data: { items: [], total: 0 } });
      }

      return JSON.stringify({
        success: true,
        data: {
          items: cart.items.map(item => ({
            productName: item.product?.name,
            quantity: item.quantity,
            price: item.price,
            subtotal: (item.price || 0) * item.quantity,
            inStock: (item.product?.stock || 0) > 0
          })),
          total: cart.total,
          groupName: cart.groupId?.name,
          itemCount: cart.items.length
        }
      });
    } catch (error) {
      return JSON.stringify({ error: "Failed to fetch cart contents" });
    }
  }

  private async getPopularProducts(limit: number): Promise<string> {
    try {
      // Get products with highest stock turnover or most recent orders
      const products = await Product.find({ stock: { $gt: 0 } })
        .populate('category', 'name')
        .sort({ createdAt: -1 })
        .limit(limit)
        .lean();

      return JSON.stringify({
        success: true,
        data: products.map(product => ({
          id: product._id,
          name: product.name,
          price: product.price,
          category: product.category?.name,
          image: product.image,
          stock: product.stock
        }))
      });
    } catch (error) {
      return JSON.stringify({ error: "Failed to fetch popular products" });
    }
  }

  private async getGroupMembers(groupId: string): Promise<string> {
    try {
      const group = await StokvelGroup.findById(groupId)
        .populate('members', 'name email phone')
        .populate('admin', 'name email')
        .lean();

      if (!group) {
        return JSON.stringify({ error: "Group not found" });
      }

      return JSON.stringify({
        success: true,
        data: {
          admin: {
            name: group.admin?.name,
            email: group.admin?.email,
            role: 'admin'
          },
          members: group.members?.map(member => ({
            name: member.name,
            email: member.email,
            role: 'member'
          })) || [],
          totalMembers: (group.members?.length || 0) + 1
        }
      });
    } catch (error) {
      return JSON.stringify({ error: "Failed to fetch group members" });
    }
  }
}
