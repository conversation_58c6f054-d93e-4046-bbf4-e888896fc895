# AI-Powered Chat System for Stokvel Market

## Overview

The AI-powered chat system transforms the existing chatbox into an intelligent customer service assistant named "<PERSON>" that can:

- Access real-time database information
- Provide personalized responses based on user context
- Help with orders, products, groups, and platform navigation
- Offer human-like conversational experience
- Suggest relevant actions and provide contextual help

## Key Features

### 🤖 Intelligent AI Assistant
- **Human-like personality**: <PERSON> acts as a friendly customer service representative
- **Context awareness**: Understands user role, current page, and session context
- **Database integration**: Real-time access to products, orders, groups, and user data
- **Streaming responses**: Real-time response generation for natural conversation flow

### 💬 Enhanced Chat Interface
- **Professional appearance**: Sarah's avatar and name for human-like interaction
- **Typing indicators**: Shows when <PERSON> is responding
- **Quick actions**: Contextual buttons for common tasks
- **Message timestamps**: Professional chat experience
- **Copy/feedback options**: Users can copy messages and provide feedback

### 🔍 Smart Capabilities
- **Product search**: Find products by name, category, or description
- **Order tracking**: Check order status and delivery information
- **Group management**: View group information and member details
- **Account assistance**: Help with profile, settings, and account features
- **Platform guidance**: Explain features, policies, and how-to information

## Technical Architecture

### Core Components

1. **Enhanced AI Chain** (`lib/langchain/chain.ts`)
   - Uses GPT-4 for better reasoning and responses
   - Includes custom tools for database queries
   - Context-aware prompt engineering
   - Streaming response support

2. **Database Query Tool** (`lib/langchain/tools/databaseQuery.ts`)
   - Real-time database access
   - Supports user profiles, products, orders, groups
   - Secure data handling with proper error management

3. **Platform Knowledge Tool** (`lib/langchain/tools/platformKnowledge.ts`)
   - Comprehensive knowledge base about platform features
   - FAQ responses and policy information
   - Step-by-step guidance for common tasks

4. **User Context Tool** (`lib/langchain/tools/userContext.ts`)
   - Personalized responses based on user role and current page
   - Context-aware suggestions and capabilities
   - Role-based feature explanations

5. **AI Chat Service** (`lib/services/aiChatService.ts`)
   - High-level service for common chat operations
   - Quick actions generation
   - Contextual help and suggestions

### API Endpoints

- **`/api/ai/ask`**: Main chat endpoint with streaming responses
- **`/api/ai/chat-actions`**: Quick actions and contextual help

### Enhanced UI Components

- **ChatBot** (`components/chat/ChatBot.tsx`): Floating chat button
- **ChatWindow** (`components/chat/ChatWindow.tsx`): Enhanced chat interface with Sarah's personality

## Setup and Configuration

### Environment Variables Required

```env
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key

# MongoDB Configuration
MONGODB_URL=your_mongodb_connection_string

# Authentication (if using JWT)
JWT_SECRET=your_jwt_secret
```

### Dependencies

The system uses the following key dependencies:
- `@langchain/openai`: OpenAI integration
- `@langchain/community`: MongoDB chat history
- `langchain`: Core LangChain functionality
- `mongodb`: Database operations
- `uuid`: Session ID generation

## Usage Examples

### Basic Chat Interaction
```typescript
// User asks: "What products do you have?"
// Sarah responds with product categories and search options
// Provides quick action buttons for browsing
```

### Order Tracking
```typescript
// User asks: "Where is my order?"
// Sarah accesses user's order history from database
// Provides detailed status and tracking information
```

### Group Management
```typescript
// User asks: "How do I join a group?"
// Sarah explains the process step-by-step
// Offers to find groups in user's area
```

## Customization Options

### Personality Customization
Edit the system prompt in `lib/langchain/chain.ts` to adjust Sarah's personality:
- Tone and communication style
- Level of formality
- Specific knowledge areas to emphasize

### Knowledge Base Updates
Add new information to `lib/langchain/tools/platformKnowledge.ts`:
- New FAQ entries
- Policy updates
- Feature explanations

### Database Queries
Extend `lib/langchain/tools/databaseQuery.ts` to add new query types:
- Additional data sources
- Complex aggregations
- Custom business logic

## Performance Considerations

### Caching
- Chat memory is stored in MongoDB for session persistence
- Vector embeddings are cached for faster retrieval
- Database queries include appropriate indexes

### Rate Limiting
Consider implementing rate limiting for:
- API endpoint calls
- Database queries per session
- OpenAI API usage

### Error Handling
- Graceful degradation when AI services are unavailable
- Fallback responses for database connection issues
- User-friendly error messages

## Security Features

### Data Protection
- User authentication verification for personalized data
- Secure database queries with proper sanitization
- No sensitive information in chat logs

### Access Control
- Role-based responses (admin, member, customer, guest)
- Restricted access to sensitive operations
- Audit trail for chat interactions

## Monitoring and Analytics

### Recommended Metrics
- Chat session duration and engagement
- Most common user queries and intents
- Success rate of AI responses
- User satisfaction feedback

### Logging
- Chat interactions for improvement
- Error rates and types
- Performance metrics

## Future Enhancements

### Planned Features
1. **Voice Integration**: Voice-to-text and text-to-voice capabilities
2. **Multi-language Support**: Localization for different languages
3. **Advanced Analytics**: User behavior insights and recommendations
4. **Integration Expansion**: Connect with more business systems
5. **Proactive Assistance**: Contextual suggestions based on user behavior

### Scalability Improvements
- Redis caching for high-traffic scenarios
- Load balancing for AI processing
- Database optimization for large datasets

## Troubleshooting

### Common Issues

1. **AI Not Responding**
   - Check OpenAI API key and quota
   - Verify MongoDB connection
   - Review error logs

2. **Database Queries Failing**
   - Check database connection string
   - Verify model imports and schemas
   - Review query syntax

3. **Context Not Working**
   - Verify JWT token handling
   - Check user authentication flow
   - Review context extraction logic

### Debug Mode
Enable detailed logging by setting environment variables:
```env
DEBUG=langchain:*
NODE_ENV=development
```

## Support and Maintenance

### Regular Updates
- Monitor OpenAI model updates and pricing
- Update knowledge base with new platform features
- Review and improve AI responses based on user feedback

### Performance Monitoring
- Track response times and accuracy
- Monitor database query performance
- Analyze user satisfaction metrics

This AI chat system provides a foundation for intelligent customer service that can be continuously improved and expanded based on user needs and business requirements.
